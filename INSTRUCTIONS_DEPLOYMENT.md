# Instructions pour déployer la solution de panier et favoris

Ce document contient les instructions pour déployer la solution de panier et favoris sur le site en production.

## Résumé du problème

Les fonctionnalités d'ajout au panier et de favoris ne fonctionnent pas correctement sur le site en production. Nous avons créé une solution simplifiée qui utilise Zustand pour stocker les données localement, sans dépendre de Supabase.

## Fichiers à déployer

1. **Stores Zustand**
   - `lib/store/favorites-store.ts`
   - `lib/store/cart-store.ts`

2. **Hooks simplifiés**
   - `hooks/use-simple-favorites.tsx`
   - `hooks/use-simple-cart.tsx`

3. **Composants UI**
   - `components/simple-product-card.tsx`
   - `components/mini-cart.tsx`
   - `components/favorites-menu.tsx`
   - `components/simple-header.tsx`

4. **Pages de test**
   - `app/test-cart/page.tsx`
   - `app/test-cart/layout.tsx`

## Étapes de déploiement

1. **Copier les fichiers**
   - Copiez tous les fichiers mentionnés ci-dessus dans votre projet.

2. **Intégrer les composants dans votre site**
   - Remplacez les composants existants par les nouveaux composants simplifiés.
   - Par exemple, remplacez `ProductCard` par `SimpleProductCard` dans vos pages de produits.

3. **Tester en local**
   - Testez la solution en local pour vous assurer que tout fonctionne correctement.

4. **Déployer sur GitHub**
   - Committez et poussez les modifications vers GitHub.
   - Vercel déploiera automatiquement les modifications.

## Modifications à apporter

### 1. Remplacer les composants dans les pages de produits

Remplacez :
```jsx
import ProductCard from "@/components/product-card"
```

Par :
```jsx
import SimpleProductCard from "@/components/simple-product-card"
```

Et remplacez toutes les instances de `<ProductCard ... />` par `<SimpleProductCard ... />`.

### 2. Remplacer le header

Remplacez :
```jsx
import Header from "@/components/header"
```

Par :
```jsx
import SimpleHeader from "@/components/simple-header"
```

Et remplacez toutes les instances de `<Header />` par `<SimpleHeader />`.

### 3. Mettre à jour les imports dans les pages qui utilisent le panier ou les favoris

Remplacez :
```jsx
import { useCart } from "@/hooks/use-cart"
import { useFavorites } from "@/hooks/use-favorites"
```

Par :
```jsx
import { useSimpleCart } from "@/hooks/use-simple-cart"
import { useSimpleFavorites } from "@/hooks/use-simple-favorites"
```

Et mettez à jour les appels de fonctions en conséquence.

## Remarques importantes

- Cette solution utilise le stockage local (localStorage) pour persister les données du panier et des favoris.
- Les données sont perdues si l'utilisateur efface son cache ou utilise un autre navigateur.
- Pour une solution plus robuste à long terme, il faudrait synchroniser ces données avec Supabase.

## Test après déploiement

Après le déploiement, testez les fonctionnalités suivantes :

1. Ajouter des produits au panier
2. Ajouter des produits aux favoris
3. Voir le panier et les favoris dans le header
4. Supprimer des produits du panier et des favoris

Si tout fonctionne correctement, la solution est déployée avec succès.
