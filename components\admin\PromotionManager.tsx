"use client"

import { useState, useRef } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { CalendarIcon, Trash2, Upload, Plus, Edit, Gift, Check, X } from "lucide-react"

// Types pour les promotions
interface Promotion {
  id: string
  title: string
  description: string
  imageUrl: string
  discount: number
  code: string
  startDate: string
  endDate: string
  isActive: boolean
}

// Données d'exemple pour les promotions
const samplePromotions: Promotion[] = [
  {
    id: "mothers-day-2025",
    title: "Fête des Mères 2025",
    description: "Offrez une coque personnalisée à votre maman pour la fête des mères ! Profitez de 20% de réduction sur toutes nos coques personnalisées.",
    imageUrl: "/images/placeholder.svg?height=400&width=600",
    discount: 20,
    code: "MAMAN2025",
    startDate: "2025-05-15",
    endDate: "2025-06-04",
    isActive: true
  },
  {
    id: "summer-sale",
    title: "Soldes d'Été",
    description: "Préparez votre téléphone pour l'été avec nos coques résistantes à l'eau. 15% de réduction sur la collection été.",
    imageUrl: "/images/placeholder.svg?height=400&width=600",
    discount: 15,
    code: "ETE2025",
    startDate: "2025-06-21",
    endDate: "2025-07-21",
    isActive: true
  }
]

export default function PromotionManager() {
  const [promotions, setPromotions] = useState<Promotion[]>(samplePromotions)
  const [editingPromotion, setEditingPromotion] = useState<Promotion | null>(null)

  // États pour le formulaire
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [discount, setDiscount] = useState("")
  const [code, setCode] = useState("")
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  const [isActive, setIsActive] = useState(true)
  const [previewImage, setPreviewImage] = useState<string | null>(null)

  const fileInputRef = useRef<HTMLInputElement>(null)

  // Fonction pour gérer l'upload d'image
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        setPreviewImage(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Fonction pour éditer une promotion
  const handleEditPromotion = (promo: Promotion) => {
    setEditingPromotion(promo)
    setTitle(promo.title)
    setDescription(promo.description)
    setDiscount(promo.discount.toString())
    setCode(promo.code)
    setStartDate(new Date(promo.startDate))
    setEndDate(new Date(promo.endDate))
    setIsActive(promo.isActive)
    setPreviewImage(promo.imageUrl)
  }

  // Fonction pour supprimer une promotion
  const handleDeletePromotion = (id: string) => {
    if (confirm("Êtes-vous sûr de vouloir supprimer cette promotion ?")) {
      setPromotions(promotions.filter(promo => promo.id !== id))
    }
  }

  // Fonction pour réinitialiser le formulaire
  const resetForm = () => {
    setEditingPromotion(null)
    setTitle("")
    setDescription("")
    setDiscount("")
    setCode("")
    setStartDate(undefined)
    setEndDate(undefined)
    setIsActive(true)
    setPreviewImage(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  // Fonction pour sauvegarder une promotion
  const handleSavePromotion = () => {
    if (!title || !description || !discount || !code || !startDate || !endDate || !previewImage) {
      alert("Veuillez remplir tous les champs")
      return
    }

    const newPromo: Promotion = {
      id: editingPromotion ? editingPromotion.id : `promo-${Date.now()}`,
      title,
      description,
      imageUrl: previewImage,
      discount: parseInt(discount),
      code,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      isActive
    }

    if (editingPromotion) {
      // Mise à jour d'une promotion existante
      setPromotions(promotions.map(promo =>
        promo.id === editingPromotion.id ? newPromo : promo
      ))
    } else {
      // Ajout d'une nouvelle promotion
      setPromotions([...promotions, newPromo])
    }

    resetForm()
  }

  // Fonction pour annuler l'édition
  const handleCancelEdit = () => {
    resetForm()
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{editingPromotion ? "Modifier une promotion" : "Ajouter une nouvelle promotion"}</CardTitle>
          <CardDescription>
            Créez et gérez les promotions qui seront affichées sur le site
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Titre de la promotion</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="ex: Fête des Mères 2025"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="discount">Réduction (%)</Label>
              <Input
                id="discount"
                type="number"
                value={discount}
                onChange={(e) => setDiscount(e.target.value)}
                placeholder="ex: 20"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Décrivez votre promotion en quelques phrases"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="code">Code promo</Label>
            <Input
              id="code"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              placeholder="ex: MAMAN2025"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Date de début</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP", { locale: fr }) : "Sélectionner une date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">Date de fin</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP", { locale: fr }) : "Sélectionner une date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={isActive}
              onCheckedChange={setIsActive}
            />
            <Label htmlFor="isActive">Promotion active</Label>
          </div>

          <div className="space-y-2">
            <Label htmlFor="image">Image de la promotion</Label>
            <div className="flex flex-col space-y-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="flex items-center gap-2"
                >
                  <Upload size={16} /> Télécharger une image
                </Button>
                <Input
                  id="image"
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleFileUpload}
                />
              </div>

              {previewImage && (
                <div className="relative w-full max-w-md">
                  <img
                    src={previewImage}
                    alt="Aperçu de l'image"
                    className="w-full h-auto rounded-md border border-gray-200 border-solid"
                  />
                </div>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleCancelEdit}>
            Annuler
          </Button>
          <Button onClick={handleSavePromotion}>
            {editingPromotion ? "Mettre à jour" : "Ajouter"} la promotion
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Promotions existantes</CardTitle>
          <CardDescription>
            Liste de toutes les promotions disponibles sur le site
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Titre</TableHead>
                <TableHead>Code</TableHead>
                <TableHead>Réduction</TableHead>
                <TableHead>Période</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {promotions.map((promo) => (
                <TableRow key={promo.id}>
                  <TableCell className="font-medium">{promo.title}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className="flex items-center w-fit">
                      <Gift className="h-3 w-3 mr-1" />
                      {promo.code}
                    </Badge>
                  </TableCell>
                  <TableCell>{promo.discount}%</TableCell>
                  <TableCell>
                    <span className="text-xs text-gray-500">
                      Du {new Date(promo.startDate).toLocaleDateString('fr-FR')} au {new Date(promo.endDate).toLocaleDateString('fr-FR')}
                    </span>
                  </TableCell>
                  <TableCell>
                    {promo.isActive ? (
                      <Badge className="bg-green-500">
                        <Check className="h-3 w-3 mr-1" /> Active
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-gray-500">
                        <X className="h-3 w-3 mr-1" /> Inactive
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditPromotion(promo)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-red-500"
                        onClick={() => handleDeletePromotion(promo.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}