const fs = require('fs');
const path = require('path');

// Fonction pour vérifier si un fichier existe
function fileExists(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    return fs.existsSync(fullPath);
  } catch (error) {
    return false;
  }
}

// Fonction pour extraire les chemins d'images d'un fichier
function extractImagePaths(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Recherche des chemins d'images dans les imports et les attributs src
    const regex = /(?:src|image|imageUrl|thumbnail):\s*["']([^"']*\.(?:png|jpg|jpeg|svg|gif|webp))["']/g;
    const matches = [];
    let match;
    
    while ((match = regex.exec(content)) !== null) {
      matches.push(match[1]);
    }
    
    return matches;
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier ${filePath}:`, error);
    return [];
  }
}

// Fonction pour parcourir récursivement un répertoire
function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    isDirectory ? walkDir(dirPath, callback) : callback(path.join(dir, f));
  });
}

// Fonction principale
function checkImages() {
  const imagePaths = [];
  const missingImages = [];
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire app
  walkDir('app', (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const paths = extractImagePaths(filePath);
      paths.forEach(p => {
        if (!p.startsWith('http') && !p.includes('placeholder')) {
          imagePaths.push({
            path: p,
            source: filePath
          });
        }
      });
    }
  });
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire components
  walkDir('components', (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const paths = extractImagePaths(filePath);
      paths.forEach(p => {
        if (!p.startsWith('http') && !p.includes('placeholder')) {
          imagePaths.push({
            path: p,
            source: filePath
          });
        }
      });
    }
  });
  
  // Vérifier si les images existent
  imagePaths.forEach(item => {
    const { path: imagePath, source } = item;
    
    // Normaliser le chemin pour qu'il commence par public si ce n'est pas le cas
    let normalizedPath = imagePath;
    if (imagePath.startsWith('/')) {
      normalizedPath = `public${imagePath}`;
    }
    
    if (!fileExists(normalizedPath)) {
      missingImages.push({
        path: imagePath,
        normalizedPath,
        source
      });
    }
  });
  
  // Afficher les résultats
  console.log(`Total des chemins d'images trouvés: ${imagePaths.length}`);
  
  if (missingImages.length > 0) {
    console.log(`\nImages manquantes (${missingImages.length}):`);
    missingImages.forEach(item => {
      console.log(`- ${item.path} (référencé dans ${item.source})`);
    });
  } else {
    console.log('\nToutes les images référencées existent!');
  }
}

// Exécuter la fonction principale
checkImages();
