"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Heart } from "lucide-react"
import { useFavoritesStore } from "@/lib/store/favorites-store"
import SimpleProductCard from "@/components/simple-product-card"

export default function FavoritesPage() {
  const { favorites } = useFavoritesStore()

  return (
    <main className="container mx-auto px-4 py-8">
      <div className="bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-2">Mes Favoris</h1>
        <p className="text-lg opacity-90">Retrouvez ici tous vos produits favoris.</p>
      </div>

      {favorites.length === 0 ? (
        <div className="text-center py-12">
          <Heart className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-xl font-medium mb-2">Aucun favori pour le moment</h3>
          <p className="text-muted-foreground mb-6">Ajoutez des produits à vos favoris pour les retrouver facilement ici.</p>
          <Link href="/products">
            <Button>Voir les produits</Button>
          </Link>
        </div>
      ) : (
        <div>
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-2xl font-bold">{favorites.length} produit{favorites.length > 1 ? 's' : ''} en favoris</h2>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {favorites.map((product) => (
              <SimpleProductCard
                key={product.id}
                product={{
                  id: product.id,
                  name: product.name,
                  price: product.price,
                  image: product.image_url,
                  rating: 0,
                  reviews: 0,
                  isNew: false,
                  isBestseller: false,
                  category: "",
                  type: "Accessoire",
                  colors: [],
                  collections: []
                }}
                viewMode="grid"
              />
            ))}
          </div>
        </div>
      )}
    </main>
  )
}