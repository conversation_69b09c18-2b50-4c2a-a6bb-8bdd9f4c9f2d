# Guide des dossiers d'images pour HCP Design CI

Ce guide explique la structure des dossiers d'images du site HCP Design CI et comment les utiliser correctement.

## Structure générale des dossiers

Les images du site sont organisées dans des dossiers selon la structure suivante :

```
public/
  images/
    ├── promos/                  # Images pour la page Promotions
    │   ├── banniere/            # Bannières de la page Promotions
    │   ├── nouveaux-clients/    # Images pour les offres nouveaux clients
    │   ├── packs/               # Images pour les packs promotionnels
    │   └── saisonniere/         # Images pour les promotions saisonnières
    │
    ├── gifts/                   # Images pour la page Cadeaux & Gadgets
    │   ├── anniversary-pack/    # Pack Anniversaire Classique
    │   ├── wedding-pack/        # Pack Mariage Premium
    │   ├── dinner-pack/         # Pack Dîner VIP
    │   ├── corporate-pack/      # Pack Entreprise
    │   ├── baby-pack/           # Pack Baby Shower
    │   └── graduation-pack/     # Pack Remise de Diplôme
    │
    ├── products/                # Images pour la page Tous les produits
    │   ├── mugs/                # Tasses
    │   │   ├── classic-mug/     # Tasse Personnalisable Classique
    │   │   ├── magic-mug/       # Tasse Magique Thermosensible
    │   │   └── isothermal-mug/  # Tasse Isotherme Inox
    │   │
    │   ├── tshirts/             # T-shirts
    │   │   ├── cotton-tshirt/   # T-shirt Personnalisable Coton
    │   │   ├── premium-tshirt/  # T-shirt Premium Impression Totale
    │   │   └── polo-shirt/      # Polo Brodé Personnalisable
    │   │
    │   ├── mousepads/           # Tapis de souris
    │   │   ├── standard-mousepad/    # Tapis de Souris Standard
    │   │   ├── xxl-gamer-mousepad/   # Tapis de Souris XXL Gamer
    │   │   └── ergonomic-mousepad/   # Tapis de Souris avec Repose-Poignet
    │   │
    │   ├── cushions/            # Coussins
    │   │   ├── decorative-cushion/   # Coussin Décoratif Personnalisable
    │   │   ├── photo-cushion/        # Coussin Photo Recto-Verso
    │   │   └── xxl-cushion/          # Coussin de Sol XXL
    │   │
    │   └── keychains/           # Porte-clés
    │       ├── photo-keychain/       # Porte-clé Photo Personnalisable
    │       ├── metal-keychain/       # Porte-clé Métal Gravé
    │       └── multifunction-keychain/ # Porte-clé Multifonction
    │
    └── ...                      # Autres dossiers d'images
```

## Règles de nommage des fichiers

Pour que les images s'affichent correctement sur le site, veuillez suivre ces règles de nommage :

1. **Nom du fichier** : Le nom du fichier doit correspondre au nom du dossier dans lequel il se trouve.
   - Exemple : Dans le dossier `gifts/anniversary-pack/`, le fichier doit s'appeler `anniversary-pack.jpg` ou `anniversary-pack.png`.

2. **Format des fichiers** : Utilisez des fichiers PNG ou JPG/JPEG (pas de SVG).

3. **Dimensions recommandées** :
   - Bannières : 1920 x 600 pixels
   - Images de produits : 800 x 800 pixels
   - Vignettes : 400 x 400 pixels

## Comment ajouter ou remplacer des images

### Pour la page Cadeaux & Gadgets

1. Accédez au dossier `public/images/gifts/`
2. Choisissez le pack que vous souhaitez modifier (ex: `anniversary-pack/`)
3. Placez votre image dans ce dossier en la nommant comme le dossier (ex: `anniversary-pack.jpg`)

### Pour la page Tous les produits

1. Accédez au dossier `public/images/products/`
2. Choisissez la catégorie de produit (ex: `mugs/`)
3. Choisissez le produit spécifique (ex: `classic-mug/`)
4. Placez votre image dans ce dossier en la nommant comme le dossier (ex: `classic-mug.png`)

### Pour la page Promotions

1. Accédez au dossier `public/images/promos/`
2. Choisissez le type de promotion (ex: `banniere/`)
3. Placez votre image dans ce dossier en la nommant comme le dossier (ex: `banniere.jpg`)

## Remarques importantes

- **Double structure** : Pour assurer la compatibilité avec le code existant, certaines images sont placées à la fois dans le dossier parent et dans un sous-dossier. Par exemple, l'image `anniversary-pack.jpg` est présente à la fois dans `gifts/` et dans `gifts/anniversary-pack/`.

- **Formats d'image** : Privilégiez les formats PNG pour les images avec transparence et JPG pour les photos.

- **Taille des fichiers** : Optimisez vos images pour le web pour réduire leur taille et améliorer les performances du site.

- **Noms de dossiers** : Ne modifiez pas les noms des dossiers, car ils sont référencés dans le code.

## Résolution des problèmes courants

### Les images ne s'affichent pas

1. **Vérifiez le nom du fichier** : Assurez-vous que le nom du fichier correspond exactement au nom du dossier.
2. **Vérifiez le format** : Utilisez uniquement des fichiers PNG ou JPG/JPEG.
3. **Vérifiez le chemin** : Assurez-vous que l'image est placée dans le bon dossier.
4. **Vérifiez les permissions** : Assurez-vous que les fichiers ont les bonnes permissions.

### Les images s'affichent localement mais pas en production

1. **Vérifiez que les images sont bien committées** : Assurez-vous que les images sont bien ajoutées au dépôt Git.
2. **Vérifiez que les images sont bien poussées** : Assurez-vous que les images sont bien poussées vers le dépôt distant.
3. **Vérifiez la casse des noms de fichiers** : Les serveurs Linux sont sensibles à la casse, contrairement à Windows.

## Conclusion

En suivant ces instructions, vous devriez être en mesure de gérer efficacement les images du site HCP Design CI. Si vous rencontrez des problèmes, n'hésitez pas à consulter ce guide ou à demander de l'aide.
