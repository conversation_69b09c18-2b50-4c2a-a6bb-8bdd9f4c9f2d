import Link from "next/link"
import { Smartphone, Mail, Phone, Facebook, Instagram, Twitter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <div className="flex items-center mb-4">
              <Smartphone className="h-6 w-6 text-purple-400 mr-2" />
              <span className="font-bold text-xl">HCP-DESIGN CI</span>
            </div>
            <p className="text-gray-400 mb-4">
              Créez des coques de téléphone uniques qui reflètent votre personnalité.
            </p>
            <div className="flex space-x-4">
              <Link href="https://www.facebook.com/HabillagesetCoquesPersonnalises/" target="_blank" rel="noopener noreferrer">
                <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
                  <Facebook className="h-5 w-5" />
                  <span className="sr-only">Facebook</span>
                </Button>
              </Link>
              <Link href="https://www.instagram.com/designshcp/" target="_blank" rel="noopener noreferrer">
                <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
                  <Instagram className="h-5 w-5" />
                  <span className="sr-only">Instagram</span>
                </Button>
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Liens Rapides</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/customize" className="text-gray-400 hover:text-white">
                  Personnaliser
                </Link>
              </li>
              <li>
                <Link href="/models" className="text-gray-400 hover:text-white">
                  Modèles compatibles
                </Link>
              </li>
              <li>
                <Link href="/customize" className="text-gray-400 hover:text-white">
                  Personnaliser
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-400 hover:text-white">
                  À propos de nous
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-400 hover:text-white">
                  Blog
                </Link>
              </li>
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Service Client</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/contact" className="text-gray-400 hover:text-white">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-gray-400 hover:text-white">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/shipping" className="text-gray-400 hover:text-white">
                  Livraison
                </Link>
              </li>
              <li>
                <Link href="/returns" className="text-gray-400 hover:text-white">
                  Retours
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-400 hover:text-white">
                  Conditions générales
                </Link>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Newsletter</h3>
            <p className="text-gray-400 mb-4">Inscrivez-vous pour recevoir nos offres et nouveautés.</p>
            <div className="flex space-x-2">
              <Input placeholder="Votre email" className="bg-gray-800 border-gray-700 text-white" />
              <Button className="bg-purple-600 hover:bg-purple-700">S'inscrire</Button>
            </div>
            <div className="mt-6">
              <div className="flex items-center text-gray-400 mb-2">
                <Mail className="h-4 w-4 mr-2" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center text-gray-400">
                <Phone className="h-4 w-4 mr-2" />
                <span>+225 07 09 49 58 49</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400 text-sm">
          <p>© {new Date().getFullYear()} HCP-DESIGN CI. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
  )
}
