"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

export default function Verify2FAPage() {
  const [code, setCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const redirectPath = new URLSearchParams(window.location.search).get("from") || "/hcp-admin-panel";

  const verify2FA = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Vérifier le code 2FA (à remplacer par une vérification réelle)
      const validCode = process.env.NEXT_PUBLIC_ADMIN_2FA_CODE || "123456";
      
      if (code === validCode) {
        // Définir un cookie pour indiquer que 2FA est vérifié
        document.cookie = "hcp-2fa-verified=true; path=/; max-age=3600; secure; samesite=strict";
        toast.success("Vérification réussie");
        router.push(redirectPath);
      } else {
        toast.error("Code invalide");
      }
    } catch (error) {
      toast.error("Erreur lors de la vérification");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8 rounded-lg border bg-card p-6 shadow-lg">
        <div className="space-y-2 text-center">
          <h1 className="text-2xl font-bold">Vérification supplémentaire requise</h1>
          <p className="text-sm text-muted-foreground">
            Veuillez entrer le code de vérification pour accéder à cette section
          </p>
        </div>
        <form onSubmit={verify2FA} className="space-y-4">
          <div className="space-y-2">
            <Input
              id="code"
              placeholder="Code de vérification"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              required
            />
          </div>
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Vérification..." : "Vérifier"}
          </Button>
        </form>
      </div>
    </div>
  );
}