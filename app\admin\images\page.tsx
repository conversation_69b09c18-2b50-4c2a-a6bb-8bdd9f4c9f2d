"use client";

import { useState, useEffect } from "react";
import { AdminPageLayout } from "@/components/admin/admin-page-layout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Search, Plus, Edit, Trash2, Upload, Image as ImageIcon } from "lucide-react";
import { AdaptiveImage } from "@/components/ui/adaptive-image";
import { ImageService } from "@/services/image-service";
import { supabase } from "@/lib/supabase";

// Types pour les images
interface SiteImage {
  id: string;
  name: string;
  path: string;
  category: string;
  description: string;
  width: number;
  height: number;
  alt: string;
  createdAt: string;
  updatedAt: string;
}

// Catégories d'images
const imageCategories = [
  "banners",
  "logos",
  "phone-cases",
  "phone-models",
  "products",
  "designs",
  "team",
  "testimonials",
  "gallery",
  "gospel",
  "icons",
];

// Sous-catégories pour les designs
const designSubcategories = [
  "abstract",
  "nature",
  "animals",
  "religious",
  "gospel",
];

// Sous-catégories pour les produits
const productSubcategories = [
  "mugs",
  "tshirts",
  "mousepads",
  "cushions",
  "keychains",
  "notebooks",
  "pens",
  "banners",
  "bottles",
  "gospel-products",
];

export default function ImagesPage() {
  const [images, setImages] = useState<SiteImage[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingImage, setEditingImage] = useState<SiteImage | null>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [uploadFile, setUploadFile] = useState<File | null>(null);

  // État pour le formulaire d'ajout/modification
  const [formData, setFormData] = useState({
    id: "",
    name: "",
    path: "",
    category: "banners",
    description: "",
    width: 0,
    height: 0,
    alt: "",
    createdAt: new Date().toISOString().split('T')[0],
    updatedAt: new Date().toISOString().split('T')[0],
  });

  // Ajout : gestion du texte de la bannière
  const [bannerFields, setBannerFields] = useState<{ [key: string]: string }>({
    banner_title: "",
    banner_subtitle: "",
    banner_button: "",
  });
  const [bannerLoading, setBannerLoading] = useState(true);

  // Ajout d'autres contenus éditables (exemple : texte d'accueil, pied de page)
  const [siteContentFields, setSiteContentFields] = useState<{ [key: string]: string }>({
    homepage_intro: "",
    footer_text: ""
  });
  const [siteContentLoading, setSiteContentLoading] = useState(true);

  useEffect(() => {
    (async () => {
      const data = await ImageService.getAllImages();
      setImages(data || []);
    })();

    // Charger le texte de la bannière
    const fetchBannerContent = async () => {
      const { data } = await supabase
        .from("site_content")
        .select("*")
        .in("key", ["banner_title", "banner_subtitle", "banner_button"]);
      if (data) {
        const obj: any = {};
        data.forEach((row: any) => (obj[row.key] = row.value));
        setBannerFields((prev) => ({ ...prev, ...obj }));
      }
      setBannerLoading(false);
    };
    fetchBannerContent();

    // Charger d'autres contenus éditables
    const fetchSiteContent = async () => {
      const { data } = await supabase
        .from("site_content")
        .select("*")
        .in("key", ["homepage_intro", "footer_text"]);
      if (data) {
        const obj: any = {};
        data.forEach((row: any) => (obj[row.key] = row.value));
        setSiteContentFields((prev) => ({ ...prev, ...obj }));
      }
      setSiteContentLoading(false);
    };
    fetchSiteContent();
  }, []);

  // Filtrer les images en fonction de la recherche et de la catégorie
  const filteredImages = images.filter((image) => {
    const matchesSearch = image.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          image.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === "all" || image.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Gérer le changement de champ dans le formulaire
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleBannerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBannerFields({ ...bannerFields, [e.target.name]: e.target.value });
  };

  const handleBannerSave = async () => {
    setBannerLoading(true);
    for (const key of Object.keys(bannerFields)) {
      await supabase.from("site_content").upsert({ key, value: bannerFields[key] });
    }
    setBannerLoading(false);
    alert("Bannière mise à jour !");
  };

  const handleSiteContentChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setSiteContentFields({ ...siteContentFields, [e.target.name]: e.target.value });
  };

  const handleSiteContentSave = async () => {
    setSiteContentLoading(true);
    for (const key of Object.keys(siteContentFields)) {
      await supabase.from("site_content").upsert({ key, value: siteContentFields[key] as string });
    }
    setSiteContentLoading(false);
    alert("Contenu du site mis à jour !");
  };

  // Gérer le téléchargement d'image
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadFile(file);
      const reader = new FileReader();
      reader.onload = (event) => {
        const base64Data = event.target?.result as string;
        setPreviewImage(base64Data);
        setFormData({
          ...formData,
          name: file.name,
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // Ouvrir le dialogue d'ajout d'image
  const handleAddImage = () => {
    setEditingImage(null);
    setFormData({
      id: "",
      name: "",
      path: "",
      category: "banners",
      description: "",
      width: 0,
      height: 0,
      alt: "",
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
    });
    setPreviewImage(null);
    setUploadFile(null);
    setIsAddDialogOpen(true);
  };

  // Ouvrir le dialogue de modification d'image
  const handleEditImage = (image: SiteImage) => {
    setEditingImage(image);
    setFormData({
      ...image,
    });
    setPreviewImage(image.path);
    setUploadFile(null);
    setIsAddDialogOpen(true);
  };

  // Supprimer une image
  const handleDeleteImage = async (id: string) => {
    if (confirm("Êtes-vous sûr de vouloir supprimer cette image ?")) {
      await ImageService.deleteImage(id);
      const data = await ImageService.getAllImages();
      setImages(data || []);
    }
  };

  // Soumettre le formulaire
  const handleSubmitForm = async () => {
    let imageUrl = formData.path;
    if (uploadFile) {
      try {
        imageUrl = await ImageService.uploadImageToStorage(uploadFile, formData.category);
      } catch (err) {
        alert("Erreur lors de l'upload de l'image dans Supabase Storage");
        return;
      }
    }
    if (editingImage) {
      await ImageService.updateImage(editingImage.id, {
        ...formData,
        path: imageUrl,
        updatedAt: new Date().toISOString().split('T')[0]
      });
    } else {
      await ImageService.addImage({
        ...formData,
        path: imageUrl,
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0],
      });
    }
    const data = await ImageService.getAllImages();
    setImages(data || []);
    setIsAddDialogOpen(false);
    setUploadFile(null);
  };

  return (
    <AdminPageLayout>
      <div className="container mx-auto py-6">
        {/* Bloc édition texte bannière */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Texte de la bannière d’accueil</CardTitle>
          </CardHeader>
          <CardContent>
            <Input
              name="banner_title"
              value={bannerFields.banner_title}
              onChange={handleBannerChange}
              placeholder="Titre de la bannière"
              className="mb-2"
            />
            <Input
              name="banner_subtitle"
              value={bannerFields.banner_subtitle}
              onChange={handleBannerChange}
              placeholder="Sous-titre"
              className="mb-2"
            />
            <Input
              name="banner_button"
              value={bannerFields.banner_button}
              onChange={handleBannerChange}
              placeholder="Texte du bouton"
              className="mb-2"
            />
            <Button onClick={handleBannerSave} disabled={bannerLoading}>
              {bannerLoading ? "Enregistrement..." : "Enregistrer"}
            </Button>
          </CardContent>
        </Card>

        {/* Bloc édition autres contenus du site */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Autres contenus éditables du site</CardTitle>
          </CardHeader>
          <CardContent>
            <Input
              name="homepage_intro"
              value={siteContentFields.homepage_intro}
              onChange={handleSiteContentChange}
              placeholder="Texte d'introduction de la page d'accueil"
              className="mb-2"
            />
            <Input
              name="footer_text"
              value={siteContentFields.footer_text}
              onChange={handleSiteContentChange}
              placeholder="Texte du pied de page"
              className="mb-2"
            />
            <Button onClick={handleSiteContentSave} disabled={siteContentLoading}>
              {siteContentLoading ? "Enregistrement..." : "Enregistrer"}
            </Button>
          </CardContent>
        </Card>

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Gestion des images</h1>
          <Button onClick={handleAddImage}>
            <Plus className="mr-2 h-4 w-4" /> Ajouter une image
          </Button>
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Filtres</CardTitle>
              <CardDescription>Recherchez et filtrez les images par catégorie</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Rechercher une image..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Toutes les catégories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les catégories</SelectItem>
                    {imageCategories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="grid" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="grid">Grille</TabsTrigger>
              <TabsTrigger value="list">Liste</TabsTrigger>
            </TabsList>

            <TabsContent value="grid">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {filteredImages.map((image) => (
                  <Card key={image.id} className="overflow-hidden">
                    <div className="relative aspect-square bg-gray-100">
                      <AdaptiveImage
                        src={image.path}
                        alt={image.alt}
                        width={200}
                        height={200}
                        style={{ objectFit: "cover" }}
                      />
                    </div>
                    <CardContent className="p-3">
                      <p className="text-sm font-medium truncate">{image.name}</p>
                      <p className="text-xs text-gray-500 truncate">{image.category}</p>
                      <div className="flex justify-between mt-2">
                        <Button variant="ghost" size="icon" onClick={() => handleEditImage(image)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleDeleteImage(image.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="list">
              <Card>
                <CardContent className="p-0">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3">Image</th>
                        <th className="text-left p-3">Nom</th>
                        <th className="text-left p-3">Catégorie</th>
                        <th className="text-left p-3">Dimensions</th>
                        <th className="text-left p-3">Description</th>
                        <th className="text-left p-3">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredImages.map((image) => (
                        <tr key={image.id} className="border-b">
                          <td className="p-3">
                            <div className="relative w-16 h-16 bg-gray-100">
                              <AdaptiveImage
                                src={image.path}
                                alt={image.alt}
                                width={200}
                                height={200}
                                style={{ objectFit: "cover" }}
                              />
                            </div>
                          </td>
                          <td className="p-3">{image.name}</td>
                          <td className="p-3">{image.category}</td>
                          <td className="p-3">{image.width} x {image.height}</td>
                          <td className="p-3">{image.description}</td>
                          <td className="p-3">
                            <div className="flex space-x-2">
                              <Button variant="ghost" size="icon" onClick={() => handleEditImage(image)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon" onClick={() => handleDeleteImage(image.id)}>
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Dialogue d'ajout/modification d'image */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{editingImage ? "Modifier l'image" : "Ajouter une image"}</DialogTitle>
              <DialogDescription>
                {editingImage
                  ? "Modifiez les détails de l'image sélectionnée."
                  : "Téléchargez une nouvelle image et remplissez les détails."}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="col-span-2">
                  <Label htmlFor="image-upload">Image</Label>
                  <div className="mt-1 flex items-center">
                    <Label
                      htmlFor="image-upload"
                      className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-md cursor-pointer bg-gray-50 hover:bg-gray-100"
                    >
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="h-8 w-8 text-gray-400 mb-2" />
                        <p className="text-sm text-gray-500">Cliquez pour télécharger</p>
                      </div>
                      <input
                        id="image-upload"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleImageUpload}
                        aria-label="Télécharger une image"
                        title="Télécharger une image"
                      />
                    </Label>
                  </div>
                </div>

                {previewImage && (
                  <div className="col-span-2 flex justify-center p-2 bg-gray-50 rounded-md">
                    <div className="relative h-40 w-40">
                      <AdaptiveImage
                        src={previewImage}
                        alt="Aperçu de l'image"
                        width={200}
                        height={200}
                        style={{ objectFit: "contain" }}
                      />
                    </div>
                  </div>
                )}

                <div className="col-span-2">
                  <Label htmlFor="name">Nom du fichier</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="nom-du-fichier.jpg"
                  />
                </div>

                <div>
                  <Label htmlFor="category">Catégorie</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData({ ...formData, category: value })}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="Sélectionner une catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      {imageCategories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="subcategory">Sous-catégorie</Label>
                  <Select
                    value={
                      formData.path.includes('/designs/')
                        ? formData.path.split('/')[3]
                        : formData.path.includes('/products/')
                          ? formData.path.split('/')[3]
                          : ''
                    }
                    onValueChange={(value) => {
                      if (formData.category === 'designs') {
                        setFormData({
                          ...formData,
                          path: `/images/designs/${value}/${formData.name}`,
                        });
                      } else if (formData.category === 'products') {
                        setFormData({
                          ...formData,
                          path: `/images/products/${value}/${formData.name}`,
                        });
                      }
                    }}
                    disabled={formData.category !== 'designs' && formData.category !== 'products'}
                  >
                    <SelectTrigger id="subcategory">
                      <SelectValue placeholder="Sélectionner une sous-catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      {formData.category === 'designs' && designSubcategories.map((subcategory) => (
                        <SelectItem key={subcategory} value={subcategory}>
                          {subcategory.charAt(0).toUpperCase() + subcategory.slice(1)}
                        </SelectItem>
                      ))}
                      {formData.category === 'products' && productSubcategories.map((subcategory) => (
                        <SelectItem key={subcategory} value={subcategory}>
                          {subcategory === 'mugs' ? 'Tasses' :
                           subcategory === 'tshirts' ? 'T-shirts' :
                           subcategory === 'mousepads' ? 'Tapis de souris' :
                           subcategory === 'cushions' ? 'Coussins' :
                           subcategory === 'keychains' ? 'Porte-clés' :
                           subcategory.charAt(0).toUpperCase() + subcategory.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="width">Largeur (px)</Label>
                  <Input
                    id="width"
                    name="width"
                    type="number"
                    value={formData.width}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <Label htmlFor="height">Hauteur (px)</Label>
                  <Input
                    id="height"
                    name="height"
                    type="number"
                    value={formData.height}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="col-span-2">
                  <Label htmlFor="alt">Texte alternatif</Label>
                  <Input
                    id="alt"
                    name="alt"
                    value={formData.alt}
                    onChange={handleInputChange}
                    placeholder="Description pour l'accessibilité"
                  />
                </div>

                <div className="col-span-2">
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Description de l'image"
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Annuler
              </Button>
              <Button onClick={handleSubmitForm}>
                {editingImage ? "Mettre à jour" : "Ajouter"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminPageLayout>
  );
}

