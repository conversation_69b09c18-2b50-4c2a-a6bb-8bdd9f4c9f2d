/**
 * Fonctions de gestion des favoris pour Supabase
 */

import { supabase } from './supabase';

// API pour les favoris
export const favoritesApi = {
  add: async (userId: string, productId: string) => {
    const { data, error } = await supabase
      .from('favorites')
      .insert({
        user_id: userId,
        product_id: productId,
      })
      .select();
    return { data, error };
  },
  
  remove: async (userId: string, productId: string) => {
    const { data, error } = await supabase
      .from('favorites')
      .delete()
      .eq('user_id', userId)
      .eq('product_id', productId);
    return { data, error };
  },
  
  getByUser: async (userId: string) => {
    const { data, error } = await supabase
      .from('favorites')
      .select('*, product:products(*)')
      .eq('user_id', userId);
    return { data, error };
  },
  
  getCount: async (userId: string) => {
    const { count, error } = await supabase
      .from('favorites')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);
    return { count, error };
  },
  
  check: async (userId: string, productId: string) => {
    const { data, error } = await supabase
      .from('favorites')
      .select('*')
      .eq('user_id', userId)
      .eq('product_id', productId)
      .single();
    return { exists: !!data, error };
  },
};

// Exporter l'API
export default favoritesApi;
