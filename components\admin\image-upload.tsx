"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, X, Image as ImageIcon, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";

interface ImageUploadProps {
  bucketName: string;
  folderPath?: string;
  onUploadComplete: (url: string) => void;
  className?: string;
}

export function ImageUpload({
  bucketName,
  folderPath = "",
  onUploadComplete,
  className = "",
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Vérifier le type de fichier
    if (!file.type.startsWith("image/")) {
      toast.error("Veuillez sélectionner une image valide");
      return;
    }

    // Vérifier la taille du fichier (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("L'image ne doit pas dépasser 5MB");
      return;
    }

    // Créer un aperçu
    const reader = new FileReader();
    reader.onload = (event) => {
      setPreview(event.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const uploadFile = async () => {
    const file = fileInputRef.current?.files?.[0];
    if (!file) {
      toast.error("Veuillez sélectionner une image");
      return;
    }

    setIsUploading(true);

    try {
      // Générer un nom de fichier unique
      const fileExt = file.name.split(".").pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = folderPath ? `${folderPath}/${fileName}` : fileName;

      // Uploader le fichier
      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: false,
        });

      if (error) {
        throw error;
      }

      // Obtenir l'URL publique
      const { data: urlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      // Appeler le callback avec l'URL
      onUploadComplete(urlData.publicUrl);
      toast.success("Image téléchargée avec succès");

      // Réinitialiser le formulaire
      setPreview(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error: any) {
      console.error("Erreur lors du téléchargement:", error);
      toast.error(`Erreur lors du téléchargement: ${error.message || "Erreur inconnue"}`);
    } finally {
      setIsUploading(false);
    }
  };

  const clearSelection = () => {
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="space-y-2">
        <Label htmlFor="image-upload">Télécharger une image</Label>
        <div className="flex items-center gap-2">
          <Input
            id="image-upload"
            type="file"
            accept="image/*"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="flex-1"
            disabled={isUploading}
          />
          {preview ? (
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={clearSelection}
              disabled={isUploading}
            >
              <X className="h-4 w-4" />
            </Button>
          ) : null}
        </div>
      </div>

      {preview && (
        <div className="relative border rounded-md overflow-hidden">
          <img
            src={preview}
            alt="Aperçu"
            className="w-full h-auto max-h-48 object-contain"
          />
        </div>
      )}

      <Button
        type="button"
        onClick={uploadFile}
        disabled={!preview || isUploading}
        className="w-full"
      >
        {isUploading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Téléchargement en cours...
          </>
        ) : (
          <>
            <Upload className="mr-2 h-4 w-4" />
            Télécharger l'image
          </>
        )}
      </Button>
    </div>
  );
}
