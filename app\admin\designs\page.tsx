import { Metadata } from "next";
import { AdminPageLayout } from "@/components/admin/admin-page-layout";

export const metadata: Metadata = {
  title: "Gestion des designs prédéfinis | Admin HCP-DESIGN CI",
  description: "<PERSON><PERSON>rez les designs prédéfinis disponibles pour personnalisation",
};

export default function DesignsPage() {
  return (
    <AdminPageLayout>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Gestion des designs prédéfinis</h1>
      </div>
      <div className="space-y-4">
        <p className="text-muted-foreground">
          Cette page vous permet de gérer les designs prédéfinis disponibles pour la personnalisation.
        </p>
        {/* Contenu à implémenter */}
        <div className="p-8 bg-white rounded-lg border">
          <p className="text-center text-muted-foreground">
            Fonctionnalité en cours de développement
          </p>
        </div>
      </div>
    </AdminPageLayout>
  );
}