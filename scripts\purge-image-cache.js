/**
 * Script pour vider le cache des images sur Vercel
 * 
 * Pour utiliser ce script :
 * 1. Installez les dépendances : npm install node-fetch dotenv
 * 2. Créez un fichier .env avec VERCEL_TOKEN, VERCEL_TEAM_ID et VERCEL_PROJECT_ID
 * 3. Exécutez le script : node scripts/purge-image-cache.js
 */

const fetch = require('node-fetch');
require('dotenv').config();

// Récupérer les variables d'environnement
const VERCEL_TOKEN = process.env.VERCEL_TOKEN;
const VERCEL_TEAM_ID = process.env.VERCEL_TEAM_ID;
const VERCEL_PROJECT_ID = process.env.VERCEL_PROJECT_ID;

// Vérifier que les variables d'environnement sont définies
if (!VERCEL_TOKEN || !VERCEL_TEAM_ID || !VERCEL_PROJECT_ID) {
  console.error('Les variables d\'environnement VERCEL_TOKEN, VERCEL_TEAM_ID et VERCEL_PROJECT_ID doivent être définies.');
  process.exit(1);
}

/**
 * Fonction pour vider le cache des images sur Vercel
 * @param {string} path - Chemin de l'image à vider du cache (par exemple, /images/*)
 */
async function purgeCache(path) {
  try {
    const response = await fetch(`https://api.vercel.com/v1/projects/${VERCEL_PROJECT_ID}/purge-cache`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${VERCEL_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        teamId: VERCEL_TEAM_ID,
        paths: [path],
      }),
    });

    const data = await response.json();

    if (response.ok) {
      console.log(`Cache vidé avec succès pour ${path}`);
      console.log(data);
    } else {
      console.error(`Erreur lors de la vidange du cache pour ${path}`);
      console.error(data);
    }
  } catch (error) {
    console.error('Erreur lors de la vidange du cache :', error);
  }
}

// Vider le cache pour toutes les images
async function purgeAllImageCache() {
  // Liste des chemins d'images à vider du cache
  const paths = [
    '/images/*',
    '/_next/image*',
  ];

  // Vider le cache pour chaque chemin
  for (const path of paths) {
    await purgeCache(path);
  }
}

// Exécuter la fonction principale
purgeAllImageCache().then(() => {
  console.log('Terminé !');
}).catch((error) => {
  console.error('Erreur :', error);
});
