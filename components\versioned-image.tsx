"use client";

import Image, { ImageProps } from "next/image";
import { getVersionedImagePath } from "@/lib/image-version";

interface VersionedImageProps extends Omit<ImageProps, "src"> {
  src: string;
  alt: string;
}

/**
 * Composant Image qui ajoute automatiquement un paramètre de version à l'URL de l'image
 * pour contourner le cache du navigateur et du CDN
 */
export default function VersionedImage({
  src,
  alt,
  ...props
}: VersionedImageProps) {
  // Ajouter le paramètre de version à l'URL de l'image
  const versionedSrc = getVersionedImagePath(src);

  return <Image src={versionedSrc} alt={alt} {...props} />;
}
