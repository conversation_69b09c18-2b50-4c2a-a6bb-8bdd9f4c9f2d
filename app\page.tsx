import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Truck, Shield, ThumbsUp } from "lucide-react"
import Testimonials from "@/components/testimonials"
import { BannerExample } from "@/components/banner-example";
import { MediaBanner } from "@/components/media-banner";
import SimpleProductCard from "@/components/simple-product-card";

export default function Home() {
  // Exemple de médias pour la bannière (images et vidéos)
  const bannerMediaItems = [
    {
      type: 'image' as const,
      src: '/images/banners/home/<USER>',
      alt: 'Bannière principale'
    },
    {
      type: 'image' as const,
      src: '/images/banners/home/<USER>',
      alt: 'Collection Gospel'
    },
    {
      type: 'image' as const,
      src: '/images/banners/home/<USER>',
      alt: 'Designs religieux'
    },
    // Exemple de vidéo - vous pouvez ajouter vos propres vidéos ici
    // {
    //   type: 'video' as const,
    //   src: '/videos/promo-video.mp4',
    //   poster: '/images/video-poster.jpg'
    // }
  ];

  return (
    <main className="flex flex-col min-h-screen">
      <MediaBanner
        mediaItems={bannerMediaItems}
        title="Découvrez nos promotions exclusives"
        subtitle="Profitez de réductions spéciales sur nos coques personnalisables de haute qualité"
        buttonText="Voir les promotions"
        buttonLink="/promos"
        interval={4000}
      />

      {/* Section 3 Produits Vedettes */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Nos Produits Vedettes</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <SimpleProductCard
              product={{
                id: "featured-1",
                name: "Coque Gospel Personnalisée",
                price: 15000,
                image: "/images/accueil/produits_vedettes/coque _gospel_personnalisée/gospel.png",
                rating: 5,
                reviews: 24,
                isNew: true,
                category: "Gospel",
                type: "coque"
              }}
              viewMode="grid"
            />
            <SimpleProductCard
              product={{
                id: "featured-2",
                name: "Design Moderne Premium",
                price: 12000,
                image: "/images/accueil/produits_vedettes/design_moderne_premium/moderne.jpeg",
                rating: 4,
                reviews: 18,
                isBestseller: true,
                category: "Design",
                type: "coque"
              }}
              viewMode="grid"
            />
            <SimpleProductCard
              product={{
                id: "featured-3",
                name: "Coque Tendance 2024",
                price: 10000,
                image: "/images/accueil/produits_vedettes/coque-tendance_2024/tendance.png",
                rating: 5,
                reviews: 32,
                isNew: true,
                category: "Tendance",
                type: "coque"
              }}
              viewMode="grid"
            />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Pourquoi choisir nos coques personnalisées</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 mb-4 bg-purple-100 rounded-full text-purple-600">
                <ThumbsUp className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Qualité Premium</h3>
              <p className="text-gray-600">
                Matériaux durables et impression haute définition pour des coques qui durent.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 mb-4 bg-purple-100 rounded-full text-purple-600">
                <Truck className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Livraison Rapide</h3>
              <p className="text-gray-600">Production et expédition en 48h pour recevoir votre création rapidement.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 mb-4 bg-purple-100 rounded-full text-purple-600">
                <Shield className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Garantie Satisfaction</h3>
              <p className="text-gray-600">Satisfait ou remboursé sous 30 jours, nous garantissons la qualité.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Section Gospel et Designs Généraux sur la même ligne */}
      <section className="py-20 bg-gradient-to-br from-purple-50 to-blue-50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid md:grid-cols-2 gap-12">
            {/* Produits Gospel */}
            <div className="text-center">
              <h2 className="text-4xl md:text-5xl font-bold text-purple-800 mb-6">Produits Gospel</h2>
              <p className="text-lg text-gray-700 mb-8">
                Découvrez notre collection exclusive de produits personnalisés pour la communauté chrétienne. 
                Des designs inspirants pour exprimer votre foi au quotidien.
              </p>
              <Link href="/gallery">
                <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg">
                  Découvrir les Produits Gospel
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>

            {/* Designs Généraux */}
            <div className="text-center">
              <h2 className="text-4xl md:text-5xl font-bold text-blue-800 mb-6">Designs Généraux</h2>
              <p className="text-lg text-gray-700 mb-8">
                Explorez notre vaste gamme de designs créatifs et tendances. 
                Personnalisez vos coques avec style et originalité.
              </p>
              <Link href="/gallery">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg">
                  Explorer les Designs Généraux
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Customization CTA */}
      <section className="py-16 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="container mx-auto px-4 md:px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">Prêt à créer votre coque unique?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Notre outil de personnalisation intuitif vous permet de créer une coque qui vous ressemble en quelques
            minutes. Profitez de nos promotions exclusives!
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/promos">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                Voir les promotions
              </Button>
            </Link>
            <Link href="/customize">
              <Button size="lg" className="bg-purple-700 text-white hover:bg-purple-800 border border-white border-solid">
                Commencer à personnaliser
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="bg-transparent text-white border-white border-[1px] border-solid hover:bg-white hover:text-purple-600">
                Demander un devis
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Ce que nos clients disent</h2>
          <Testimonials />
        </div>
      </section>

      {/* Compatible Devices */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Compatible avec tous les modèles populaires</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 text-center">
            {[
              "iPhone 15",
              "iPhone 14",
              "iPhone 13",
              "iPhone 12",
              "iPhone SE",
              "iPhone 11",
              "Samsung S23",
              "Samsung S22",
              "Samsung A54",
              "Google Pixel 7",
              "Xiaomi 13",
              "Huawei P40",
            ].map((model) => (
              <div key={model} className="p-4 bg-white rounded-lg shadow-sm border border-gray-100 border-solid">
                <p className="font-medium">{model}</p>
              </div>
            ))}
          </div>
          <div className="text-center mt-8">
            <Link href="/models">
              <Button variant="link" className="text-purple-600">
                Voir tous les modèles compatibles
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </main>
  )
}


