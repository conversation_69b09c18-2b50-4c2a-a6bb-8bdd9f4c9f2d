-- Migration pour créer la table site_settings

-- Créer la table site_settings
CREATE TABLE IF NOT EXISTS public.site_settings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  value JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- C<PERSON>er un index sur la colonne key
CREATE INDEX IF NOT EXISTS idx_site_settings_key ON public.site_settings(key);

-- Activer RLS (Row Level Security)
ALTER TABLE public.site_settings ENABLE ROW LEVEL SECURITY;

-- Créer une politique pour que seuls les administrateurs puissent gérer les paramètres
CREATE POLICY "Ad<PERSON> can manage site settings" 
  ON public.site_settings 
  USING (auth.role() = 'service_role');

-- <PERSON><PERSON>er une politique pour que tous les utilisateurs puissent lire les paramètres
CREATE POLICY "All users can read site settings" 
  ON public.site_settings 
  FOR SELECT
  USING (true);

-- Insérer les paramètres par défaut
INSERT INTO public.site_settings (key, value)
VALUES 
  ('site_name', '"HCP Design CI"'),
  ('site_description', '"Coques de téléphone personnalisées"'),
  ('primary_color', '"#6d28d9"'),
  ('secondary_color', '"#8b5cf6"'),
  ('show_whatsapp_button', 'true'),
  ('whatsapp_number', '"+2250709495849"'),
  ('navigation', '[{"name":"Accueil","path":"/"},{"name":"Produits","path":"/produits"},{"name":"Promo","path":"/promo"},{"name":"Personnaliser","path":"/customize"},{"name":"Contact","path":"/contact"}]'),
  ('home_banner', '{"enabled":true,"images":["/images/home/<USER>","/images/home/<USER>","/images/home/<USER>"],"transition_time":4000,"transition_effect":"zoom"}')
ON CONFLICT (key) DO UPDATE
SET 
  value = EXCLUDED.value,
  updated_at = NOW();

-- Créer une fonction pour mettre à jour la date de mise à jour
CREATE OR REPLACE FUNCTION update_site_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Créer un déclencheur pour mettre à jour la date de mise à jour
CREATE TRIGGER update_site_settings_updated_at
BEFORE UPDATE ON public.site_settings
FOR EACH ROW
EXECUTE FUNCTION update_site_settings_updated_at();
