"use client";

import { <PERSON>ada<PERSON> } from "next";
import { AdminLayout } from "@/components/admin/admin-layout";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Plus, Edit, Trash2, Upload, Smartphone, Check, ImageIcon } from "lucide-react";
import Image from "next/image";

// Note: Metadata ne peut pas être utilisé dans un composant client
// Nous devons le supprimer ou le déplacer dans un fichier layout.tsx
// export const metadata: Metadata = {
//   title: "Gestion des modèles de téléphones | Admin HCP-DESIGN CI",
//   description: "Gérez les modèles de téléphones disponibles pour personnalisation",
// };

// Types pour les modèles de téléphone
interface PhoneModel {
  id: string;
  brand: string;
  name: string;
  image: string;
  dimensions: string;
  releaseYear: number;
  popular: boolean;
  hasCase: boolean;
}

// Données d'exemple (à remplacer par des appels API réels)
const phoneModels: PhoneModel[] = [
  {
    id: "iphone15pro",
    brand: "Apple",
    name: "iPhone 15 Pro",
    image: "/images/phone-models/iphone15.png?height=300&width=150",
    dimensions: "146.7 x 71.5 x 8.3 mm",
    releaseYear: 2023,
    popular: true,
    hasCase: true,
  },
  {
    id: "iphone15",
    brand: "Apple",
    name: "iPhone 15",
    image: "/images/phone-models/iphone15.png?height=300&width=150",
    dimensions: "147.6 x 71.6 x 7.8 mm",
    releaseYear: 2023,
    popular: true,
    hasCase: true,
  },
  {
    id: "samsungs23ultra",
    brand: "Samsung",
    name: "Galaxy S23 Ultra",
    image: "/images/phone-models/iphone15.png?height=300&width=150",
    dimensions: "163.4 x 78.1 x 8.9 mm",
    releaseYear: 2023,
    popular: true,
    hasCase: true,
  },
  {
    id: "pixel8pro",
    brand: "Google",
    name: "Pixel 8 Pro",
    image: "/images/phone-models/iphone15.png?height=300&width=150",
    dimensions: "162.6 x 76.5 x 8.8 mm",
    releaseYear: 2023,
    popular: true,
    hasCase: false,
  },
  {
    id: "xiaomi13pro",
    brand: "Xiaomi",
    name: "Xiaomi 13 Pro",
    image: "/images/phone-models/iphone15.png?height=300&width=150",
    dimensions: "162.9 x 74.6 x 8.4 mm",
    releaseYear: 2023,
    popular: true,
    hasCase: false,
  },
];

// Marques disponibles
const brands = ["Apple", "Samsung", "Google", "Xiaomi", "Huawei", "OPPO", "OnePlus", "Nothing"];

export default function PhoneModelsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBrand, setSelectedBrand] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [editingModel, setEditingModel] = useState<PhoneModel | null>(null);
  
  // État pour le formulaire d'ajout/modification
  const [formData, setFormData] = useState({
    id: "",
    brand: "",
    name: "",
    image: "/images/phone-models/iphone15.png?height=300&width=150",
    dimensions: "",
    releaseYear: new Date().getFullYear(),
    popular: false,
    hasCase: false,
  });

  // Filtrer les modèles en fonction de la recherche et de la marque sélectionnée
  const filteredModels = phoneModels.filter(model => {
    const matchesSearch = 
      model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.brand.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesBrand = selectedBrand === "all" || model.brand === selectedBrand;
    
    return matchesSearch && matchesBrand;
  });

  // Gérer l'ouverture du formulaire d'édition
  const handleEditModel = (model: PhoneModel) => {
    setEditingModel(model);
    setFormData(model);
    setIsAddDialogOpen(true);
  };

  // Gérer la soumission du formulaire
  const handleSubmitForm = () => {
    // Logique pour ajouter ou mettre à jour un modèle
    // Dans une application réelle, cela ferait un appel API
    console.log("Saving model:", formData);
    
    // Réinitialiser le formulaire et fermer le dialogue
    setFormData({
      id: "",
      brand: "",
      name: "",
      image: "/images/phone-models/iphone15.png?height=300&width=150",
      dimensions: "",
      releaseYear: new Date().getFullYear(),
      popular: false,
      hasCase: false,
    });
    setEditingModel(null);
    setIsAddDialogOpen(false);
  };

  // Gérer l'importation CSV
  const handleImportCSV = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Logique pour traiter le fichier CSV
      console.log("Importing file:", file.name);
      
      // Dans une application réelle, vous utiliseriez FileReader pour lire le contenu
      // et le traiter ligne par ligne
      
      // Fermer le dialogue après l'importation
      setIsImportDialogOpen(false);
    }
  };

  return (
    <AdminLayout>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Gestion des modèles de téléphones</h1>
        <div className="flex gap-2">
          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="mr-2 h-4 w-4" /> Importer CSV
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Importer des modèles de téléphones</DialogTitle>
                <DialogDescription>
                  Téléchargez un fichier CSV contenant les modèles de téléphones à importer.
                  Le fichier doit contenir les colonnes suivantes: id, brand, name, dimensions, releaseYear.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <Label htmlFor="csv-file">Fichier CSV</Label>
                <Input 
                  id="csv-file" 
                  type="file" 
                  accept=".csv" 
                  onChange={handleImportCSV}
                />
                <div className="text-xs text-muted-foreground">
                  <a href="#" className="text-primary underline">Télécharger un modèle de fichier CSV</a>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>Annuler</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" /> Ajouter un modèle
              </Button>
            </DialogTrigger>
            <Button variant="outline" className="ml-2" asChild>
              <a href="/admin/phone-models/images">
                <ImageIcon className="mr-2 h-4 w-4" /> Gérer les images
              </a>
            </Button>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>{editingModel ? "Modifier un modèle" : "Ajouter un modèle"}</DialogTitle>
                <DialogDescription>
                  {editingModel 
                    ? "Modifiez les informations du modèle de téléphone." 
                    : "Ajoutez un nouveau modèle de téléphone au catalogue."}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="brand">Marque</Label>
                    <Select 
                      value={formData.brand} 
                      onValueChange={(value) => setFormData({...formData, brand: value})}
                    >
                      <SelectTrigger id="brand">
                        <SelectValue placeholder="Sélectionner une marque" />
                      </SelectTrigger>
                      <SelectContent>
                        {brands.map((brand) => (
                          <SelectItem key={brand} value={brand}>
                            {brand}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="name">Nom du modèle</Label>
                    <Input 
                      id="name" 
                      value={formData.name} 
                      onChange={(e) => setFormData({...formData, name: e.target.value})} 
                      placeholder="ex: iPhone 15 Pro"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="dimensions">Dimensions (L x l x h)</Label>
                  <Input 
                    id="dimensions" 
                    value={formData.dimensions} 
                    onChange={(e) => setFormData({...formData, dimensions: e.target.value})} 
                    placeholder="ex: 146.7 x 71.5 x 8.3 mm"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="releaseYear">Année de sortie</Label>
                    <Input 
                      id="releaseYear" 
                      type="number" 
                      value={formData.releaseYear} 
                      onChange={(e) => setFormData({...formData, releaseYear: parseInt(e.target.value)})} 
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="image">Image (URL)</Label>
                    <Input 
                      id="image" 
                      value={formData.image} 
                      onChange={(e) => setFormData({...formData, image: e.target.value})} 
                    />
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="popular"
                    checked={formData.popular}
                    onChange={(e) => setFormData({...formData, popular: e.target.checked})}
                    className="rounded border-gray-300"
                    aria-labelledby="popular-label"
                    title="Modèle populaire"
                  />
                  <Label htmlFor="popular">Modèle populaire</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="hasCase"
                    checked={formData.hasCase}
                    onChange={(e) => setFormData({...formData, hasCase: e.target.checked})}
                    className="rounded border-gray-300"
                    aria-labelledby="hasCase-label"
                    title="Coque disponible"
                  />
                  <Label htmlFor="hasCase">Coque disponible</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsAddDialogOpen(false);
                  setEditingModel(null);
                }}>Annuler</Button>
                <Button onClick={handleSubmitForm}>Enregistrer</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="grid gap-2 flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Rechercher un modèle..." 
                  className="w-full pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="grid gap-2 w-full md:w-[180px]">
              <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les marques" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les marques</SelectItem>
                  {brands.map((brand) => (
                    <SelectItem key={brand} value={brand}>
                      {brand}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Tabs defaultValue="grid" className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="grid">Grille</TabsTrigger>
            <TabsTrigger value="table">Tableau</TabsTrigger>
          </TabsList>
          <div className="text-sm text-muted-foreground">
            {filteredModels.length} modèle(s) trouvé(s)
          </div>
        </div>
        
        <TabsContent value="grid" className="mt-0">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {filteredModels.map((model) => (
              <Card key={model.id} className="overflow-hidden">
                <div className="relative">
                  <div className="h-40 flex items-center justify-center p-4 bg-gray-50">
                    <img 
                      src={model.image} 
                      alt={model.name} 
                      className="h-full object-contain"
                    />
                  </div>
                  {model.popular && (
                    <div className="absolute top-2 right-2 bg-purple-600 text-white text-xs px-2 py-1 rounded-full">
                      Populaire
                    </div>
                  )}
                  {model.hasCase && (
                    <div className="absolute top-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded-full">
                      Coque
                    </div>
                  )}
                </div>
                <CardContent className="p-4">
                  <div className="text-sm font-medium">{model.brand}</div>
                  <div className="font-bold mb-1">{model.name}</div>
                  <div className="text-xs text-gray-500 mb-1">{model.dimensions}</div>
                  <div className="text-xs text-gray-500 mb-3">{model.releaseYear}</div>
                  <div className="flex justify-end gap-2 mt-2">
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => handleEditModel(model)}
                    >
                      <Edit size={16} />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="text-destructive hover:text-destructive/80"
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="table" className="mt-0">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Image</TableHead>
                    <TableHead>Marque</TableHead>
                    <TableHead>Modèle</TableHead>
                    <TableHead>Dimensions</TableHead>
                    <TableHead>Année</TableHead>
                    <TableHead>Populaire</TableHead>
                    <TableHead>Coque</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredModels.map((model) => (
                    <TableRow key={model.id}>
                      <TableCell>
                        <div className="relative h-12 w-6">
                          <img 
                            src={model.image} 
                            alt={model.name} 
                            className="h-full object-contain"
                          />
                        </div>
                      </TableCell>
                      <TableCell>{model.brand}</TableCell>
                      <TableCell className="font-medium">{model.name}</TableCell>
                      <TableCell>{model.dimensions}</TableCell>
                      <TableCell>{model.releaseYear}</TableCell>
                      <TableCell>
                        {model.popular ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : null}
                      </TableCell>
                      <TableCell>
                        {model.hasCase ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : null}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => handleEditModel(model)}
                          >
                            <Edit size={16} />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            className="text-destructive hover:text-destructive/80"
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
}

