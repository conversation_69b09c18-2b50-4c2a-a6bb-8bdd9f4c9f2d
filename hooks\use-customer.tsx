"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { supabase } from "@/lib/supabase"

interface Customer {
  id: string
  fullName: string
  whatsapp: string
}

type CustomerContextType = {
  customer: Customer | null
  isLoading: boolean
  isAuthenticated: boolean
  logout: () => void
  refreshCustomer: () => Promise<void>
}

const CustomerContext = createContext<CustomerContextType | undefined>(undefined)

export function CustomerProvider({ children }: { children: React.ReactNode }) {
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const loadCustomerFromStorage = () => {
    try {
      if (typeof window !== 'undefined') {
        const customerId = localStorage.getItem('customer_id')
        const customerName = localStorage.getItem('customer_name')
        const customerWhatsapp = localStorage.getItem('customer_whatsapp')

        if (customerId && customerName && customerWhatsapp) {
          setCustomer({
            id: customerId,
            fullName: customerName,
            whatsapp: customerWhatsapp
          })
          return true
        }
      }
      return false
    } catch (error) {
      console.error("Erreur lors du chargement des données client:", error)
      return false
    }
  }

  const refreshCustomer = async () => {
    setIsLoading(true)
    try {
      const loaded = loadCustomerFromStorage()
      if (!loaded) {
        setCustomer(null)
      }
    } catch (error) {
      console.error("Erreur lors du rafraîchissement des données client:", error)
      setCustomer(null)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    refreshCustomer()
  }, [])

  const logout = () => {
    try {
      localStorage.removeItem('customer_id')
      localStorage.removeItem('customer_name')
      localStorage.removeItem('customer_whatsapp')
      setCustomer(null)
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error)
    }
  }

  return (
    <CustomerContext.Provider
      value={{
        customer,
        isLoading,
        isAuthenticated: !!customer,
        logout,
        refreshCustomer
      }}
    >
      {children}
    </CustomerContext.Provider>
  )
}

export function useCustomer() {
  const context = useContext(CustomerContext)
  if (context === undefined) {
    throw new Error("useCustomer doit être utilisé à l'intérieur d'un CustomerProvider")
  }
  return context
}
