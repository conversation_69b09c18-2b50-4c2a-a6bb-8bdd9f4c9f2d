"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from "recharts"
import { Calendar } from "@/components/ui/calendar"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowUpRight, Users, ShoppingBag, CreditCard, TrendingUp, Calendar as CalendarIcon } from "lucide-react"

// Données fictives pour les statistiques
const salesData = [
  { name: "Jan", total: 1200 },
  { name: "Fév", total: 1900 },
  { name: "<PERSON>", total: 1500 },
  { name: "<PERSON>v<PERSON>", total: 2200 },
  { name: "<PERSON>", total: 2800 },
  { name: "Ju<PERSON>", total: 2600 },
  { name: "<PERSON><PERSON>", total: 3100 },
  { name: "<PERSON><PERSON><PERSON><PERSON>", total: 2900 },
  { name: "<PERSON>", total: 3300 },
  { name: "Oct", total: 3580 },
  { name: "Nov", total: 3900 },
  { name: "Déc", total: 4200 },
]

const productData = [
  { name: "Coques iPhone", value: 540 },
  { name: "Coques Samsung", value: 320 },
  { name: "Coques Xiaomi", value: 210 },
  { name: "Coques Google", value: 130 },
  { name: "Autres", value: 180 },
]

const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#0088fe"]

export default function StatisticsPage() {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [period, setPeriod] = useState("year")

  // Statistiques générales
  const stats = {
    totalSales: "4,385,000 FCFA",
    totalOrders: "1,324",
    totalCustomers: "856",
    averageOrderValue: "3,312 FCFA",
    conversionRate: "3.2%",
    growthRate: "+24.5%",
  }

  // Données récentes
  const recentOrders = [
    { id: "ORD-001", customer: "Konan Kouadio", date: "15/10/2023", amount: "12,500 FCFA", status: "Livré" },
    { id: "ORD-002", customer: "Aya Touré", date: "14/10/2023", amount: "8,900 FCFA", status: "En cours" },
    { id: "ORD-003", customer: "Mamadou Diallo", date: "13/10/2023", amount: "15,200 FCFA", status: "Livré" },
    { id: "ORD-004", customer: "Fatou Cissé", date: "12/10/2023", amount: "5,500 FCFA", status: "Livré" },
    { id: "ORD-005", customer: "Ibrahim Koné", date: "11/10/2023", amount: "9,800 FCFA", status: "Annulé" },
  ]

  const topProducts = [
    { name: "Coque iPhone 15 Pro - Design Floral", sales: 124, revenue: "620,000 FCFA" },
    { name: "Coque Samsung S23 - Transparente", sales: 98, revenue: "294,000 FCFA" },
    { name: "Coque iPhone 14 - Personnalisée", sales: 87, revenue: "435,000 FCFA" },
    { name: "Coque Xiaomi Redmi Note 12 - Premium", sales: 65, revenue: "325,000 FCFA" },
    { name: "Coque Google Pixel 7 - Antichoc", sales: 42, revenue: "210,000 FCFA" },
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold">Statistiques</h1>
          <p className="text-muted-foreground">Analysez les performances de votre boutique</p>
        </div>
        <div className="flex items-center space-x-2 mt-4 md:mt-0">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sélectionner une période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Aujourd'hui</SelectItem>
              <SelectItem value="week">Cette semaine</SelectItem>
              <SelectItem value="month">Ce mois</SelectItem>
              <SelectItem value="year">Cette année</SelectItem>
            </SelectContent>
          </Select>
          <div className="relative">
            <Button variant="outline" className="w-[260px] justify-start text-left font-normal">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? date.toLocaleDateString() : "Sélectionner une date"}
            </Button>
          </div>
        </div>
      </div>

      {/* Cartes de statistiques */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ventes Totales</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSales}</div>
            <p className="text-xs text-muted-foreground">
              {stats.growthRate} par rapport à la période précédente
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Commandes</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              +12.3% par rapport à la période précédente
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              +18.7% par rapport à la période précédente
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taux de Conversion</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.conversionRate}</div>
            <p className="text-xs text-muted-foreground">
              +2.5% par rapport à la période précédente
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="products">Produits</TabsTrigger>
          <TabsTrigger value="customers">Clients</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Ventes</CardTitle>
              <CardDescription>
                Évolution des ventes sur la période sélectionnée
              </CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={salesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => `${value} FCFA`} />
                  <Legend />
                  <Bar dataKey="total" fill="#8884d8" name="Ventes (FCFA)" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="products" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Répartition des Ventes par Produit</CardTitle>
                <CardDescription>
                  Distribution des ventes par catégorie de produit
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={productData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {productData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value} unités`} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Produits les Plus Vendus</CardTitle>
                <CardDescription>
                  Top 5 des produits les plus populaires
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topProducts.map((product, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{product.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {product.sales} ventes · {product.revenue}
                        </p>
                      </div>
                      <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Commandes Récentes</CardTitle>
              <CardDescription>
                Les 5 dernières commandes passées sur votre boutique
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{order.customer}</p>
                      <p className="text-xs text-muted-foreground">
                        {order.id} · {order.date}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{order.amount}</p>
                      <p className={`text-xs ${order.status === "Livré" ? "text-green-500" : order.status === "Annulé" ? "text-red-500" : "text-yellow-500"}`}>
                        {order.status}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
