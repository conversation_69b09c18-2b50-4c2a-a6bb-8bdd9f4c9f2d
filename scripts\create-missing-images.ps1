# Script pour créer les images manquantes
# Ce script crée des fichiers PNG vides pour les images manquantes

# Fonction pour créer un fichier PNG vide
function Create-Empty-PNG {
    param (
        [string]$imagePath
    )
    
    # Vérifier si le répertoire existe
    $directory = Split-Path -Path $imagePath -Parent
    if (-not (Test-Path $directory)) {
        New-Item -Path $directory -ItemType Directory -Force | Out-Null
        Write-Host "Dossier créé: $directory" -ForegroundColor Green
    }
    
    # Vérifier si le fichier existe déjà
    if (Test-Path $imagePath) {
        Write-Host "Le fichier existe déjà: $imagePath" -ForegroundColor Yellow
        return
    }
    
    # Créer un fichier PNG vide (1x1 pixel transparent)
    # Nous utilisons un fichier PNG de base64 encodé qui représente un pixel transparent
    $base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
    $bytes = [Convert]::FromBase64String($base64)
    
    # Écrire le fichier PNG
    [System.IO.File]::WriteAllBytes($imagePath, $bytes)
    
    Write-Host "Fichier PNG créé: $imagePath" -ForegroundColor Green
}

# Fonction pour créer un fichier JPG vide
function Create-Empty-JPG {
    param (
        [string]$imagePath
    )
    
    # Vérifier si le répertoire existe
    $directory = Split-Path -Path $imagePath -Parent
    if (-not (Test-Path $directory)) {
        New-Item -Path $directory -ItemType Directory -Force | Out-Null
        Write-Host "Dossier créé: $directory" -ForegroundColor Green
    }
    
    # Vérifier si le fichier existe déjà
    if (Test-Path $imagePath) {
        Write-Host "Le fichier existe déjà: $imagePath" -ForegroundColor Yellow
        return
    }
    
    # Créer un fichier JPG vide (1x1 pixel blanc)
    # Nous utilisons un fichier JPG de base64 encodé qui représente un pixel blanc
    $base64 = "/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAABAAEDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD3+iiigD//2Q=="
    $bytes = [Convert]::FromBase64String($base64)
    
    # Écrire le fichier JPG
    [System.IO.File]::WriteAllBytes($imagePath, $bytes)
    
    Write-Host "Fichier JPG créé: $imagePath" -ForegroundColor Green
}

# Liste des images manquantes
$missingImages = @(
    "public\images\products\personnalisation\personnalisation.png",
    "public\images\gifts\anniversary-pack.jpg",
    "public\images\gifts\wedding-pack.jpg",
    "public\images\gifts\dinner-pack.jpg",
    "public\images\gifts\corporate-pack.jpg",
    "public\images\gifts\baby-pack.jpg",
    "public\images\gifts\graduation-pack.jpg",
    "public\images\products\coque-iphone-floral.jpg",
    "public\images\products\coque-iphone-abstract.jpg",
    "public\images\products\coque-samsung-galaxy.jpg"
)

# Créer les images manquantes
foreach ($imagePath in $missingImages) {
    if ($imagePath -like "*.png") {
        Create-Empty-PNG -imagePath $imagePath
    } elseif ($imagePath -like "*.jpg") {
        Create-Empty-JPG -imagePath $imagePath
    }
}

Write-Host "Création des images manquantes terminée!" -ForegroundColor Green
