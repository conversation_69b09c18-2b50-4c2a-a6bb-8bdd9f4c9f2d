import { NextRequest, NextResponse } from 'next/server';
import { sendAndStoreVerificationCode, devSendAndStoreVerificationCode } from '@/lib/verification-code-service';
import { isAdminEmail } from '@/lib/admin-config';

// Limite de tentatives d'envoi de code par email (5 par heure)
const MAX_ATTEMPTS_PER_HOUR = 5;
const attemptsByEmail: Record<string, { count: number; resetTime: number }> = {};

export async function POST(request: NextRequest) {
  try {
    // Récupérer l'email du corps de la requête
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email manquant' },
        { status: 400 }
      );
    }

    // Vérifier si l'email est autorisé à accéder à l'administration
    if (!isAdminEmail(email)) {
      return NextResponse.json(
        { success: false, message: 'Email non autorisé' },
        { status: 403 }
      );
    }

    // Vérifier la limite de tentatives
    const now = Date.now();
    const emailAttempts = attemptsByEmail[email] || { count: 0, resetTime: now + 3600000 };

    // Réinitialiser le compteur si le temps de réinitialisation est dépassé
    if (now > emailAttempts.resetTime) {
      emailAttempts.count = 0;
      emailAttempts.resetTime = now + 3600000;
    }

    // Vérifier si la limite est atteinte
    if (emailAttempts.count >= MAX_ATTEMPTS_PER_HOUR) {
      const minutesRemaining = Math.ceil((emailAttempts.resetTime - now) / 60000);
      return NextResponse.json(
        {
          success: false,
          message: `Trop de tentatives. Veuillez réessayer dans ${minutesRemaining} minutes.`,
        },
        { status: 429 }
      );
    }

    // Incrémenter le compteur de tentatives
    emailAttempts.count += 1;
    attemptsByEmail[email] = emailAttempts;

    // Envoyer le code de vérification par email
    console.log("Envoi du code de vérification par email à:", email);

    // Utiliser la fonction normale qui enverra un vrai email si SMTP est configuré
    let success = await sendAndStoreVerificationCode(email);

    console.log("Résultat de l'envoi:", success ? "Succès" : "Échec");

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Code de vérification envoyé avec succès',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: "Erreur lors de l'envoi du code de vérification",
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Erreur lors de l\'envoi du code de vérification:', error);
    return NextResponse.json(
      {
        success: false,
        message: "Erreur serveur lors de l'envoi du code de vérification",
      },
      { status: 500 }
    );
  }
}
