import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { supabase, supabaseApi } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    // Vérifier si la requête contient un formulaire multipart (pour les fichiers)
    const contentType = request.headers.get('content-type') || '';
    let orderData;
    let paymentScreenshot = null;

    if (contentType.includes('multipart/form-data')) {
      // Traiter le formulaire multipart
      const formData = await request.formData();
      const orderDataStr = formData.get('orderData') as string;
      paymentScreenshot = formData.get('screenshot') as File;

      if (!orderDataStr) {
        return NextResponse.json(
          { error: 'Données de commande manquantes' },
          { status: 400 }
        );
      }

      orderData = JSON.parse(orderDataStr);
    } else {
      // Traiter le JSON standard
      orderData = await request.json();
    }

    // Récupérer l'ID utilisateur (connecté ou anonyme)
    const cookieStore = cookies();
    const sessionCookie = cookieStore.get('sb-auth-token');

    let userId;

    if (sessionCookie) {
      try {
        // Utilisateur connecté
        const { data: { user }, error } = await supabase.auth.getUser(sessionCookie.value);

        if (error || !user) {
          // Fallback à un ID anonyme
          userId = `anonymous_${uuidv4()}`;
        } else {
          userId = user.id;
        }
      } catch (error) {
        // En cas d'erreur, utiliser un ID anonyme
        userId = `anonymous_${uuidv4()}`;
      }
    } else {
      // Utilisateur anonyme
      userId = `anonymous_${uuidv4()}`;
    }

    // Stocker l'ID de session anonyme dans un cookie pour référence future
    if (userId.startsWith('anonymous_')) {
      cookieStore.set('anonymous_session_id', userId, {
        path: '/',
        maxAge: 60 * 60 * 24 * 30, // 30 jours
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
    }

    // Créer la commande
    const order = {
      user_id: userId,
      status: 'pending',
      total: orderData.total,
      shipping_address: orderData.address,
      shipping_city: orderData.city,
      shipping_notes: orderData.notes,
      payment_method: orderData.paymentMethod,
      payment_message: orderData.paymentMessage || '',
      customer_name: orderData.fullName,
      customer_phone: orderData.phone,
    };

    // Préparer les articles de la commande
    const orderItems = orderData.items.map(item => ({
      product_id: item.productId,
      quantity: item.quantity,
      price: item.price,
      customized: item.customized,
      customization_data: item.customizationData,
    }));

    // Créer la commande dans la base de données
    const { data, error } = await supabaseApi.orders.create(order, orderItems);

    if (error) {
      console.error('Erreur lors de la création de la commande:', error);
      return NextResponse.json(
        { error: 'Erreur lors de la création de la commande' },
        { status: 500 }
      );
    }

    // Si nous avons une capture d'écran, la télécharger
    if (paymentScreenshot && data) {
      const fileName = `payment-screenshots/${data.id}/${uuidv4()}.${paymentScreenshot.name.split('.').pop()}`;

      const { error: uploadError } = await supabase.storage
        .from('orders')
        .upload(fileName, paymentScreenshot);

      if (uploadError) {
        console.error('Erreur lors du téléchargement de la capture d\'écran:', uploadError);
        // Ne pas échouer la commande si le téléchargement échoue
      } else {
        // Mettre à jour la commande avec le chemin de la capture d'écran
        await supabase
          .from('orders')
          .update({ payment_screenshot: fileName })
          .eq('id', data.id);
      }
    }

    return NextResponse.json({
      success: true,
      orderId: data?.id
    });

  } catch (error) {
    console.error('Erreur lors de la création de la commande:', error);
    return NextResponse.json(
      { error: 'Erreur lors de la création de la commande' },
      { status: 500 }
    );
  }
}
