import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const BannerSettingsPage = () => {
  const [bannerText, setBannerText] = useState('');
  const [announcementText, setAnnouncementText] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Charger les données existantes
    const fetchData = async () => {
      try {
        const response = await fetch('/api/site-content?keys=banner_text,announcement_banner');
        if (response.ok) {
          const data = await response.json();
          setBannerText(data.banner_text || '');
          setAnnouncementText(data.announcement_banner || '');
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
      }
    };
    
    fetchData();
  }, []);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Enregistrer les deux valeurs
      await fetch('/api/site-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          banner_text: bannerText,
          announcement_banner: announcementText
        })
      });
      alert('Paramètres de bannière enregistrés avec succès!');
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement:', error);
      alert('Erreur lors de l\'enregistrement des paramètres');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Paramètres des bannières</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Bannière d'annonce (haut de page)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Input
              value={announcementText}
              onChange={(e) => setAnnouncementText(e.target.value)}
              placeholder="Texte de la bannière d'annonce (ex: Livraison gratuite pour toutes les commandes)"
            />
            <p className="text-sm text-gray-500">
              Ce texte apparaîtra dans la petite bannière en haut de toutes les pages du site.
            </p>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Bannière principale</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <textarea
              value={bannerText}
              onChange={(e) => setBannerText(e.target.value)}
              placeholder="Texte de la bannière principale"
              className="w-full min-h-[100px] p-2 border rounded-md"
            />
          </div>
        </CardContent>
      </Card>
      
      <Button 
        onClick={handleSave} 
        className="mt-6 bg-purple-600 hover:bg-purple-700"
        disabled={isLoading}
      >
        {isLoading ? 'Enregistrement...' : 'Enregistrer les modifications'}
      </Button>
    </div>
  );
};

export default BannerSettingsPage;