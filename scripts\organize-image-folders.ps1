# Script pour organiser les dossiers d'images
# Ce script crée les dossiers manquants pour les pages "Cadeaux & Gadgets" et "Tous les produits"
# et résout la confusion entre les dossiers "promo" et "promotions"

# Fonction pour créer un dossier s'il n'existe pas
function Create-Directory-If-Not-Exists {
    param (
        [string]$path
    )

    if (-not (Test-Path -Path $path)) {
        New-Item -Path $path -ItemType Directory -Force | Out-Null
        Write-Host "Dossier créé: $path" -ForegroundColor Green
    } else {
        Write-Host "Le dossier existe déjà: $path" -ForegroundColor Yellow
    }
}

# Fonction pour créer un fichier .gitkeep dans un dossier
function Create-GitKeep {
    param (
        [string]$path
    )

    $gitkeepPath = Join-Path -Path $path -ChildPath ".gitkeep"
    if (-not (Test-Path -Path $gitkeepPath)) {
        New-Item -Path $gitkeepPath -ItemType File -Force | Out-Null
        Write-Host "Fichier .gitkeep créé: $gitkeepPath" -ForegroundColor Cyan
    }
}

# Fonction pour créer un fichier PNG vide
function Create-Empty-PNG {
    param (
        [string]$path,
        [string]$name
    )

    $pngPath = Join-Path -Path $path -ChildPath "$name.png"
    if (-not (Test-Path -Path $pngPath)) {
        # Créer un fichier PNG vide (1x1 pixel transparent)
        # Nous utilisons un fichier PNG de base64 encodé qui représente un pixel transparent
        $base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
        $bytes = [Convert]::FromBase64String($base64)

        # Écrire le fichier PNG
        [System.IO.File]::WriteAllBytes($pngPath, $bytes)

        Write-Host "Fichier PNG créé: $pngPath" -ForegroundColor Green
    }
}

# Fonction pour créer un fichier JPG vide
function Create-Empty-JPG {
    param (
        [string]$path,
        [string]$name
    )

    $jpgPath = Join-Path -Path $path -ChildPath "$name.jpg"
    if (-not (Test-Path -Path $jpgPath)) {
        # Créer un fichier JPG vide (1x1 pixel blanc)
        # Nous utilisons un fichier JPG de base64 encodé qui représente un pixel blanc
        $base64 = "/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAABAAEDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD3+iiigD//2Q=="
        $bytes = [Convert]::FromBase64String($base64)

        # Écrire le fichier JPG
        [System.IO.File]::WriteAllBytes($jpgPath, $bytes)

        Write-Host "Fichier JPG créé: $jpgPath" -ForegroundColor Green
    }
}

# Chemin de base pour les images
$imagesPath = "public\images"

# 1. Résoudre la confusion entre "promo" et "promotions"
Write-Host "Résolution de la confusion entre 'promo' et 'promotions'..." -ForegroundColor Yellow

$promosPath = Join-Path -Path $imagesPath -ChildPath "promos"
$promotionsPath = Join-Path -Path $imagesPath -ChildPath "Promotions"

# Vérifier si les deux dossiers existent
if ((Test-Path -Path $promosPath) -and (Test-Path -Path $promotionsPath)) {
    # Copier les sous-dossiers de Promotions vers promos
    Get-ChildItem -Path $promotionsPath -Directory | ForEach-Object {
        $subfolderName = $_.Name
        $sourcePath = Join-Path -Path $promotionsPath -ChildPath $subfolderName
        $destinationPath = Join-Path -Path $promosPath -ChildPath $subfolderName

        # Créer le sous-dossier dans promos s'il n'existe pas
        if (-not (Test-Path -Path $destinationPath)) {
            New-Item -Path $destinationPath -ItemType Directory -Force | Out-Null
            Write-Host "Dossier créé: $destinationPath" -ForegroundColor Green
        }

        # Copier les fichiers du sous-dossier
        Get-ChildItem -Path $sourcePath -File | ForEach-Object {
            $fileName = $_.Name
            $sourceFilePath = Join-Path -Path $sourcePath -ChildPath $fileName
            $destinationFilePath = Join-Path -Path $destinationPath -ChildPath $fileName

            if (-not (Test-Path -Path $destinationFilePath)) {
                Copy-Item -Path $sourceFilePath -Destination $destinationFilePath -Force
                Write-Host "Fichier copié: $destinationFilePath" -ForegroundColor Cyan
            }
        }
    }

    # Renommer le dossier Promotions en Promotions_old
    $promotionsOldPath = Join-Path -Path $imagesPath -ChildPath "Promotions_old"
    if (Test-Path -Path $promotionsOldPath) {
        Remove-Item -Path $promotionsOldPath -Recurse -Force
    }
    Rename-Item -Path $promotionsPath -NewName "Promotions_old"
    Write-Host "Dossier 'Promotions' renommé en 'Promotions_old'" -ForegroundColor Yellow
} elseif (Test-Path -Path $promotionsPath) {
    # Seul le dossier Promotions existe, le renommer en promos
    Rename-Item -Path $promotionsPath -NewName "promos"
    Write-Host "Dossier 'Promotions' renommé en 'promos'" -ForegroundColor Yellow
}

# 2. Créer les dossiers manquants pour la page "Cadeaux & Gadgets"
Write-Host "Création des dossiers pour la page 'Cadeaux & Gadgets'..." -ForegroundColor Yellow

# Standardiser le nom du dossier pour les cadeaux
$giftsPath = Join-Path -Path $imagesPath -ChildPath "gifts"
Create-Directory-If-Not-Exists -path $giftsPath

# Créer les dossiers pour chaque pack
$packNames = @(
    "anniversary-pack",
    "wedding-pack",
    "dinner-pack",
    "corporate-pack",
    "baby-pack",
    "graduation-pack"
)

foreach ($packName in $packNames) {
    # Créer un sous-dossier pour chaque pack
    $packPath = Join-Path -Path $giftsPath -ChildPath $packName
    Create-Directory-If-Not-Exists -path $packPath

    # Créer le fichier JPG pour chaque pack
    Create-Empty-JPG -path $packPath -name $packName

    # Créer aussi le fichier dans le dossier principal pour compatibilité
    Create-Empty-JPG -path $giftsPath -name $packName
}

# Créer un fichier placeholder pour les cadeaux
Create-Empty-JPG -path $giftsPath -name "placeholder-gift"

# 3. Créer les dossiers manquants pour la page "Tous les produits"
Write-Host "Création des dossiers pour la page 'Tous les produits'..." -ForegroundColor Yellow

$productsPath = Join-Path -Path $imagesPath -ChildPath "products"
Create-Directory-If-Not-Exists -path $productsPath

# Créer les sous-dossiers pour chaque catégorie de produit
$productCategories = @{
    "mugs" = @(
        @{name = "classic-mug"; displayName = "Tasse Personnalisable Classique"},
        @{name = "magic-mug"; displayName = "Tasse Magique Thermosensible"},
        @{name = "isothermal-mug"; displayName = "Tasse Isotherme Inox"}
    )
    "tshirts" = @(
        @{name = "cotton-tshirt"; displayName = "T-shirt Personnalisable Coton"},
        @{name = "premium-tshirt"; displayName = "T-shirt Premium Impression Totale"},
        @{name = "polo-shirt"; displayName = "Polo Brodé Personnalisable"}
    )
    "mousepads" = @(
        @{name = "standard-mousepad"; displayName = "Tapis de Souris Standard"},
        @{name = "xxl-gamer-mousepad"; displayName = "Tapis de Souris XXL Gamer"},
        @{name = "ergonomic-mousepad"; displayName = "Tapis de Souris avec Repose-Poignet"}
    )
    "keychains" = @(
        @{name = "photo-keychain"; displayName = "Porte-clé Photo Personnalisable"},
        @{name = "metal-keychain"; displayName = "Porte-clé Métal Gravé"},
        @{name = "multifunction-keychain"; displayName = "Porte-clé Multifonction"}
    )
    "cushions" = @(
        @{name = "decorative-cushion"; displayName = "Coussin Décoratif Personnalisable"},
        @{name = "photo-cushion"; displayName = "Coussin Photo Recto-Verso"},
        @{name = "xxl-cushion"; displayName = "Coussin de Sol XXL"}
    )
}

foreach ($category in $productCategories.Keys) {
    $categoryPath = Join-Path -Path $productsPath -ChildPath $category
    Create-Directory-If-Not-Exists -path $categoryPath

    foreach ($product in $productCategories[$category]) {
        $productName = $product.name
        $productDisplayName = $product.displayName

        # Créer un sous-dossier pour chaque produit
        $productPath = Join-Path -Path $categoryPath -ChildPath $productName
        Create-Directory-If-Not-Exists -path $productPath

        # Créer le fichier PNG dans le sous-dossier
        Create-Empty-PNG -path $productPath -name $productName

        # Créer aussi le fichier dans le dossier de catégorie pour compatibilité
        Create-Empty-PNG -path $categoryPath -name $productName
    }
}

Write-Host "Organisation des dossiers d'images terminée!" -ForegroundColor Green
