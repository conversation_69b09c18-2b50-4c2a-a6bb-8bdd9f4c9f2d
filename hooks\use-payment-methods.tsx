"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { supabase<PERSON>pi, PaymentMethod } from "@/lib/supabase";
import { useToast } from "@/components/ui/use-toast";

// Valeurs par défaut pour les méthodes de paiement
const defaultPaymentMethods: PaymentMethod[] = [
  {
    id: "1",
    name: "Wave",
    description: "Paiement mobile via Wave",
    logo_url: "/images/payments/wave/logo/wave.png",
    qr_code_url: "/images/payments/wave/qr/wave-qr.png",
    is_active: true,
    display_order: 1,
    payment_details: { phone: "+2250709495849" },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "2",
    name: "Orange Money",
    description: "Paiement mobile via Orange Money",
    logo_url: "/images/payments/orange/logo/orange.png",
    qr_code_url: "/images/payments/orange/qr/orange-qr.png",
    is_active: true,
    display_order: 2,
    payment_details: { phone: "+2250709495848" },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

// Type pour le contexte
type PaymentMethodsContextType = {
  paymentMethods: PaymentMethod[];
  activePaymentMethods: PaymentMethod[];
  isLoading: boolean;
  error: Error | null;
  refreshPaymentMethods: () => Promise<void>;
  getPaymentMethodById: (id: string) => PaymentMethod | undefined;
};

// Créer le contexte
const PaymentMethodsContext = createContext<PaymentMethodsContextType | undefined>(undefined);

// Hook pour utiliser le contexte
export function usePaymentMethods() {
  const context = useContext(PaymentMethodsContext);
  if (context === undefined) {
    throw new Error("usePaymentMethods must be used within a PaymentMethodsProvider");
  }
  return context;
}

// Provider pour le contexte
export function PaymentMethodsProvider({ children }: { children: React.ReactNode }) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [activePaymentMethods, setActivePaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  // Fonction pour charger les méthodes de paiement
  const loadPaymentMethods = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await supabaseApi.paymentMethods.getAll();

      if (error) {
        throw new Error(`Erreur lors du chargement des méthodes de paiement: ${error.message}`);
      }

      if (!data || data.length === 0) {
        console.warn("Aucune méthode de paiement trouvée, utilisation des valeurs par défaut");
        setPaymentMethods(defaultPaymentMethods);
        setActivePaymentMethods(defaultPaymentMethods.filter(m => m.is_active));
        return;
      }

      setPaymentMethods(data);
      setActivePaymentMethods(data.filter(m => m.is_active));
    } catch (err) {
      console.error("Erreur lors du chargement des méthodes de paiement:", err);
      setError(err instanceof Error ? err : new Error(String(err)));
      toast({
        title: "Erreur",
        description: "Impossible de charger les méthodes de paiement",
        variant: "destructive",
      });

      // Utiliser les valeurs par défaut en cas d'erreur
      setPaymentMethods(defaultPaymentMethods);
      setActivePaymentMethods(defaultPaymentMethods.filter(m => m.is_active));
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour obtenir une méthode de paiement par son ID
  const getPaymentMethodById = (id: string): PaymentMethod | undefined => {
    return paymentMethods.find(m => m.id === id);
  };

  // Charger les méthodes de paiement au montage du composant
  useEffect(() => {
    loadPaymentMethods();
  }, []);

  return (
    <PaymentMethodsContext.Provider
      value={{
        paymentMethods,
        activePaymentMethods,
        isLoading,
        error,
        refreshPaymentMethods: loadPaymentMethods,
        getPaymentMethodById,
      }}
    >
      {children}
    </PaymentMethodsContext.Provider>
  );
}
