"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

interface ColorPickerProps {
  color: string
  onChange: (color: string) => void
  id?: string
}

export default function ColorPicker({ color, onChange, id }: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false)

  const presetColors = [
    "#000000",
    "#ffffff",
    "#ff0000",
    "#00ff00",
    "#0000ff",
    "#ffff00",
    "#ff00ff",
    "#00ffff",
    "#ff5555",
    "#55ff55",
    "#5555ff",
    "#ffff55",
    "#ff55ff",
    "#55ffff",
    "#555555",
    "#aaaaaa",
    "#800000",
    "#008000",
    "#000080",
    "#808000",
    "#800080",
    "#008080",
    "#ff8000",
    "#8000ff",
    "#0080ff",
  ]

  return (
    <div className="flex items-center space-x-2">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <button
            id={id}
            type="button"
            className="w-10 h-10 rounded-md border border-gray-300 shadow-sm"
            style={{ backgroundColor: color }}
            aria-label="Pick a color"
          />
        </PopoverTrigger>
        <PopoverContent className="w-64 p-3">
          <div className="space-y-3">
            <div className="grid grid-cols-5 gap-2">
              {presetColors.map((presetColor) => (
                <button
                  key={presetColor}
                  className="w-8 h-8 rounded-md border border-gray-300 shadow-sm"
                  style={{ backgroundColor: presetColor }}
                  onClick={() => {
                    onChange(presetColor)
                    setIsOpen(false)
                  }}
                  aria-label={`Select color ${presetColor}`}
                />
              ))}
            </div>
            <div>
              <Input type="color" value={color} onChange={(e) => onChange(e.target.value)} className="w-full h-8" />
            </div>
            <div className="flex items-center space-x-2">
              <div
                className="w-8 h-8 rounded-md border border-input shadow-sm"
                style={{ backgroundColor: color }}
              />
              <Input
                value={color}
                onChange={(e) => onChange(e.target.value)}
                className="h-9"
                placeholder="Code couleur hex"
                aria-label="Code couleur hexadécimal"
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
      <Input
        type="text"
        value={color}
        onChange={(e) => onChange(e.target.value)}
        className="w-24"
        placeholder="#000000"
      />
    </div>
  )
}
