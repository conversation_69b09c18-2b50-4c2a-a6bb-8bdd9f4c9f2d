import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  Truck,
  ShieldCheck,
  Recycle,
  Users,
  Star,
  ArrowRight,
  Instagram,
  Facebook,
  Twitter,
} from "lucide-react"

export default function AboutPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-purple-600 to-blue-500 text-white py-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">À propos de HCP-DESIGN CI</h1>
            <p className="text-xl mb-6">
              Nous créons des coques personnalisées uniques qui reflètent votre personnalité et protègent votre
              téléphone comme jamais.
            </p>
          </div>
        </div>
      </section>

      {/* Quick Info Cards */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 -mt-16">
            <Card className="shadow-lg">
              <CardContent className="p-6 flex items-start">
                <div className="bg-purple-100 p-3 rounded-full mr-4">
                  <MapPin className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-1">Notre Adresse</h3>
                  <p className="text-gray-600">Abidjan, Côte d'Ivoire</p>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg">
              <CardContent className="p-6 flex items-start">
                <div className="bg-purple-100 p-3 rounded-full mr-4">
                  <Phone className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-1">Téléphone</h3>
                  <p className="text-gray-600">+225 0709495848</p>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg">
              <CardContent className="p-6 flex items-start">
                <div className="bg-purple-100 p-3 rounded-full mr-4">
                  <Mail className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-1">Email</h3>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6">Notre Histoire</h2>
              <p className="text-gray-600 mb-4">
                Fondée en 2016 à Abidjan, HCP-DESIGN CI est née d'une passion pour l'art et la technologie. Nous avons
                commencé comme une petite entreprise locale avec une mission simple : permettre aux gens d'exprimer leur
                individualité à travers des coques de téléphone personnalisées, de tasse , de coussins, de porte-clés et plusieurs autres gadgets de haute qualité.
              </p>
              <p className="text-gray-600 mb-4">
                Au fil des ans, nous avons développé notre expertise en matière d'impression et de personnalisation, en
                investissant dans des technologies de pointe pour offrir des produits exceptionnels à nos clients.
              </p>
              <p className="text-gray-600">
                Aujourd'hui, nous sommes fiers de servir des clients dans toute la Côte d'Ivoire et au-delà, en
                continuant à innover et à créer des produits qui allient style, protection et personnalisation.
              </p>
            </div>
            <div className="md:w-1/2">
              <div className="grid grid-cols-1 gap-4">
                <div className="rounded-lg overflow-hidden">
                  <img
                    src="/placeholder.svg?height=300&width=600"
                    alt="Notre atelier"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="rounded-lg overflow-hidden">
                  <img
                    src="/placeholder.svg?height=300&width=600"
                    alt="Notre équipe"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Nos Valeurs</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Chez CoquesPerso, nous sommes guidés par des valeurs fortes qui définissent notre approche et notre
              engagement envers nos clients.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="bg-purple-100 p-4 rounded-full inline-flex mb-4">
                  <Star className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Qualité</h3>
                <p className="text-gray-600">
                  Nous utilisons uniquement des matériaux premium et des techniques d'impression avancées pour garantir
                  des produits durables et esthétiques.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="bg-purple-100 p-4 rounded-full inline-flex mb-4">
                  <Users className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Service Client</h3>
                <p className="text-gray-600">
                  Nous nous engageons à offrir une expérience client exceptionnelle, avec un support réactif et une
                  attention particulière à vos besoins.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="bg-purple-100 p-4 rounded-full inline-flex mb-4">
                  <Recycle className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Durabilité</h3>
                <p className="text-gray-600">
                  Nous nous efforçons de réduire notre impact environnemental en utilisant des matériaux recyclables et
                  des processus de production responsables.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="bg-purple-100 p-4 rounded-full inline-flex mb-4">
                  <ShieldCheck className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Fiabilité</h3>
                <p className="text-gray-600">
                  Nous tenons nos promesses en matière de délais de livraison, de qualité des produits et de
                  satisfaction client.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Our Process */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Notre Processus</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Découvrez comment nous transformons vos idées en coques personnalisées de haute qualité.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="relative">
              <div className="bg-white p-6 rounded-lg shadow-md text-center h-full">
                <div className="bg-purple-600 text-white w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-4">
                  1
                </div>
                <h3 className="font-semibold text-lg mb-2">Conception</h3>
                <p className="text-gray-600">
                  Utilisez notre outil de personnalisation en ligne pour créer votre design unique ou choisissez parmi
                  nos modèles.
                </p>
              </div>
              <div className="hidden md:block absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2">
                <ArrowRight className="h-6 w-6 text-purple-600" />
              </div>
            </div>

            <div className="relative">
              <div className="bg-white p-6 rounded-lg shadow-md text-center h-full">
                <div className="bg-purple-600 text-white w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-4">
                  2
                </div>
                <h3 className="font-semibold text-lg mb-2">Production</h3>
                <p className="text-gray-600">
                  Nos techniciens préparent votre design et utilisent notre équipement de pointe pour l'imprimer sur
                  votre coque.
                </p>
              </div>
              <div className="hidden md:block absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2">
                <ArrowRight className="h-6 w-6 text-purple-600" />
              </div>
            </div>

            <div className="relative">
              <div className="bg-white p-6 rounded-lg shadow-md text-center h-full">
                <div className="bg-purple-600 text-white w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-4">
                  3
                </div>
                <h3 className="font-semibold text-lg mb-2">Contrôle Qualité</h3>
                <p className="text-gray-600">
                  Chaque coque est minutieusement inspectée pour garantir une qualité d'impression parfaite et une
                  finition impeccable.
                </p>
              </div>
              <div className="hidden md:block absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2">
                <ArrowRight className="h-6 w-6 text-purple-600" />
              </div>
            </div>

            <div>
              <div className="bg-white p-6 rounded-lg shadow-md text-center h-full">
                <div className="bg-purple-600 text-white w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-4">
                  4
                </div>
                <h3 className="font-semibold text-lg mb-2">Livraison</h3>
                <p className="text-gray-600">
                  Votre coque personnalisée est soigneusement emballée et expédiée rapidement à votre adresse.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Notre Équipe</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Rencontrez les personnes passionnées qui travaillent chaque jour pour créer vos coques personnalisées.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[
              { name: "Konan Kouadio", role: "Fondateur & CEO", image: "/images/placeholder.svg?height=300&width=300" },
              { name: "Aya Touré", role: "Directrice Artistique", image: "/images/placeholder.svg?height=300&width=300" },
              {
                name: "Mamadou Diallo",
                role: "Responsable Production",
                image: "/images/placeholder.svg?height=300&width=300",
              },
              { name: "Fatou Cissé", role: "Service Client", image: "/images/placeholder.svg?height=300&width=300" },
            ].map((member, index) => (
              <Card key={index}>
                <CardContent className="p-0">
                  <img
                    src={member.image || "/placeholder.svg"}
                    alt={member.name}
                    className="w-full aspect-square object-cover"
                  />
                  <div className="p-4 text-center">
                    <h3 className="font-semibold text-lg">{member.name}</h3>
                    <p className="text-gray-600">{member.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Questions Fréquentes</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Trouvez des réponses aux questions les plus courantes sur nos produits et services.
            </p>
          </div>

          <div className="max-w-3xl mx-auto">
            <Tabs defaultValue="products" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="products">Produits</TabsTrigger>
                <TabsTrigger value="orders">Commandes</TabsTrigger>
                <TabsTrigger value="shipping">Livraison</TabsTrigger>
              </TabsList>

              <TabsContent value="products" className="mt-6">
                <div className="space-y-4">
                  {[
                    {
                      question: "Quels types de coques proposez-vous ?",
                      answer:
                        "Nous proposons une large gamme de coques, notamment des coques rigides, des coques en silicone, des coques antichoc et des coques portefeuille. Toutes peuvent être personnalisées selon vos préférences.",
                    },
                    {
                      question: "Les coques protègent-elles vraiment mon téléphone ?",
                      answer:
                        "Oui, nos coques sont conçues pour offrir une protection optimale contre les chocs, les rayures et les chutes. Nous utilisons des matériaux de haute qualité qui absorbent les impacts tout en restant esthétiques.",
                    },
                    {
                      question: "Puis-je utiliser mes propres images pour personnaliser ma coque ?",
                      answer:
                        "Absolument ! Notre outil de personnalisation vous permet de télécharger vos propres photos, images ou designs. Assurez-vous simplement que vous possédez les droits d'utilisation des images.",
                    },
                  ].map((faq, index) => (
                    <Card key={index}>
                      <CardContent className="p-6">
                        <h3 className="font-semibold text-lg mb-2">{faq.question}</h3>
                        <p className="text-gray-600">{faq.answer}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="orders" className="mt-6">
                <div className="space-y-4">
                  {[
                    {
                      question: "Comment puis-je suivre ma commande ?",
                      answer:
                        "Une fois votre commande expédiée, vous recevrez un email contenant un numéro de suivi. Vous pourrez utiliser ce numéro sur notre site ou directement sur le site du transporteur pour suivre votre colis.",
                    },
                    {
                      question: "Puis-je modifier ma commande après l'avoir passée ?",
                      answer:
                        "Vous pouvez modifier votre commande dans les 2 heures suivant sa validation. Contactez-nous rapidement par email ou téléphone avec votre numéro de commande et les modifications souhaitées.",
                    },
                    {
                      question: "Quelle est votre politique de retour ?",
                      answer:
                        "Nous offrons une garantie de satisfaction de 30 jours. Si vous n'êtes pas satisfait de votre produit, vous pouvez le retourner dans son état d'origine pour un remboursement ou un échange.",
                    },
                  ].map((faq, index) => (
                    <Card key={index}>
                      <CardContent className="p-6">
                        <h3 className="font-semibold text-lg mb-2">{faq.question}</h3>
                        <p className="text-gray-600">{faq.answer}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="shipping" className="mt-6">
                <div className="space-y-4">
                  {[
                    {
                      question: "Quels sont les délais de livraison ?",
                      answer:
                        "Nos délais de livraison varient selon votre localisation. En général, comptez 2-3 jours ouvrables pour Abidjan, 3-5 jours pour le reste de la Côte d'Ivoire, et 7-14 jours pour l'international.",
                    },
                    {
                      question: "Livrez-vous à l'international ?",
                      answer:
                        "Oui, nous livrons dans plusieurs pays d'Afrique de l'Ouest et au-delà. Les frais de livraison et les délais varient selon la destination.",
                    },
                    {
                      question: "Les frais de livraison sont-ils inclus ?",
                      answer:
                        "Les frais de livraison sont calculés lors du processus de commande en fonction de votre adresse. Nous offrons la livraison gratuite pour les commandes supérieures à 30 000 FCFA en Côte d'Ivoire.",
                    },
                  ].map((faq, index) => (
                    <Card key={index}>
                      <CardContent className="p-6">
                        <h3 className="font-semibold text-lg mb-2">{faq.question}</h3>
                        <p className="text-gray-600">{faq.answer}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-16 bg-gradient-to-r from-purple-600 to-blue-500 text-white">
        <div className="container mx-auto px-4 md:px-6 text-center">
          <h2 className="text-3xl font-bold mb-4">Une question ? Contactez-nous !</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Notre équipe est disponible pour répondre à toutes vos questions et vous aider à créer la coque parfaite.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/contact">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                Nous contacter
              </Button>
            </Link>
            <Link href="/customize">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10">
                Personnaliser une coque
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Business Hours & Social Media */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* Business Hours */}
            <div>
              <h2 className="text-2xl font-bold mb-6">Heures d'ouverture</h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <Clock className="h-5 w-5 text-purple-600 mr-3 mt-0.5" />
                  <div>
                    <h3 className="font-semibold text-lg">Service Client</h3>
                    <p className="text-gray-600">Lundi - Vendredi: 8h00 - 18h00</p>
                    <p className="text-gray-600">Samedi: 9h00 - 15h00</p>
                    <p className="text-gray-600">Dimanche: Fermé</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Truck className="h-5 w-5 text-purple-600 mr-3 mt-0.5" />
                  <div>
                    <h3 className="font-semibold text-lg">Production & Expédition</h3>
                    <p className="text-gray-600">Lundi - Vendredi: 8h00 - 17h00</p>
                    <p className="text-gray-600">Samedi - Dimanche: Fermé</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Social Media */}
            <div>
              <h2 className="text-2xl font-bold mb-6">Suivez-nous</h2>
              <p className="text-gray-600 mb-6">
                Restez connecté avec nous sur les réseaux sociaux pour découvrir nos dernières créations, promotions et
                actualités.
              </p>
              <div className="flex space-x-4">
                <Link href="https://www.instagram.com/designshcp"target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" size="icon" className="rounded-full h-12 w-12">
                    <Instagram className="h-5 w-5" />
                  </Button>
                </Link>
                <Link href="https://www.facebook.com/HabillagesetCoquesPersonnalises" target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" size="icon" className="rounded-full h-12 w-12">
                    <Facebook className="h-5 w-5" />
                  </Button>
                </Link>
                <Link href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" size="icon" className="rounded-full h-12 w-12">
                    <Twitter className="h-5 w-5" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
