#!/bin/bash

# Script pour créer des fichiers placeholder.png dans tous les dossiers d'images qui n'en ont pas

# Chemin du placeholder générique
PLACEHOLDER="./public/images/placeholder.png"

# Vérifier que le placeholder existe
if [ ! -f "$PLACEHOLDER" ]; then
  echo "Erreur: Le fichier placeholder.png n'existe pas à l'emplacement $PLACEHOLDER"
  exit 1
fi

# Dossiers iPhone 16
for dir in ./public/images/phone-cases/iphone/iphone-16*; do
  if [ -d "$dir" ]; then
    if [ ! -f "$dir/placeholder.png" ]; then
      echo "Création du placeholder pour $dir"
      cp "$PLACEHOLDER" "$dir/placeholder.png"
    fi
  fi
done

# Dossiers Samsung S25
for dir in ./public/images/phone-cases/samsung/galaxy-s25*; do
  if [ -d "$dir" ]; then
    if [ ! -f "$dir/placeholder.png" ]; then
      echo "Création du placeholder pour $dir"
      cp "$PLACEHOLDER" "$dir/placeholder.png"
    fi
  fi
done

# Dossiers types de coques premium
for dir in ./public/images/phone-cases/types/premium* ./public/images/phone-cases/types/vip-1; do
  if [ -d "$dir" ]; then
    if [ ! -f "$dir/placeholder.png" ]; then
      echo "Création du placeholder pour $dir"
      cp "$PLACEHOLDER" "$dir/placeholder.png"
    fi
  fi
done

# Supprimer les anciens placeholders SVG s'ils existent
find ./public/images/phone-cases -name "placeholder.svg" -type f -delete

echo "Création des placeholders PNG terminée"
