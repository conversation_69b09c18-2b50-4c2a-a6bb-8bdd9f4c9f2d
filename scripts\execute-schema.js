// Ce script est destiné à être exécuté manuellement pour appliquer le schéma SQL à Supabase
// Utilisez-le uniquement si vous ne pouvez pas exécuter le script SQL directement dans l'interface Supabase

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Charger les variables d'environnement depuis .env.local
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Les variables d\'environnement NEXT_PUBLIC_SUPABASE_URL et NEXT_PUBLIC_SUPABASE_ANON_KEY doivent être définies.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Lire le fichier SQL
const schemaPath = path.join(__dirname, '..', 'supabase', 'schema.sql');
const schemaSql = fs.readFileSync(schemaPath, 'utf8');

// Diviser le script en instructions individuelles
const statements = schemaSql
  .split(';')
  .map(statement => statement.trim())
  .filter(statement => statement.length > 0);

// Exécuter chaque instruction
async function executeSchema() {
  console.log(`Exécution de ${statements.length} instructions SQL...`);
  
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];
    console.log(`Exécution de l'instruction ${i + 1}/${statements.length}`);
    
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' });
      
      if (error) {
        console.error(`Erreur lors de l'exécution de l'instruction ${i + 1}:`, error);
      }
    } catch (err) {
      console.error(`Exception lors de l'exécution de l'instruction ${i + 1}:`, err);
    }
  }
  
  console.log('Exécution terminée.');
}

executeSchema().catch(console.error);
