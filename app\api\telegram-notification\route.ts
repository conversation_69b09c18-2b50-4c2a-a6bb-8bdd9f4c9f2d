import { NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/auth-server';

/**
 * API pour envoyer une notification Telegram
 * POST /api/telegram-notification
 */
export async function POST(request: Request) {
  try {
    const supabase = createServerSupabaseClient();
    
    // Vérifier l'authentification
    const { data: { session } } = await supabase.auth.getSession();
    
    // Récupérer les données de la requête
    const body = await request.json();
    const { message, notificationName = 'order_notification', parseMode = 'Markdown' } = body;
    
    // Validation des données
    if (!message) {
      return NextResponse.json(
        { success: false, error: 'Le message est requis' },
        { status: 400 }
      );
    }
    
    // Appeler la fonction Supabase pour envoyer la notification
    const { data, error } = await supabase.rpc('send_telegram_notification', {
      notification_name: notificationName,
      message,
      parse_mode: parseMode
    });
    
    if (error) {
      console.error('Erreur lors de l\'envoi de la notification Telegram:', error);
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true, data });
    
  } catch (error: any) {
    console.error('Erreur lors du traitement de la requête:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Une erreur est survenue' },
      { status: 500 }
    );
  }
}
