"use client"

// Ajoutez cette ligne pour forcer le rechargement du composant
// @ts-ignore
// export const dynamic = 'force-dynamic'
// export const revalidate = 0

import { useState, useEffect } from "react"
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Smartphone, ChevronDown, ChevronUp, ArrowRight } from "lucide-react"
import Link from "next/link"
import { phoneData, upcomingModels } from "@/data/phone-models"
import { Separator } from "@/components/ui/separator"
import BrandTabs from "@/components/brand-tabs"

// Définition complète de la fonction ModelsPage

// Version: 2025-05-15T00-32-54-717Z - Force redeploy

export default function ModelsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [expandedBrands, setExpandedBrands] = useState<Record<string, boolean>>({})
  const [expandedSeries, setExpandedSeries] = useState<Record<string, string | null>>({})

  // Ajoutez un useEffect pour charger les données si nécessaire
  useEffect(() => {
    // Fonction pour charger les données
    const loadData = async () => {
      try {
        // Rechargez les données ici si nécessaire
        console.log("Chargement des données des modèles");
        // Exemple: const data = await fetchModelsFromAPI();
        // setModels(data);
      } catch (error) {
        console.error("Erreur lors du chargement des modèles:", error);
      }
    };

    loadData();
  }, []);

  // Filtrer les modèles en fonction de la recherche
  const getFilteredModels = () => {
    if (!searchQuery) return null

    const allModels = Object.values(phoneData).flatMap((brand) =>
      brand.models.map((model) => ({
        ...model,
        brand: brand.name,
      })),
    )

    return allModels.filter(
      (model) =>
        model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.brand.toLowerCase().includes(searchQuery.toLowerCase()),
    )
  }

  const filteredModels = getFilteredModels()

  // Fonction pour basculer l'état d'expansion d'une marque
  const toggleBrand = (brandKey: string) => {
    setExpandedBrands({
      ...expandedBrands,
      [brandKey]: !expandedBrands[brandKey],
    })
  }

  // Fonction pour basculer l'état d'expansion d'une série
  const toggleSeries = (brandKey: string, series: string) => {
    setExpandedSeries({
      ...expandedSeries,
      [brandKey]: expandedSeries[brandKey] === series ? null : series,
    })
  }

  // Fonction pour regrouper les modèles par série
  const getModelsBySeries = (brandKey: string, models: any[]) => {
    // Créer un objet pour stocker les séries déjà traitées
    const processedSeries: Record<string, boolean> = {};

    return models.reduce((acc, model) => {
      // Extraire la série du nom du modèle
      let series = "";

      if (brandKey === "apple") {
        // Pour iPhone, extraire le numéro de série (iPhone 15, iPhone 14, etc.)
        const match = model.name.match(/iPhone (\d+)/);
        if (match) {
          series = `Série ${match[1]}`;
        } else if (model.name.includes("SE")) {
          series = "Série SE";
        } else {
          series = "Autres séries";
        }
      } else if (brandKey === "samsung") {
        // Pour Samsung, extraire la série (S25, S24, A54, etc.) en utilisant des expressions régulières
        if (model.name.includes("Galaxy S")) {
          const match = model.name.match(/Galaxy S(\d+)/);
          if (match) {
            series = `Série S${match[1]}`;
          } else {
            series = "Série S";
          }
        } else if (model.name.includes("Galaxy A")) {
          const match = model.name.match(/Galaxy A(\d+)/);
          if (match) {
            series = `Série A${match[1]}`;
          } else {
            series = "Série A";
          }
        } else {
          series = "Autres séries";
        }
      } else if (brandKey === "google") {
        // Pour Google, extraire la série (Pixel 8, Pixel 7, etc.)
        const match = model.name.match(/Pixel (\d+)/);
        if (match) {
          series = `Série Pixel ${match[1]}`;
        } else {
          series = "Autres séries Pixel";
        }
      } else if (brandKey === "xiaomi") {
        // Pour Xiaomi, extraire la série (Xiaomi 13, Redmi Note 12, etc.)
        if (model.name.includes("Xiaomi")) {
          const match = model.name.match(/Xiaomi (\d+)/);
          if (match) {
            series = `Série ${match[1]}`;
          } else {
            series = "Autres séries Xiaomi";
          }
        } else if (model.name.includes("Redmi")) {
          const match = model.name.match(/Redmi Note (\d+)/);
          if (match) {
            series = `Série Note ${match[1]}`;
          } else {
            series = "Autres séries Redmi";
          }
        } else {
          series = "Autres séries";
        }
      } else if (brandKey === "huawei") {
        // Pour Huawei, extraire la série (P60, etc.)
        const match = model.name.match(/P(\d+)/);
        if (match) {
          series = `Série P${match[1]}`;
        } else {
          series = "Autres séries";
        }
      } else {
        series = "Autres modèles";
      }

      // Vérifier si cette série existe déjà
      if (!acc[series]) {
        acc[series] = [];
        processedSeries[series] = true;
      }

      acc[series].push(model);
      return acc;
    }, {} as Record<string, any[]>);
  };

  // Trier les séries par ordre décroissant (les plus récentes d'abord)
  const sortSeries = (series: string[]) => {
    return series.sort((a, b) => {
      // Extraire les numéros des séries pour les comparer
      const numA = parseInt(a.match(/\d+/)?.[0] || "0");
      const numB = parseInt(b.match(/\d+/)?.[0] || "0");
      return numB - numA;
    });
  };

  return (
    <main className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-2">Modèles Compatibles</h1>
        <p className="text-lg opacity-90">
          Découvrez tous les modèles de téléphones compatibles avec nos coques personnalisées
        </p>
      </div>

      {/* Brand Tabs */}
      <div className="mb-6">
        <BrandTabs
          activeBrand="apple"
          onSelectBrand={(brandKey) => {
            setExpandedBrands({
              ...expandedBrands,
              [brandKey]: true
            });
          }}
        />
      </div>

      {/* Search Bar */}
      <div className="relative max-w-md mx-auto mb-8">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Rechercher un modèle..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* Search Results */}
      {searchQuery && filteredModels && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Résultats de recherche pour "{searchQuery}"</h2>
          {filteredModels.length > 0 ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {filteredModels.map((model) => (
                <Card key={model.id} className="overflow-hidden hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center">
                      <div className="text-xs text-gray-500 mb-2">{model.brand}</div>
                      <div className="h-32 mb-2 flex items-center justify-center">
                        <img
                          src={model.image || "/placeholder.png"}
                          alt={model.name}
                          className="h-full object-contain"
                        />
                      </div>
                      <h3 className="font-medium text-center">{model.name}</h3>
                      <div className="mt-3 w-full">
                        <Link href={`/customize?model=${model.id}`}>
                          <Button className="w-full bg-purple-600 hover:bg-purple-700">Personnaliser</Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <Smartphone className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <h3 className="text-lg font-medium mb-1">Aucun modèle trouvé</h3>
              <p className="text-gray-500">Essayez avec un autre terme de recherche</p>
            </div>
          )}
        </div>
      )}

      {/* Marques avec menus déroulants */}
      {!searchQuery && (
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-6">Marques disponibles</h2>

          <div className="space-y-4">
            {Object.entries(phoneData).map(([brandKey, brand]) => {
              const modelsBySeries = getModelsBySeries(brandKey, brand.models);
              const sortedSeries = sortSeries(Object.keys(modelsBySeries));

              return (
                <Card key={brandKey} id={brandKey} className="overflow-hidden">
                  <div
                    className="p-4 flex items-center justify-between cursor-pointer border-b"
                    onClick={() => toggleBrand(brandKey)}
                  >
                    <div className="flex items-center gap-3">
                      <img src={brand.logo || "/placeholder.png"} alt={brand.name} className="w-8 h-8" />
                      <h3 className="font-bold text-lg">{brand.name}</h3>
                    </div>
                    <Button variant="ghost" size="sm">
                      {expandedBrands[brandKey] ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
                    </Button>
                  </div>

                  {expandedBrands[brandKey] && (
                    <CardContent className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {sortedSeries.map((series) => (
                          <div key={`${brandKey}-${series}`} className="border rounded-lg p-3">
                            <div
                              className="flex items-center justify-between cursor-pointer"
                              onClick={() => toggleSeries(brandKey, series)}
                            >
                              <h4 className="font-medium">{series}</h4>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                {expandedSeries[brandKey] === series ?
                                  <ChevronUp className="h-4 w-4" /> :
                                  <ChevronDown className="h-4 w-4" />
                                }
                              </Button>
                            </div>

                            {expandedSeries[brandKey] === series && (
                              <div className="mt-2 space-y-1">
                                {modelsBySeries[series].map((model) => (
                                  <Link
                                    href={`/customize?model=${model.id}`}
                                    key={model.id}
                                    className="block"
                                  >
                                    <div className="flex items-center justify-between p-2 hover:bg-gray-100 rounded-md">
                                      <span className="text-sm">{model.name}</span>
                                      <div className="flex gap-1">
                                        {model.availableQualities?.map((quality: string) => (
                                          <Badge key={quality} variant="outline" className="text-xs">
                                            {quality}
                                          </Badge>
                                        ))}
                                        {model.popular && (
                                          <Badge className="text-xs bg-purple-600">
                                            Populaire
                                          </Badge>
                                        )}
                                      </div>
                                    </div>
                                  </Link>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  )}
                </Card>
              );
            })}
          </div>
        </div>
      )}

      <Separator className="my-8" />

      {/* Qualités disponibles */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-6">Qualités disponibles</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-2">Vip-1</h3>
              <p className="text-gray-600 mb-4">Notre qualité standard disponible pour tous les modèles</p>
              <ul className="list-disc pl-5 space-y-1 mb-4">
                <li>Protection optimale</li>
                <li>Impression haute définition</li>
                <li>Compatible avec tous les modèles</li>
                <li>Finition mate ou brillante</li>
              </ul>
              <Badge className="bg-green-100 text-green-800">Disponible pour tous les modèles</Badge>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-2">Premium</h3>
              <p className="text-gray-600 mb-4">Notre qualité supérieure pour les iPhone (11 à 16)</p>
              <ul className="list-disc pl-5 space-y-1 mb-4">
                <li>Protection renforcée</li>
                <li>Impression ultra HD</li>
                <li>Matériaux écologiques</li>
                <li>Finition soft touch</li>
              </ul>
              <Badge className="bg-blue-100 text-blue-800">iPhone 11 à 16 Pro Max</Badge>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-2">Premium MagSafe</h3>
              <p className="text-gray-600 mb-4">Compatible avec la recharge MagSafe d'Apple</p>
              <ul className="list-disc pl-5 space-y-1 mb-4">
                <li>Aimants intégrés</li>
                <li>Compatible recharge sans fil</li>
                <li>Protection renforcée</li>
                <li>Impression ultra HD</li>
              </ul>
              <Badge className="bg-purple-100 text-purple-800">iPhone 12 à 16 Pro Max</Badge>
            </CardContent>
          </Card>
        </div>
      </div>

      <Separator className="my-8" />

      {/* Upcoming Models */}
      <div className="mt-12 bg-gray-50 rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-6">Modèles à venir</h2>
        <p className="text-gray-600 mb-6">
          Nous préparons déjà des coques pour ces modèles qui sortiront prochainement. Inscrivez-vous à notre newsletter
          pour être informé dès leur disponibilité.
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
          {upcomingModels.map((model) => (
            <Card key={model.id} className="overflow-hidden border-dashed border-2 border-gray-300">
              <CardContent className="p-4">
                <div className="flex flex-col items-center">
                  <Badge className="mb-2 bg-blue-500">À venir</Badge>
                  <div className="h-32 mb-2 flex items-center justify-center opacity-70">
                    <img src={model.image || "/placeholder.png"} alt={model.name} className="h-full object-contain" />
                  </div>
                  <h3 className="font-medium text-center">{model.name}</h3>
                  <div className="text-sm text-gray-500 text-center mt-1">{model.brand}</div>
                  <div className="text-sm font-medium text-purple-600 text-center mt-2">
                    Sortie prévue: {model.expectedRelease}
                  </div>
                  <Button variant="outline" className="mt-3 w-full" disabled>
                    Bientôt disponible
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* FAQ Section */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Questions fréquentes</h2>

        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-2">Mon modèle n'est pas listé ?</h3>
              <p className="text-gray-600 mb-4">
                Nous ajoutons régulièrement de nouveaux modèles. Si le vôtre n'est pas disponible, contactez-nous et
                nous ferons notre possible pour l'ajouter rapidement.
              </p>
              <Link href="/contact">
                <Button variant="outline" className="w-full">
                  Contactez-nous
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-2">Comment mesurer mon téléphone ?</h3>
              <p className="text-gray-600 mb-4">
                Si vous n'êtes pas sûr de votre modèle, vous pouvez mesurer les dimensions de votre téléphone et les
                comparer avec celles indiquées sur notre site.
              </p>
              <Link href="/guide-mesure">
                <Button variant="outline" className="w-full">
                  Guide de mesure
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* CTA Section */}
      <div className="mt-12 bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Prêt à créer votre coque personnalisée ?</h2>
        <p className="text-lg mb-6 max-w-2xl mx-auto">
          Choisissez votre modèle et commencez à concevoir une coque unique qui vous ressemble.
        </p>
        <Link href="/customize">
          <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
            Commencer à personnaliser <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </Link>
      </div>
    </main>
  )
}
