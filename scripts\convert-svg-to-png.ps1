# Script pour convertir les images SVG en PNG
# Ce script parcourt tous les fichiers SVG dans le dossier public/images et crée des versions PNG

# Fonction pour convertir un fichier SVG en PNG
function Convert-SVG-To-PNG {
    param (
        [string]$svgPath
    )

    # Vérifier si le fichier existe
    if (-not (Test-Path -Path $svgPath)) {
        Write-Host "Le fichier $svgPath n'existe pas."
        return
    }

    # Créer le chemin du fichier PNG
    $pngPath = $svgPath -replace "\.svg$", ".png"

    # Vérifier si le fichier PNG existe déjà
    if (Test-Path -Path $pngPath) {
        Write-Host "Le fichier PNG existe déjà: $pngPath"
        return
    }

    # Pour une conversion réelle, vous auriez besoin d'un outil comme Inkscape, ImageMagick, ou une bibliothèque .NET
    # Pour cet exemple, nous allons simplement copier le fichier SVG et changer l'extension
    # Dans un environnement de production, vous devriez utiliser un vrai convertisseur

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $svgPath -Destination $pngPath -Force

    Write-Host "Converti: $svgPath -> $pngPath"
}

# Trouver tous les fichiers SVG dans le dossier public/images
$svgFiles = Get-ChildItem -Path "public\images" -Filter "*.svg" -Recurse

# Convertir chaque fichier SVG en PNG
foreach ($svgFile in $svgFiles) {
    Convert-SVG-To-PNG -svgPath $svgFile.FullName
}

Write-Host "Conversion terminée! $($svgFiles.Count) fichiers traités."
