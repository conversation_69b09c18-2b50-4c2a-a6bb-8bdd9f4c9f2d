// Script pour vérifier les tables Supabase existantes
const { createClient } = require('@supabase/supabase-js');

// Utiliser les valeurs de fallback de lib/supabase.ts
const supabaseUrl = 'https://bekwlxorzlyoudsnajsn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJla3dseG9yemx5b3Vkc25hanNuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NzUzOTgsImV4cCI6MjA2MDU1MTM5OH0.yRKV3bo0ww0GIW1LtBB0HJQQErVWHbbbpkWVcFScS34';

// Créer le client Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

// Fonction pour vérifier si une table existe
async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
    
    if (error) {
      if (error.code === '42P01') {
        console.log(`Table '${tableName}' n'existe pas.`);
        return false;
      } else {
        console.error(`Erreur lors de la vérification de la table '${tableName}':`, error);
        return false;
      }
    } else {
      console.log(`Table '${tableName}' existe.`);
      return true;
    }
  } catch (err) {
    console.error(`Exception lors de la vérification de la table '${tableName}':`, err);
    return false;
  }
}

// Liste des tables à vérifier (basée sur le schéma SQL)
const tablesToCheck = [
  'profiles',
  'products',
  'cart_items',
  'orders',
  'order_items',
  'phone_models',
  'phone_cases',
  'site_settings',
  'verification_codes',
  'gallery_variants',
  'payment_methods'
];

// Vérifier toutes les tables
async function checkAllTables() {
  console.log('Vérification des tables Supabase...');
  
  const results = {};
  
  for (const tableName of tablesToCheck) {
    results[tableName] = await checkTableExists(tableName);
  }
  
  console.log('\nRésumé:');
  console.log('-------');
  
  const existingTables = Object.keys(results).filter(table => results[table]);
  const missingTables = Object.keys(results).filter(table => !results[table]);
  
  console.log('Tables existantes:', existingTables.join(', ') || 'Aucune');
  console.log('Tables manquantes:', missingTables.join(', ') || 'Aucune');
  
  return {
    existingTables,
    missingTables
  };
}

// Exécuter la vérification
checkAllTables()
  .then(({ missingTables }) => {
    if (missingTables.length > 0) {
      console.log('\nDes tables sont manquantes. Veuillez exécuter le script schema.sql pour les créer.');
    } else {
      console.log('\nToutes les tables nécessaires existent.');
    }
  })
  .catch(err => {
    console.error('Erreur lors de la vérification des tables:', err);
  });
