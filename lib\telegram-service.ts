/**
 * Service pour envoyer des notifications Telegram
 */

/**
 * Options pour l'envoi de notification Telegram
 */
interface TelegramNotificationOptions {
  notificationName?: string;
  parseMode?: 'Markdown' | 'HTML';
}

/**
 * Envoie une notification Telegram
 * @param message Le message à envoyer
 * @param options Options supplémentaires
 * @returns Résultat de l'opération
 */
export async function sendTelegramNotification(
  message: string,
  options: TelegramNotificationOptions = {}
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const { notificationName = 'order_notification', parseMode = 'Markdown' } = options;
    
    const response = await fetch('/api/telegram-notification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        notificationName,
        parseMode,
      }),
    });
    
    const result = await response.json();
    
    if (!response.ok) {
      console.error('Erreur lors de l\'envoi de la notification Telegram:', result.error);
      return { success: false, error: result.error || 'Erreur lors de l\'envoi de la notification' };
    }
    
    return { success: true, data: result.data };
  } catch (error: any) {
    console.error('Exception lors de l\'envoi de la notification Telegram:', error);
    return { success: false, error: error.message || 'Une erreur est survenue' };
  }
}

/**
 * Formate un message de commande pour Telegram
 * @param orderData Données de la commande
 * @returns Message formaté
 */
export function formatOrderMessage(orderData: any): string {
  return `
*🛒 Nouvelle Commande HCP Design*

*👤 Client*
Nom: *${orderData.name || orderData.customer_name || 'Non spécifié'}*
Téléphone: *${orderData.phone || orderData.customer_phone || 'Non spécifié'}*

*📦 Articles Commandés*
${orderData.items?.map((item: any) => 
  `- ${item.quantity}x ${item.name} (${item.price} Fr)`
).join('\n') || 'Aucun article'}

*💰 Paiement*
Montant Total: *${orderData.totalAmount || orderData.total} Fr*
${orderData.advancePayment ? `Avance Payée: *${orderData.advancePayment} Fr*` : ''}
${orderData.remainingAmount ? `Reste à Payer: *${orderData.remainingAmount} Fr*` : ''}
Méthode de Paiement: *${orderData.paymentMethod || orderData.payment_method}*

*🚚 Livraison*
Ville: *${orderData.deliveryCity || orderData.shipping_city}*
Adresse: *${orderData.deliveryAddress || orderData.shipping_address}*

*📝 Instructions Spéciales*
${orderData.specialInstructions || orderData.shipping_notes || 'Aucune'}

*⏱️ Date de Commande*
${new Date().toLocaleString('fr-FR', { timeZone: 'Africa/Abidjan' })}
`;
}
