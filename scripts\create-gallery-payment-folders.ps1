# Script pour créer les dossiers des variantes de la galerie et des services de paiement
# Ce script crée la structure des dossiers et génère des images placeholder

# Fonction pour créer un dossier s'il n'existe pas
function Create-Directory-If-Not-Exists {
    param (
        [string]$path
    )

    if (-not (Test-Path -Path $path)) {
        New-Item -Path $path -ItemType Directory -Force | Out-Null
        Write-Host "Créé: $path"
    } else {
        Write-Host "Existe déjà: $path"
    }
}

# Fonction pour créer un PNG placeholder
function Create-PNG-Placeholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title,
        [string]$subtitle
    )

    # Créer le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Vérifier si le fichier existe déjà
    if (Test-Path -Path $filePath) {
        Write-Host "Le fichier $filePath existe déjà."
        return
    }

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="600" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="500" height="300" rx="5" fill="#E5E7EB"/>
  <text x="300" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="300" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force
    
    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force
    
    Write-Host "Créé: $filePath"
}

# Fonction pour créer un PNG placeholder pour les logos de paiement
function Create-Payment-Logo-Placeholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title
    )

    # Créer le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Vérifier si le fichier existe déjà
    if (Test-Path -Path $filePath) {
        Write-Host "Le fichier $filePath existe déjà."
        return
    }

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="200" height="100" viewBox="0 0 200 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="200" height="100" rx="10" fill="#F3F4F6"/>
  <rect x="20" y="20" width="160" height="60" rx="5" fill="#E5E7EB"/>
  <text x="100" y="55" font-family="Arial" font-size="24" text-anchor="middle" fill="#6B7280">$title</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force
    
    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force
    
    Write-Host "Créé: $filePath"
}

# Fonction pour créer un PNG placeholder pour les codes QR
function Create-QR-Code-Placeholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title
    )

    # Créer le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Vérifier si le fichier existe déjà
    if (Test-Path -Path $filePath) {
        Write-Host "Le fichier $filePath existe déjà."
        return
    }

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="300" height="300" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="200" height="200" rx="5" fill="#E5E7EB"/>
  <text x="150" y="150" font-family="Arial" font-size="24" text-anchor="middle" fill="#6B7280">$title QR</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force
    
    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force
    
    Write-Host "Créé: $filePath"
}

# Dossier racine des images
$imagesRoot = "public\images"

# 1. Créer des dossiers pour les variantes de la galerie
$galleryVariants = @(
    "vagues-abstraites",
    "fleurs-tropicales",
    "galaxie-cosmique",
    "marbre-elegant",
    "retro-synthwave",
    "montagnes-minimalistes",
    "motif-geometrique",
    "neon-urbain",
    "mandala-zen",
    "animaux-polygonaux",
    "typographie-creative"
)

# Créer le dossier gallery/variants s'il n'existe pas
$galleryVariantsPath = "$imagesRoot\gallery\variants"
Create-Directory-If-Not-Exists -path $galleryVariantsPath

foreach ($variant in $galleryVariants) {
    $variantPath = "$galleryVariantsPath\$variant"
    Create-Directory-If-Not-Exists -path $variantPath
    
    # Convertir le nom du dossier en titre lisible
    $variantTitle = $variant -replace "-", " " -replace "\b(\w)", { $args[0].Groups[1].Value.ToUpper() }
    
    # Créer une image placeholder pour chaque variante
    Create-PNG-Placeholder -folderPath $variantPath -fileName $variant -title $variantTitle -subtitle "Variante de design"
}

# 2. Créer des dossiers pour les services de paiement
$paymentServices = @(
    "wave",
    "orange"
)

# Créer le dossier payments s'il n'existe pas
$paymentsPath = "$imagesRoot\payments"
Create-Directory-If-Not-Exists -path $paymentsPath

foreach ($service in $paymentServices) {
    $servicePath = "$paymentsPath\$service"
    Create-Directory-If-Not-Exists -path $servicePath
    
    # Créer des sous-dossiers pour les logos et codes QR
    $logoPath = "$servicePath\logo"
    $qrPath = "$servicePath\qr"
    
    Create-Directory-If-Not-Exists -path $logoPath
    Create-Directory-If-Not-Exists -path $qrPath
    
    # Convertir le nom du service en titre lisible
    $serviceTitle = $service.Substring(0,1).ToUpper() + $service.Substring(1)
    
    # Créer des images placeholder pour les logos et codes QR
    Create-Payment-Logo-Placeholder -folderPath $logoPath -fileName $service -title "$serviceTitle Pay"
    Create-QR-Code-Placeholder -folderPath $qrPath -fileName "$service-qr" -title $serviceTitle
}

Write-Host "Création des dossiers terminée !"
