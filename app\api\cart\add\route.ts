import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'
import { v4 as uuidv4 } from 'uuid'

// Initialiser le client Supabase
function getSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase environment variables')
  }

  return createClient(supabaseUrl, supabaseKey)
}

export async function POST(request: Request) {
  try {
    const supabase = getSupabaseClient()
    const { productId, quantity, color, price } = await request.json()
    
    if (!productId || !quantity || !color || !price) {
      return NextResponse.json(
        { error: 'Données manquantes' },
        { status: 400 }
      )
    }

    // Récupérer ou créer un ID de session
    const cookieStore = await cookies()
    let sessionId = cookieStore.get('session_id')?.value
    
    if (!sessionId) {
      sessionId = `session_${uuidv4()}`
      cookieStore.set('session_id', sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7 // 7 jours
      })
    }

    // Pour Supabase, nous n'avons pas besoin de table panier séparée
    // Les items sont directement liés au user_id

    // Vérifier si l'item existe déjà dans le panier
    const { data: existingItem } = await supabase
      .from('cart_items')
      .select('id, quantity')
      .eq('user_id', sessionId)
      .eq('product_id', productId)
      .single()

    if (existingItem) {
      // Mettre à jour la quantité
      const { error: updateError } = await supabase
        .from('cart_items')
        .update({ quantity: existingItem.quantity + quantity })
        .eq('id', existingItem.id)

      if (updateError) throw updateError
    } else {
      // Ajouter un nouvel item
      const { error: insertError } = await supabase
        .from('cart_items')
        .insert({
          user_id: sessionId,
          product_id: productId,
          quantity: quantity,
          customized: false
        })

      if (insertError) throw insertError
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Erreur lors de l\'ajout au panier:', error)
    return NextResponse.json(
      { error: 'Erreur lors de l\'ajout au panier' },
      { status: 500 }
    )
  }
}