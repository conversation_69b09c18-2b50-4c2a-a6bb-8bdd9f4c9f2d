-- Script pour créer les tables nécessaires au panier dans Supabase
-- Exécutez ce script dans la console SQL de Supabase si vous rencontrez des erreurs liées aux tables manquantes

-- Vérifier si la table cart_items existe, sinon la créer
CREATE TABLE IF NOT EXISTS cart_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL,
  product_id TEXT NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  customized BOOLEAN DEFAULT FALSE,
  customization_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Contrainte d'unicité simplifiée
  CONSTRAINT cart_items_user_id_product_id_unique UNIQUE (user_id, product_id, customized)
);

-- Ajouter des index pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS cart_items_user_id_idx ON cart_items (user_id);
CREATE INDEX IF NOT EXISTS cart_items_product_id_idx ON cart_items (product_id);

-- Ajouter des politiques RLS (Row Level Security) pour sécuriser les données
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre à un utilisateur de voir uniquement ses propres articles
DROP POLICY IF EXISTS cart_items_select_policy ON cart_items;
CREATE POLICY cart_items_select_policy ON cart_items
  FOR SELECT
  USING (
    -- Permettre l'accès aux articles de l'utilisateur connecté ou aux articles anonymes avec l'ID de session
    (auth.uid() IS NOT NULL AND auth.uid()::text = user_id) OR
    (auth.uid() IS NULL AND user_id LIKE 'anonymous_%') OR
    (user_id LIKE 'anonymous_%')
  );

-- Politique pour permettre à un utilisateur d'insérer uniquement ses propres articles
DROP POLICY IF EXISTS cart_items_insert_policy ON cart_items;
CREATE POLICY cart_items_insert_policy ON cart_items
  FOR INSERT
  WITH CHECK (
    -- Permettre l'insertion d'articles pour l'utilisateur connecté ou pour les sessions anonymes
    (auth.uid() IS NOT NULL AND auth.uid()::text = user_id) OR
    (auth.uid() IS NULL AND user_id LIKE 'anonymous_%') OR
    (user_id LIKE 'anonymous_%')
  );

-- Politique pour permettre à un utilisateur de mettre à jour uniquement ses propres articles
DROP POLICY IF EXISTS cart_items_update_policy ON cart_items;
CREATE POLICY cart_items_update_policy ON cart_items
  FOR UPDATE
  USING (
    -- Permettre la mise à jour des articles de l'utilisateur connecté ou des articles anonymes avec l'ID de session
    (auth.uid() IS NOT NULL AND auth.uid()::text = user_id) OR
    (auth.uid() IS NULL AND user_id LIKE 'anonymous_%') OR
    (user_id LIKE 'anonymous_%')
  );

-- Politique pour permettre à un utilisateur de supprimer uniquement ses propres articles
DROP POLICY IF EXISTS cart_items_delete_policy ON cart_items;
CREATE POLICY cart_items_delete_policy ON cart_items
  FOR DELETE
  USING (
    -- Permettre la suppression des articles de l'utilisateur connecté ou des articles anonymes avec l'ID de session
    (auth.uid() IS NOT NULL AND auth.uid()::text = user_id) OR
    (auth.uid() IS NULL AND user_id LIKE 'anonymous_%') OR
    (user_id LIKE 'anonymous_%')
  );

-- Activer l'accès public à la table cart_items (nécessaire pour les utilisateurs anonymes)
GRANT SELECT, INSERT, UPDATE, DELETE ON cart_items TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON cart_items TO authenticated;
GRANT USAGE ON SEQUENCE cart_items_id_seq TO anon;
GRANT USAGE ON SEQUENCE cart_items_id_seq TO authenticated;
