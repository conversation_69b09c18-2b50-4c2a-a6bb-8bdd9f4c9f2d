"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import { useFavorites } from "@/hooks/use-favorites"
import { useToast } from "@/components/ui/use-toast"
import { ShoppingCart, Heart } from "lucide-react"

export default function DebugCartFavorites() {
  const { toast } = useToast()
  const [cartState, setCartState] = useState<string>("Non initialisé")
  const [favoritesState, setFavoritesState] = useState<string>("Non initialisé")
  
  // Récupérer les hooks
  const cart = useSimpleCart()
  const favorites = useFavorites()
  
  // Produit de test
  const testProduct = {
    id: "test-123",
    name: "Produit de test",
    price: 5000,
    image_url: "/images/products/placeholder.png",
    category: "Test",
    subcategory: "Debug",
    is_new: true,
    is_bestseller: false,
    created_at: new Date().toISOString()
  }
  
  // Vérifier l'état des hooks au chargement
  useEffect(() => {
    try {
      setCartState(cart ? "Initialisé" : "Non disponible")
      setFavoritesState(favorites ? "Initialisé" : "Non disponible")
      
      console.log("État du panier:", cart)
      console.log("État des favoris:", favorites)
    } catch (error) {
      console.error("Erreur lors de la vérification des hooks:", error)
    }
  }, [cart, favorites])
  
  // Fonction pour ajouter au panier
  const handleAddToCart = async () => {
    try {
      console.log("Tentative d'ajout au panier...")
      // Correction : utiliser addToCart à la place de addItem
      await cart.addToCart(testProduct, 1)
      toast({
        title: "Test réussi",
        description: "Produit ajouté au panier avec succès"
      })
    } catch (error) {
      console.error("Erreur lors de l'ajout au panier:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'ajouter au panier: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive"
      })
    }
  }
  
  // Fonction pour ajouter aux favoris
  const handleToggleFavorite = () => {
    try {
      console.log("Tentative d'ajout aux favoris...")
      favorites.toggleFavorite(testProduct)
      toast({
        title: "Test réussi",
        description: "Produit ajouté/retiré des favoris avec succès"
      })
    } catch (error) {
      console.error("Erreur lors de l'ajout aux favoris:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'ajouter aux favoris: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive"
      })
    }
  }
  
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Débogage Panier & Favoris</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="p-4 border rounded-md">
            <h3 className="font-bold mb-2">État du panier</h3>
            <p className={cartState === "Initialisé" ? "text-green-500" : "text-red-500"}>
              {cartState}
            </p>
            <p className="text-sm mt-2">
              {cart?.items?.length || 0} articles dans le panier
            </p>
          </div>
          
          <div className="p-4 border rounded-md">
            <h3 className="font-bold mb-2">État des favoris</h3>
            <p className={favoritesState === "Initialisé" ? "text-green-500" : "text-red-500"}>
              {favoritesState}
            </p>
            <p className="text-sm mt-2">
              {favorites?.favorites?.length || 0} articles en favoris
            </p>
          </div>
        </div>
        
        <div className="flex gap-4 mt-4">
          <Button 
            onClick={handleAddToCart}
            className="flex-1 bg-purple-600 hover:bg-purple-700"
          >
            <ShoppingCart className="w-4 h-4 mr-2" />
            Tester panier
          </Button>
          
          <Button 
            onClick={handleToggleFavorite}
            variant="outline"
            className="flex-1"
          >
            <Heart className="w-4 h-4 mr-2" />
            Tester favoris
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
