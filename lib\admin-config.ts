/**
 * Configuration pour l'administration du site
 * Ce fichier centralise les paramètres liés à l'administration
 */

// Liste des emails autorisés pour l'accès admin
export const ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>'
];

/**
 * Vérifie si un email est autorisé à accéder à l'administration
 * @param email Email à vérifier
 * @returns true si l'email est autorisé, false sinon
 */
export function isAdminEmail(email: string | null | undefined): boolean {
  if (!email) return false;
  return ADMIN_EMAILS.includes(email);
}
