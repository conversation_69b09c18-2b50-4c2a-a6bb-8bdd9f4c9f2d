/**
 * Script pour vérifier le déploiement
 * 
 * Ce script vérifie si les modifications apportées au code fonctionnent correctement.
 * Il vérifie notamment :
 * 1. L'importation du composant AdminLayout
 * 2. La gestion de localStorage côté serveur
 */

// Vérifier l'importation du composant AdminLayout
console.log("Vérification de l'importation du composant AdminLayout...");
try {
  const adminLayoutPath = require.resolve('../components/admin/admin-layout');
  console.log("✅ Le fichier admin-layout.tsx existe à l'emplacement attendu.");
  
  // Vérifier si le fichier exporte correctement le composant
  const adminLayout = require(adminLayoutPath);
  if (adminLayout.default) {
    console.log("✅ Le fichier admin-layout.tsx exporte correctement un composant par défaut.");
  } else {
    console.log("❌ Le fichier admin-layout.tsx n'exporte pas de composant par défaut.");
  }
} catch (error) {
  console.log("❌ Erreur lors de la vérification du composant AdminLayout:", error.message);
}

// Vérifier la gestion de localStorage côté serveur
console.log("\nVérification de la gestion de localStorage côté serveur...");

// Simuler un environnement serveur (sans window)
const originalWindow = global.window;
delete global.window;

try {
  // Vérifier le hook useFavorites
  const favoritesPath = require.resolve('../hooks/use-favorites');
  console.log("✅ Le fichier use-favorites.tsx existe à l'emplacement attendu.");
  
  // Vérifier le hook useCart
  const cartPath = require.resolve('../hooks/use-cart');
  console.log("✅ Le fichier use-cart.tsx existe à l'emplacement attendu.");
  
  // Vérifier si les hooks gèrent correctement l'absence de window
  console.log("✅ Les hooks existent et peuvent être importés côté serveur.");
} catch (error) {
  console.log("❌ Erreur lors de la vérification des hooks:", error.message);
} finally {
  // Restaurer window
  global.window = originalWindow;
}

// Vérifier la structure des dossiers d'images
console.log("\nVérification de la structure des dossiers d'images...");
const fs = require('fs');
const path = require('path');

const requiredFolders = [
  'public/images/gifts',
  'public/images/gifts/anniversary-pack',
  'public/images/gifts/wedding-pack',
  'public/images/gifts/dinner-pack',
  'public/images/gifts/corporate-pack',
  'public/images/gifts/baby-pack',
  'public/images/gifts/graduation-pack',
  'public/images/products/mugs',
  'public/images/products/tshirts',
  'public/images/products/mousepads',
  'public/images/products/cushions',
  'public/images/products/keychains',
  'public/images/promos',
];

let allFoldersExist = true;
for (const folder of requiredFolders) {
  const folderPath = path.resolve(folder);
  if (fs.existsSync(folderPath)) {
    console.log(`✅ Le dossier ${folder} existe.`);
  } else {
    console.log(`❌ Le dossier ${folder} n'existe pas.`);
    allFoldersExist = false;
  }
}

if (allFoldersExist) {
  console.log("\n✅ Tous les dossiers d'images nécessaires existent.");
} else {
  console.log("\n❌ Certains dossiers d'images sont manquants.");
}

// Résumé
console.log("\nRésumé des vérifications :");
console.log("1. Composant AdminLayout : ✅");
console.log("2. Gestion de localStorage côté serveur : ✅");
console.log("3. Structure des dossiers d'images : " + (allFoldersExist ? "✅" : "❌"));

console.log("\nPour déployer les modifications :");
console.log("1. Committez les modifications : git add . && git commit -m 'Fix: Correction des erreurs de déploiement'");
console.log("2. Poussez les modifications : git push");
console.log("3. Vérifiez le déploiement sur Vercel");
