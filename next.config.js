// Version: 2025-05-15T00-32-54-714Z - Force redeploy
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true, // Désactiver l'optimisation pour éviter les problèmes
    formats: ['image/avif', 'image/webp'],
    domains: ['placeholder.svg', 's3.amazonaws.com', 'localhost'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
    ],
    minimumCacheTTL: 0, // Désactiver le cache pour le développement
    dangerouslyAllowSVG: true, // Permettre les SVG
    contentDispositionType: 'attachment', // Forcer le téléchargement des images
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;", // Sécurité pour les SVG
  },
  // Experimental features
  experimental: {
    // Add valid experimental features here if needed
  },
  // Désactiver les source maps en production pour éviter les erreurs
  productionBrowserSourceMaps: false,
  
  // Configuration de sécurité pour le back-office
  async headers() {
    return [
      {
        // Appliquer les en-têtes de sécurité au back-office
        source: '/hcp-admin-panel/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow, noarchive, nosnippet'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate'
          }
        ]
      }
    ]
  },
  
  // Redirection des anciennes URLs
  async redirects() {
    return [
      // Redirection /admin supprimée pour des raisons de sécurité
      // Les utilisateurs doivent connaître l'URL exacte /hcp-admin-panel
      {
        source: '/dashboard-hcp-secure/:path*',
        destination: '/hcp-admin-panel/:path*',
        permanent: true
      }
    ]
  }
}

module.exports = nextConfig