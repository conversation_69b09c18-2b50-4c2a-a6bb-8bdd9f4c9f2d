// Script pour générer un hash bcrypt pour le mot de passe d'administration
const bcrypt = require('bcrypt');

// Remplacez ceci par votre nouveau mot de passe complexe
const password = 'P@ssw0rd!2024#ComplexSecure';
const saltRounds = 12;

bcrypt.hash(password, saltRounds, function(err, hash) {
  if (err) {
    console.error('Erreur lors du hachage du mot de passe:', err);
    return;
  }
  
  console.log('Nouveau hash bcrypt pour .htpasswd:');
  console.log(`MenannZoro:${hash}`);
});

