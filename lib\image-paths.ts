/**
 * Configuration des chemins d'accès aux images du site
 * Ce fichier centralise tous les chemins d'images pour faciliter leur mise à jour
 */

// Chemins pour les coques de téléphone par modèle
export const phoneCasePaths = {
  // iPhone
  iphone: {
    // iPhone 16 Series
    '16ProMax': '/images/phone-cases/iphone/iphone-16-pro-max/placeholder.png',
    '16Pro': '/images/phone-cases/iphone/iphone-16-pro/placeholder.png',
    '16Plus': '/images/phone-cases/iphone/iphone-16-plus/placeholder.png',
    '16': '/images/phone-cases/iphone/iphone-16/placeholder.png',
    // iPhone 15 Series
    '15ProMax': '/images/phone-cases/iphone/iphone-15-pro-max/placeholder.png',
    '15Pro': '/images/phone-cases/iphone/iphone-15-pro/placeholder.png',
    '15Plus': '/images/phone-cases/iphone/iphone-15-plus/placeholder.png',
    '15': '/images/phone-cases/iphone/iphone-15/placeholder.png',
    // iPhone 14 Series
    '14ProMax': '/images/phone-cases/iphone/iphone-14-pro-max/placeholder.png',
    '14Pro': '/images/phone-cases/iphone/iphone-14-pro/placeholder.png',
    '14Plus': '/images/phone-cases/iphone/iphone-14-plus/placeholder.png',
    '14': '/images/phone-cases/iphone/iphone-14/placeholder.png',
    // iPhone 13 Series
    '13ProMax': '/images/phone-cases/iphone/iphone-13-pro-max/placeholder.png',
    '13Pro': '/images/phone-cases/iphone/iphone-13-pro/placeholder.png',
    '13': '/images/phone-cases/iphone/iphone-13/placeholder.png',
    // iPhone 12 Series
    '12ProMax': '/images/phone-cases/iphone/iphone-12-pro-max/placeholder.png',
    '12Pro': '/images/phone-cases/iphone/iphone-12-pro/placeholder.png',
    '12': '/images/phone-cases/iphone/iphone-12/placeholder.png',
    // iPhone SE
    'SE2022': '/images/phone-cases/iphone/iphone-se-2022/placeholder.png',
  },

  // Samsung
  samsung: {
    // Galaxy S25 Series
    'S25Ultra': '/images/phone-cases/samsung/galaxy-s25-ultra/placeholder.png',
    'S25Plus': '/images/phone-cases/samsung/galaxy-s25-plus/placeholder.png',
    'S25': '/images/phone-cases/samsung/galaxy-s25/placeholder.png',
    // Galaxy S24 Series
    'S24Ultra': '/images/phone-cases/samsung/galaxy-s24-ultra/placeholder.png',
    'S24Plus': '/images/phone-cases/samsung/galaxy-s24-plus/placeholder.png',
    'S24': '/images/phone-cases/samsung/galaxy-s24/placeholder.png',
    // Galaxy S23 Series
    'S23Ultra': '/images/phone-cases/samsung/galaxy-s23-ultra/placeholder.png',
    'S23Plus': '/images/phone-cases/samsung/galaxy-s23-plus/placeholder.png',
    'S23': '/images/phone-cases/samsung/galaxy-s23/placeholder.png',
    // Galaxy S22 Series
    'S22Ultra': '/images/phone-cases/samsung/galaxy-s22-ultra/placeholder.png',
    'S22Plus': '/images/phone-cases/samsung/galaxy-s22-plus/placeholder.png',
    'S22': '/images/phone-cases/samsung/galaxy-s22/placeholder.png',
    // Galaxy A Series
    'A54': '/images/phone-cases/samsung/galaxy-a54/placeholder.png',
    'A53': '/images/phone-cases/samsung/galaxy-a53/placeholder.png',
    'A34': '/images/phone-cases/samsung/galaxy-a34/placeholder.png',
  },

  // Google Pixel
  google: {
    'Pixel8Pro': '/images/phone-cases/google/pixel-8-pro/placeholder.png',
    'Pixel8': '/images/phone-cases/google/pixel-8/placeholder.png',
    'Pixel7Pro': '/images/phone-cases/google/pixel-7-pro/placeholder.png',
    'Pixel7': '/images/phone-cases/google/pixel-7/placeholder.png',
    'Pixel6Pro': '/images/phone-cases/google/pixel-6-pro/placeholder.png',
    'Pixel6': '/images/phone-cases/google/pixel-6/placeholder.png',
  },
};

// Chemins pour les types de coques
export const caseTypePaths = {
  // Types standard
  transparente: '/images/phone-cases/types/transparente/placeholder.png',
  silicone: '/images/phone-cases/types/silicone/placeholder.png',
  rigide: '/images/phone-cases/types/rigide/placeholder.png',
  antichoc: '/images/phone-cases/types/antichoc/placeholder.png',
  portefeuille: '/images/phone-cases/types/portefeuille/placeholder.png',
  magnetique: '/images/phone-cases/types/magnetique/placeholder.png',
  // Types premium
  premium: '/images/phone-cases/types/premium/placeholder.png',
  premiumMagsafe: '/images/phone-cases/types/premium-magsafe/placeholder.png',
  vip1: '/images/phone-cases/types/vip-1/placeholder.png',
};

// Chemins pour les bannières
export const bannerPaths = {
  accueil: '/images/banners/accueil/accueil.png',
  promotions: '/images/banners/promotions/promotions.png',
  categories: '/images/banners/categories/categories.png',
  produits: '/images/banners/produits/produits.png',
  evenements: '/images/banners/evenements/evenements.png',
  saisonniers: '/images/banners/saisonniers/saisonniers.png',
  about: '/images/banners/about/about.png',
  contact: '/images/banners/contact/contact.png',
  models: '/images/banners/models/models.png',
  shop: '/images/banners/shop/shop.png',
  customize: '/images/banners/customize/customize.png',
  gallery: '/images/banners/gallery/gallery.png',
  account: '/images/banners/account/account.png',
};

// Chemins pour les témoignages
export const testimonialPaths = {
  clients: '/images/testimonials/clients/clients.png',
  entreprises: '/images/testimonials/entreprises/entreprises.png',
  influenceurs: '/images/testimonials/influenceurs/influenceurs.png',
};

// Chemins pour les designs
export const designPaths = {
  abstraits: '/images/designs/abstraits/abstraits.png',
  animaux: '/images/designs/animaux/animaux.png',
  fleurs: '/images/designs/fleurs/fleurs.png',
  geometriques: '/images/designs/geometriques/geometriques.png',
  personnages: '/images/designs/personnages/personnages.png',
  sports: '/images/designs/sports/sports.png',
  marques: '/images/designs/marques/marques.png',
  personnalises: '/images/designs/personnalises/personnalises.png',
  abstract: '/images/designs/abstract/abstract.png',
  nature: '/images/designs/nature/nature.png',
  animals: '/images/designs/animals/animals.png',
  religious: '/images/designs/religious/religious.png',
};

// Chemins pour les produits
export const productPaths = {
  coques: '/images/products/coques/coques.png',
  accessoires: '/images/products/accessoires/accessoires.png',
  personnalisation: '/images/products/personnalisation/personnalisation.png',
  nouveautes: '/images/products/nouveautes/nouveautes.png',
  promotions: '/images/products/promotions/promotions.png',
  mugs: '/images/products/mugs/mugs.png',
  tshirts: '/images/products/tshirts/tshirts.png',
  mousepads: '/images/products/mousepads/mousepads.png',
  cushions: '/images/products/cushions/cushions.png',
  keychains: '/images/products/keychains/keychains.png',
};

// Chemins pour les images du compte utilisateur
export const accountPaths = {
  profile: '/images/account/profile/profile.png',
  orders: '/images/account/orders/orders.png',
  designs: '/images/account/designs/designs.png',
  favorites: '/images/account/favorites/favorites.png',
};

/**
 * Fonction utilitaire pour obtenir le chemin d'une image de coque par modèle
 * @param brand - Marque du téléphone (iphone, samsung, google)
 * @param model - Modèle du téléphone
 * @returns Le chemin de l'image correspondante
 */
export function getPhoneCaseImagePath(brand: string, model: string): string {
  const brandLower = brand.toLowerCase();

  if (brandLower === 'iphone' && model in phoneCasePaths.iphone) {
    return phoneCasePaths.iphone[model as keyof typeof phoneCasePaths.iphone];
  }

  if (brandLower === 'samsung' && model in phoneCasePaths.samsung) {
    return phoneCasePaths.samsung[model as keyof typeof phoneCasePaths.samsung];
  }

  if (brandLower === 'google' && model in phoneCasePaths.google) {
    return phoneCasePaths.google[model as keyof typeof phoneCasePaths.google];
  }

  // Image par défaut si le modèle n'est pas trouvé
  return '/images/phone-cases/phone-case-placeholder.png';
}

/**
 * Fonction utilitaire pour obtenir le chemin d'une image de type de coque
 * @param type - Type de coque (transparente, silicone, rigide, etc.)
 * @returns Le chemin de l'image correspondante
 */
export function getCaseTypePath(type: string): string {
  const typeLower = type.toLowerCase().replace('-', '');

  if (typeLower in caseTypePaths) {
    return caseTypePaths[typeLower as keyof typeof caseTypePaths];
  }

  // Image par défaut si le type n'est pas trouvé
  return '/images/phone-cases/types/transparente/placeholder.png';
}

/**
 * Fonction utilitaire pour obtenir le chemin d'une bannière par type
 * @param type - Type de bannière
 * @returns Le chemin de l'image correspondante
 */
export function getBannerImagePath(type: string): string {
  if (type in bannerPaths) {
    return bannerPaths[type as keyof typeof bannerPaths];
  }

  // Image par défaut si le type n'est pas trouvé
  return '/images/banners/banner-placeholder.png';
}

/**
 * Fonction utilitaire pour obtenir le chemin d'un témoignage par type
 * @param type - Type de témoignage
 * @returns Le chemin de l'image correspondante
 */
export function getTestimonialImagePath(type: string): string {
  if (type in testimonialPaths) {
    return testimonialPaths[type as keyof typeof testimonialPaths];
  }

  // Image par défaut si le type n'est pas trouvé
  return '/images/testimonials/testimonial-placeholder.png';
}

/**
 * Fonction utilitaire pour obtenir le chemin d'une image du compte utilisateur
 * @param type - Type d'image du compte (profile, orders, designs, favorites)
 * @returns Le chemin de l'image correspondante
 */
export function getAccountImagePath(type: string): string {
  if (type in accountPaths) {
    return accountPaths[type as keyof typeof accountPaths];
  }

  // Image par défaut si le type n'est pas trouvé
  return '/images/account/profile/profile.png';
}

/**
 * Fonction utilitaire pour obtenir le chemin d'une icône
 * @param name - Nom de l'icône (whatsapp, facebook, instagram, etc.)
 * @returns Le chemin de l'icône correspondante
 */
export function getIconPath(name: string): string {
  const iconPath = `/images/icons/${name}.png`;
  return iconPath;
}

/**
 * Fonction utilitaire pour obtenir le chemin d'une image de la galerie
 * @param name - Nom de l'image (optionnel)
 * @returns Le chemin de l'image correspondante
 */
export function getGalleryImagePath(name: string = 'gallery'): string {
  const galleryPath = `/images/gallery/${name}.png`;
  return galleryPath;
}

/**
 * Fonction utilitaire pour obtenir les chemins des images de la bannière animée
 * @returns Un tableau de chemins d'images pour la bannière animée
 */
export function getAnimatedBannerImagePaths(): { src: string; alt: string }[] {
  return [
    { src: "/images/banners/promotions/banner1.png", alt: "Promotion spéciale fête des mères" },
    { src: "/images/banners/promotions/banner2.png", alt: "Offre exclusive" },
    { src: "/images/banners/promotions/banner3.png", alt: "Réduction spéciale" }
  ];
}
