/**
 * Script pour vérifier et corriger les images de la page modèle
 */

const fs = require('fs');
const path = require('path');

// Fonction pour lire un fichier
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier ${filePath}:`, error);
    return null;
  }
}

// Fonction pour écrire dans un fichier
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'écriture dans le fichier ${filePath}:`, error);
    return false;
  }
}

// Fonction pour vérifier si un fichier existe
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Fonction pour créer une image SVG de base
function createBasicSvgImage(text, subtext = '') {
  return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="600" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="500" height="300" rx="5" fill="#E5E7EB"/>
  <text x="300" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">${text}</text>
  ${subtext ? `<text x="300" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">${subtext}</text>` : ''}
</svg>`;
}

// Fonction pour créer une image de remplacement
function createPlaceholderImage(outputPath, text = 'Image', subtext = 'Placeholder') {
  try {
    // Créer le répertoire parent s'il n'existe pas
    const dirPath = path.dirname(outputPath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    // Créer l'image SVG
    const svgContent = createBasicSvgImage(text, subtext);
    fs.writeFileSync(outputPath, svgContent);
    console.log(`Image créée: ${outputPath}`);
    return true;
  } catch (error) {
    console.error(`Erreur lors de la création de l'image ${outputPath}:`, error);
    return false;
  }
}

// Fonction pour vérifier et corriger les images des marques
function checkAndFixBrandImages() {
  console.log("Vérification des images des marques...");
  
  const brandsDir = path.join(process.cwd(), 'public', 'images', 'brands');
  if (!fs.existsSync(brandsDir)) {
    fs.mkdirSync(brandsDir, { recursive: true });
  }
  
  // Liste des marques
  const brands = [
    { id: 'apple', name: 'Apple' },
    { id: 'samsung', name: 'Samsung' },
    { id: 'google', name: 'Google' },
    { id: 'xiaomi', name: 'Xiaomi' },
    { id: 'huawei', name: 'Huawei' }
  ];
  
  brands.forEach(brand => {
    const svgPath = path.join(brandsDir, `${brand.id}.svg`);
    if (!fileExists(svgPath)) {
      createPlaceholderImage(svgPath, brand.name, 'Logo');
    }
  });
}

// Fonction pour vérifier et corriger les images des modèles de téléphone
function checkAndFixPhoneModelImages() {
  console.log("Vérification des images des modèles de téléphone...");
  
  const phoneModelsDir = path.join(process.cwd(), 'public', 'images', 'phone-models');
  if (!fs.existsSync(phoneModelsDir)) {
    fs.mkdirSync(phoneModelsDir, { recursive: true });
  }
  
  // Liste des modèles
  const models = [
    { id: 'iphone15promax', name: 'iPhone 15 Pro Max' },
    { id: 'iphone15pro', name: 'iPhone 15 Pro' },
    { id: 'iphone15plus', name: 'iPhone 15 Plus' },
    { id: 'iphone15', name: 'iPhone 15' },
    { id: 'iphone14promax', name: 'iPhone 14 Pro Max' },
    { id: 'iphone14pro', name: 'iPhone 14 Pro' },
    { id: 'iphone14plus', name: 'iPhone 14 Plus' },
    { id: 'iphone14', name: 'iPhone 14' },
    { id: 'samsungs23ultra', name: 'Samsung S23 Ultra' },
    { id: 'samsungs23plus', name: 'Samsung S23 Plus' },
    { id: 'samsungs23', name: 'Samsung S23' },
    { id: 'pixel8pro', name: 'Google Pixel 8 Pro' },
    { id: 'pixel8', name: 'Google Pixel 8' },
    { id: 'pixel7pro', name: 'Google Pixel 7 Pro' },
    { id: 'pixel7', name: 'Google Pixel 7' }
  ];
  
  models.forEach(model => {
    const pngPath = path.join(phoneModelsDir, `${model.id}.png`);
    if (!fileExists(pngPath)) {
      createPlaceholderImage(pngPath, model.name, 'Modèle');
    }
  });
}

// Fonction pour corriger les références aux images dans la page modèle
function fixModelPageImageReferences() {
  console.log("Correction des références aux images dans la page modèle...");
  
  const filePath = path.join(process.cwd(), 'app', 'models', 'page.tsx');
  const content = readFile(filePath);
  
  if (!content) return false;
  
  // Recherche des références aux images placeholder
  const placeholderRegex = /(?:src|image|imageUrl|thumbnail):\s*["'](\/placeholder\.svg|placeholder\.svg)(?:\?[^"']*)?["']/g;
  let match;
  let modified = false;
  let newContent = content;
  
  const replacements = [];
  
  while ((match = placeholderRegex.exec(content)) !== null) {
    const fullMatch = match[0];
    const placeholderPath = match[1];
    
    // Remplacer par une référence à une image de marque ou de modèle
    let alternativePath = '/images/brands/apple.svg';
    
    // Remplacer la référence
    const newReference = fullMatch.replace(placeholderPath, alternativePath);
    replacements.push({ from: fullMatch, to: newReference });
    
    modified = true;
  }
  
  // Appliquer les remplacements
  for (const replacement of replacements) {
    newContent = newContent.replace(replacement.from, replacement.to);
  }
  
  // Enregistrer les modifications
  if (modified) {
    console.log(`Correction de ${replacements.length} références aux images placeholder dans la page modèle`);
    return writeFile(filePath, newContent);
  }
  
  return false;
}

// Fonction principale
function main() {
  console.log("Début de la vérification et correction des images de la page modèle...");
  
  // Vérifier et corriger les images des marques
  checkAndFixBrandImages();
  
  // Vérifier et corriger les images des modèles de téléphone
  checkAndFixPhoneModelImages();
  
  // Corriger les références aux images dans la page modèle
  const fixed = fixModelPageImageReferences();
  
  if (fixed) {
    console.log("\nLes références aux images ont été corrigées dans la page modèle.");
    console.log("N'oubliez pas de committer et pousser ces modifications.");
  } else {
    console.log("\nAucune référence à corriger n'a été trouvée dans la page modèle.");
  }
  
  console.log("\nVérification et correction des images de la page modèle terminées.");
}

// Exécuter la fonction principale
main();
