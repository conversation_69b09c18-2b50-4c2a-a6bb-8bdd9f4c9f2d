/**
 * Script pour supprimer les fichiers .gitkeep des dossiers qui contiennent déjà des images
 */

const fs = require('fs');
const path = require('path');

// Fonction pour vérifier si un dossier contient des images
function directoryContainsImages(dirPath) {
  try {
    const files = fs.readdirSync(dirPath);
    return files.some(file => {
      const filePath = path.join(dirPath, file);
      if (fs.statSync(filePath).isDirectory()) {
        return false; // Ignorer les sous-dossiers
      }
      const ext = path.extname(file).toLowerCase();
      return ['.png', '.jpg', '.jpeg', '.svg', '.gif', '.webp'].includes(ext);
    });
  } catch (error) {
    console.error(`Erreur lors de la vérification du dossier ${dirPath}:`, error);
    return false;
  }
}

// Fonction pour supprimer le fichier .gitkeep si le dossier contient des images
function removeGitkeepIfNeeded(dirPath) {
  try {
    const gitkeepPath = path.join(dirPath, '.gitkeep');
    
    if (fs.existsSync(gitkeepPath) && directoryContainsImages(dirPath)) {
      fs.unlinkSync(gitkeepPath);
      console.log(`Fichier .gitkeep supprimé de: ${dirPath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`Erreur lors de la suppression du fichier .gitkeep de ${dirPath}:`, error);
    return false;
  }
}

// Fonction pour parcourir récursivement un répertoire
function walkDir(dir, callback) {
  try {
    fs.readdirSync(dir).forEach(f => {
      const dirPath = path.join(dir, f);
      if (fs.statSync(dirPath).isDirectory()) {
        callback(dirPath);
        walkDir(dirPath, callback);
      }
    });
  } catch (error) {
    console.error(`Erreur lors du parcours du répertoire ${dir}:`, error);
  }
}

// Fonction principale
function main() {
  console.log("Début de la suppression des fichiers .gitkeep inutiles...");
  
  const imagesDir = path.join(process.cwd(), 'public', 'images');
  let totalRemoved = 0;
  
  // Vérifier le dossier principal
  if (removeGitkeepIfNeeded(imagesDir)) {
    totalRemoved++;
  }
  
  // Parcourir tous les sous-dossiers
  walkDir(imagesDir, (dirPath) => {
    if (removeGitkeepIfNeeded(dirPath)) {
      totalRemoved++;
    }
  });
  
  console.log(`Suppression terminée. ${totalRemoved} fichiers .gitkeep ont été supprimés.`);
}

// Exécuter la fonction principale
main();
