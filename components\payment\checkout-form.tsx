"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import WavePayment from "./wave-payment"
import OrangePayment from "./orange-payment"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import { useCustomer } from "@/hooks/use-customer"
import { useRouter } from "next/navigation"

interface CheckoutFormProps {
  subtotal: number
  shipping: number
  total: number
}

export default function CheckoutForm({ subtotal, shipping, total }: CheckoutFormProps) {
  const [paymentMethod, setPaymentMethod] = useState<"wave" | "orange">("wave")
  const [formData, setFormData] = useState({
    fullName: "",
    phone: "",
    address: "",
    city: "",
    notes: ""
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()
  const { items, clearCart } = useSimpleCart()
  const { customer } = useCustomer()
  const router = useRouter()

  // Pré-remplir les champs avec les informations du client connecté
  useEffect(() => {
    if (customer) {
      setFormData(prev => ({
        ...prev,
        fullName: customer.fullName || prev.fullName,
        phone: customer.whatsapp || prev.phone
      }))
    }
  }, [customer])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleWavePaymentComplete = async () => {
    await submitOrder("wave", "", null)
  }

  const handleOrangePaymentComplete = async (message: string, screenshot: File | null) => {
    await submitOrder("orange", message, screenshot)
  }

  const submitOrder = async (
    method: "wave" | "orange",
    paymentMessage: string = "",
    paymentScreenshot: File | null = null
  ) => {
    try {
      setIsSubmitting(true)

      // Vérifier que tous les champs requis sont remplis
      if (!formData.fullName || !formData.phone || !formData.address || !formData.city) {
        toast({
          title: "Erreur",
          description: "Veuillez remplir tous les champs obligatoires",
          variant: "destructive"
        })
        return
      }

      // Préparer les données de la commande
      const orderData = {
        ...formData,
        paymentMethod: method,
        paymentMessage,
        total,
        items: items.map(item => ({
          productId: item.id, // id du produit dans le panier
          quantity: item.quantity,
          price: item.price,
          customized: item.customized || false,
          customizationData: item.customization_data || null
        }))
      }

      // Créer un FormData si nous avons une capture d'écran
      let formDataToSend = null
      if (paymentScreenshot) {
        formDataToSend = new FormData()
        formDataToSend.append("screenshot", paymentScreenshot)
        formDataToSend.append("orderData", JSON.stringify(orderData))
      }

      // Envoyer la commande à l'API
      const response = await fetch("/api/orders/create", {
        method: "POST",
        headers: !formDataToSend ? { "Content-Type": "application/json" } : undefined,
        body: formDataToSend || JSON.stringify(orderData)
      })

      if (!response.ok) {
        throw new Error("Erreur lors de la création de la commande")
      }

      const data = await response.json()

      // Vider le panier après une commande réussie
      await clearCart()

      // Afficher un message de succès
      toast({
        title: "Commande confirmée",
        description: "Votre commande a été enregistrée avec succès"
      })

      // Rediriger vers la page de confirmation
      router.push(`/order-confirmation/${data.orderId}`)

    } catch (error) {
      console.error("Erreur lors de la soumission de la commande:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création de votre commande",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleWhatsAppSend = () => {
    // Préparer le message WhatsApp avec les informations de la commande
    const phoneModels = items.map(item => {
      // Extraire le modèle de téléphone depuis les données de personnalisation ou le nom du produit
      if (item.customization_data?.model) {
        return item.customization_data.model
      }
      // Fallback: essayer d'extraire depuis le nom du produit
      return "Modèle non spécifié"
    }).join(", ")

    const message = `Bonjour ! Voici les informations de ma commande :

` +
      `👤 Nom du client : ${formData.fullName}
` +
      `📱 Numéro du client : ${formData.phone}
` +
      `📞 Série du téléphone sélectionnée : ${phoneModels}
` +
      `💰 Montant total : ${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XOF' }).format(total)}
` +
      `🏠 Adresse de livraison : ${formData.address}, ${formData.city}
\n` +
      `📸 Note : Je vais envoyer la photo personnalisée manuellement après ce message.
\n` +
      `Merci !`

    // Encoder le message pour l'URL
    const encodedMessage = encodeURIComponent(message)
    
    // Numéro WhatsApp de l'entreprise (à remplacer par le vrai numéro)
    const businessWhatsApp = "+2250709495848"
    
    // Créer l'URL WhatsApp
    const whatsappUrl = `https://wa.me/${businessWhatsApp}?text=${encodedMessage}`
    
    // Ouvrir WhatsApp
    window.open(whatsappUrl, '_blank')
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Informations de livraison</CardTitle>
          <CardDescription>
            Entrez vos coordonnées pour la livraison
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fullName">Nom complet *</Label>
              <Input
                id="fullName"
                name="fullName"
                placeholder="Votre nom complet"
                value={formData.fullName}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Téléphone / WhatsApp *</Label>
              <Input
                id="phone"
                name="phone"
                placeholder="Votre numéro de téléphone"
                value={formData.phone}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Adresse de livraison *</Label>
            <Input
              id="address"
              name="address"
              placeholder="Votre adresse complète"
              value={formData.address}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="city">Ville / Commune *</Label>
            <Input
              id="city"
              name="city"
              placeholder="Votre ville ou commune"
              value={formData.city}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Instructions de livraison (facultatif)</Label>
            <Textarea
              id="notes"
              name="notes"
              placeholder="Instructions spéciales pour la livraison"
              value={formData.notes}
              onChange={handleInputChange}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Méthode de paiement</CardTitle>
          <CardDescription>
            Choisissez votre méthode de paiement préférée
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue="wave"
            onValueChange={(value) => setPaymentMethod(value as "wave" | "orange")}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="wave">Wave</TabsTrigger>
              <TabsTrigger value="orange">Orange Money</TabsTrigger>
            </TabsList>
            <TabsContent value="wave" className="mt-4">
              <WavePayment
                amount={total}
                onPaymentComplete={handleWavePaymentComplete}
              />
            </TabsContent>
            <TabsContent value="orange" className="mt-4">
              <OrangePayment
                amount={total}
                onPaymentComplete={handleOrangePaymentComplete}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Instructions et bouton WhatsApp */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <p className="text-sm text-muted-foreground">
              🔔 Après avoir effectué le paiement, veuillez cliquer sur le bouton ci-dessous pour nous envoyer les informations de votre commande via WhatsApp.
            </p>
            <Button 
              onClick={handleWhatsAppSend}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
              disabled={!formData.fullName || !formData.phone}
            >
              📲 Envoyer sur WhatsApp
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
