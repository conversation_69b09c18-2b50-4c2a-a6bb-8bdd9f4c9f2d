# Script pour consolider les dossiers Galerie et gallery

# Fonction pour créer un dossier s'il n'existe pas
function Create-Directory-If-Not-Exists {
    param (
        [string]$path
    )
    
    if (-not (Test-Path -Path $path)) {
        New-Item -Path $path -ItemType Directory -Force | Out-Null
        Write-Host "Dossier créé: $path" -ForegroundColor Green
    }
}

# Fonction pour copier les fichiers d'un dossier à un autre
function Copy-Files-Between-Directories {
    param (
        [string]$sourcePath,
        [string]$destinationPath
    )
    
    if (Test-Path -Path $sourcePath) {
        # Créer le dossier de destination s'il n'existe pas
        Create-Directory-If-Not-Exists -path $destinationPath
        
        # Copier tous les fichiers du dossier source vers le dossier destination
        Get-ChildItem -Path $sourcePath -File | ForEach-Object {
            $destFile = Join-Path -Path $destinationPath -ChildPath $_.Name
            Copy-Item -Path $_.FullName -Destination $destFile -Force
            Write-Host "Fichier copié: $($_.Name) -> $destFile" -ForegroundColor Cyan
        }
    }
}

# Fonction pour consolider les dossiers Galerie et gallery
function Consolidate-Gallery-Folders {
    param (
        [string]$sourceRoot,
        [string]$destinationRoot,
        [string[]]$subfolders
    )
    
    foreach ($subfolder in $subfolders) {
        $sourcePath = Join-Path -Path $sourceRoot -ChildPath $subfolder
        $destinationPath = Join-Path -Path $destinationRoot -ChildPath $subfolder
        
        # Copier les fichiers du dossier source vers le dossier destination
        Copy-Files-Between-Directories -sourcePath $sourcePath -destinationPath $destinationPath
        
        # Copier récursivement les sous-dossiers
        if (Test-Path -Path $sourcePath) {
            Get-ChildItem -Path $sourcePath -Directory | ForEach-Object {
                $sourceSubPath = $_.FullName
                $subfolderName = $_.Name
                $destinationSubPath = Join-Path -Path $destinationPath -ChildPath $subfolderName
                
                # Créer le sous-dossier de destination s'il n'existe pas
                Create-Directory-If-Not-Exists -path $destinationSubPath
                
                # Copier les fichiers du sous-dossier
                Copy-Files-Between-Directories -sourcePath $sourceSubPath -destinationPath $destinationSubPath
            }
        }
    }
}

# Chemins des dossiers
$galerieRoot = "public\images\Galerie"
$galleryRoot = "public\images\gallery"
$consolidatedRoot = "public\images\gallery"

# Créer le dossier consolidé s'il n'existe pas
Create-Directory-If-Not-Exists -path $consolidatedRoot

# Liste des sous-dossiers communs
$commonSubfolders = @(
    "vagues-abstraites",
    "fleurs-tropicales",
    "galaxie-cosmique",
    "marbre-elegant",
    "banniere"
)

# Créer le dossier variants s'il n'existe pas
$variantsPath = Join-Path -Path $consolidatedRoot -ChildPath "variants"
Create-Directory-If-Not-Exists -path $variantsPath

# Consolider les dossiers
Write-Host "Consolidation des dossiers Galerie et gallery..." -ForegroundColor Yellow

# 1. Copier les fichiers de Galerie vers gallery
Consolidate-Gallery-Folders -sourceRoot $galerieRoot -destinationRoot $consolidatedRoot -subfolders $commonSubfolders

# 2. Copier les fichiers de chaque sous-dossier de Galerie vers le dossier variants correspondant
foreach ($subfolder in $commonSubfolders) {
    if ($subfolder -ne "banniere") {
        $sourcePath = Join-Path -Path $galerieRoot -ChildPath $subfolder
        $destinationPath = Join-Path -Path $variantsPath -ChildPath $subfolder
        
        # Créer le dossier de destination s'il n'existe pas
        Create-Directory-If-Not-Exists -path $destinationPath
        
        # Copier les fichiers
        Copy-Files-Between-Directories -sourcePath $sourcePath -destinationPath $destinationPath
    }
}

Write-Host "Consolidation terminée!" -ForegroundColor Green
Write-Host "Tous les fichiers ont été copiés dans le dossier $consolidatedRoot" -ForegroundColor Green
Write-Host "Vous pouvez maintenant supprimer le dossier $galerieRoot si vous le souhaitez." -ForegroundColor Yellow
