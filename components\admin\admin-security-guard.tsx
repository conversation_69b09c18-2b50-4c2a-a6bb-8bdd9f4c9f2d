"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { isAdminEmail } from "@/lib/admin-config";
import { toast } from "sonner";
import Cookies from "js-cookie";

/**
 * Composant de sécurité pour le back office
 * Vérifie l'authentification et les droits d'accès côté client
 * Redirige vers la page d'authentification si l'utilisateur n'est pas authentifié
 * Redirige vers la page d'accueil si l'utilisateur n'est pas un administrateur
 * L'authentification à deux facteurs a été supprimée
 */
export function AdminSecurityGuard({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    // Vérifier si nous sommes déjà sur la page d'authentification
    const isAuthPage = window.location.pathname === "/hcp-admin-panel/auth";

    console.log('AdminSecurityGuard check:', {
      pathname: window.location.pathname,
      isAuthPage,
      user: user ? { email: user.email, id: user.id } : null,
      isLoading,
      isAdmin: user ? isAdminEmail(user.email) : false
    });

    // Si nous sommes sur la page d'authentification, autoriser l'accès sans vérification
    if (isAuthPage) {
      console.log("Page d'authentification, accès autorisé sans vérification");
      setIsAuthorized(true);
      return;
    }

    // Si le chargement est en cours, ne rien faire
    if (isLoading) {
      console.log("Chargement en cours...");
      return;
    }

    // Si l'utilisateur n'est pas connecté, rediriger vers la page d'authentification
    if (!user) {
      console.log("Utilisateur non connecté, redirection vers la page d'authentification");
      const currentPath = window.location.pathname;
      router.push(`/hcp-admin-panel/auth?from=${encodeURIComponent(currentPath)}`);
      return;
    }

    // Si l'utilisateur est connecté mais n'est pas un admin, rediriger vers la page d'accueil
    if (user && !isAdminEmail(user.email)) {
      console.log("Utilisateur connecté mais non admin, redirection vers la page d'accueil");
      toast.error("Vous n'avez pas les droits d'accès à l'administration");
      router.push("/");
      return;
    }

    // Si l'utilisateur est un admin, autoriser l'accès (sans vérification 2FA)
    if (user && isAdminEmail(user.email)) {
      console.log("Utilisateur admin authentifié, accès autorisé");
      setIsAuthorized(true);
    }
  }, [user, isLoading, router]);

  // Afficher un écran de chargement pendant la vérification
  if (isLoading || !isAuthorized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Vérification des droits d'accès...</p>
        </div>
      </div>
    );
  }

  // Si l'utilisateur est un admin, afficher le contenu
  return <>{children}</>;
}
