-- Script pour ajouter les tables manquantes à Supabase

-- Activer les extensions nécessaires (si ce n'est pas déjà fait)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Fonction pour mettre à jour le timestamp updated_at (si ce n'est pas déjà fait)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 1. Table des paramètres du site
CREATE TABLE IF NOT EXISTS public.site_settings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  value JSONB NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger pour mettre à jour updated_at
CREATE TRIGGER update_site_settings_updated_at
BEFORE UPDATE ON public.site_settings
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Politique RLS pour site_settings
ALTER TABLE public.site_settings ENABLE ROW LEVEL SECURITY;

-- Tout le monde peut lire les paramètres du site
CREATE POLICY "Anyone can read site settings" 
  ON public.site_settings 
  FOR SELECT 
  USING (true);

-- Seuls les administrateurs peuvent modifier les paramètres du site
CREATE POLICY "Only admins can modify site settings" 
  ON public.site_settings 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- 2. Table des codes de vérification
CREATE TABLE IF NOT EXISTS public.verification_codes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  email TEXT NOT NULL,
  code TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used BOOLEAN DEFAULT FALSE
);

-- Index pour accélérer les recherches par email
CREATE INDEX IF NOT EXISTS idx_verification_codes_email ON public.verification_codes(email);

-- Politique RLS pour verification_codes
ALTER TABLE public.verification_codes ENABLE ROW LEVEL SECURITY;

-- Seuls les administrateurs peuvent accéder à cette table
CREATE POLICY "Admin can manage verification codes" 
  ON public.verification_codes 
  USING (auth.role() = 'service_role');

-- 3. Table des variantes de la galerie
CREATE TABLE IF NOT EXISTS public.gallery_variants (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  image_url TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger pour mettre à jour updated_at
CREATE TRIGGER update_gallery_variants_updated_at
BEFORE UPDATE ON public.gallery_variants
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Politique RLS pour gallery_variants
ALTER TABLE public.gallery_variants ENABLE ROW LEVEL SECURITY;

-- Tout le monde peut lire les variantes de la galerie
CREATE POLICY "Anyone can read gallery variants" 
  ON public.gallery_variants 
  FOR SELECT 
  USING (true);

-- Seuls les administrateurs peuvent modifier les variantes de la galerie
CREATE POLICY "Only admins can modify gallery variants" 
  ON public.gallery_variants 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- 4. Table des méthodes de paiement
CREATE TABLE IF NOT EXISTS public.payment_methods (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  logo_url TEXT,
  qr_code_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  display_order INTEGER DEFAULT 0,
  payment_details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger pour mettre à jour updated_at
CREATE TRIGGER update_payment_methods_updated_at
BEFORE UPDATE ON public.payment_methods
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Politique RLS pour payment_methods
ALTER TABLE public.payment_methods ENABLE ROW LEVEL SECURITY;

-- Tout le monde peut lire les méthodes de paiement
CREATE POLICY "Anyone can read payment methods" 
  ON public.payment_methods 
  FOR SELECT 
  USING (true);

-- Seuls les administrateurs peuvent modifier les méthodes de paiement
CREATE POLICY "Only admins can modify payment methods" 
  ON public.payment_methods 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Insérer des données initiales pour les variantes de la galerie
INSERT INTO public.gallery_variants (name, slug, description, image_url, display_order)
VALUES
  ('Vagues Abstraites', 'vagues-abstraites', 'Designs avec motifs de vagues abstraites', '/images/gallery/variants/vagues-abstraites/vagues-abstraites.png', 1),
  ('Fleurs Tropicales', 'fleurs-tropicales', 'Designs avec motifs de fleurs tropicales', '/images/gallery/variants/fleurs-tropicales/fleurs-tropicales.png', 2),
  ('Galaxie Cosmique', 'galaxie-cosmique', 'Designs avec motifs de galaxie et d''espace', '/images/gallery/variants/galaxie-cosmique/galaxie-cosmique.png', 3),
  ('Marbre Élégant', 'marbre-elegant', 'Designs avec motifs de marbre', '/images/gallery/variants/marbre-elegant/marbre-elegant.png', 4),
  ('Rétro Synthwave', 'retro-synthwave', 'Designs avec style rétro synthwave', '/images/gallery/variants/retro-synthwave/retro-synthwave.png', 5),
  ('Montagnes Minimalistes', 'montagnes-minimalistes', 'Designs avec motifs de montagnes minimalistes', '/images/gallery/variants/montagnes-minimalistes/montagnes-minimalistes.png', 6),
  ('Motif Géométrique', 'motif-geometrique', 'Designs avec motifs géométriques', '/images/gallery/variants/motif-geometrique/motif-geometrique.png', 7),
  ('Néon Urbain', 'neon-urbain', 'Designs avec style néon urbain', '/images/gallery/variants/neon-urbain/neon-urbain.png', 8),
  ('Mandala Zen', 'mandala-zen', 'Designs avec motifs de mandala', '/images/gallery/variants/mandala-zen/mandala-zen.png', 9),
  ('Animaux Polygonaux', 'animaux-polygonaux', 'Designs avec animaux en style polygonal', '/images/gallery/variants/animaux-polygonaux/animaux-polygonaux.png', 10),
  ('Typographie Créative', 'typographie-creative', 'Designs avec typographie créative', '/images/gallery/variants/typographie-creative/typographie-creative.png', 11)
ON CONFLICT (slug) DO NOTHING;

-- Insérer des données initiales pour les méthodes de paiement
INSERT INTO public.payment_methods (name, description, logo_url, qr_code_url, is_active, display_order, payment_details)
VALUES
  ('Wave', 'Paiement mobile via Wave', '/images/payments/wave/logo/wave.png', '/images/payments/wave/qr/wave-qr.png', true, 1, '{"phone": "+2250709495849"}'),
  ('Orange Money', 'Paiement mobile via Orange Money', '/images/payments/orange/logo/orange.png', '/images/payments/orange/qr/orange-qr.png', true, 2, '{"phone": "+2250768307080"}')
ON CONFLICT DO NOTHING;

-- Insérer des données initiales pour les paramètres du site
INSERT INTO public.site_settings (key, value, description)
VALUES
  ('site_info', '{"name": "HCP Design CI", "description": "Coques de téléphone personnalisées", "primary_color": "#6d28d9", "secondary_color": "#8b5cf6"}', 'Informations générales du site'),
  ('contact_info', '{"whatsapp": "+2250709495849", "email": "<EMAIL>", "address": "Abidjan, Côte d''Ivoire"}', 'Informations de contact'),
  ('social_media', '{"facebook": "https://www.facebook.com/HabillagesetCoquesPersonnalises/", "instagram": "https://www.instagram.com/designshcp/"}', 'Liens des réseaux sociaux'),
  ('navigation', '[{"name": "Accueil", "path": "/"}, {"name": "Produits", "path": "/produits"}, {"name": "Promo", "path": "/promo"}, {"name": "Personnaliser", "path": "/customize"}, {"name": "Contact", "path": "/contact"}]', 'Configuration de la navigation'),
  ('home_banner', '{"enabled": true, "images": ["/images/banners/accueil/accueil.png"], "transition_time": 4000, "transition_effect": "zoom"}', 'Configuration de la bannière d''accueil')
ON CONFLICT (key) DO NOTHING;
