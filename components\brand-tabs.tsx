"use client"

import Link from "next/link"
import { phoneData } from "@/data/phone-models"

interface BrandTabsProps {
  activeBrand?: string;
  onSelectBrand?: (brandKey: string) => void;
}

export default function BrandTabs({ activeBrand, onSelectBrand }: BrandTabsProps) {
  return (
    <div className="flex flex-wrap gap-2 justify-center">
      {Object.entries(phoneData).map(([brandKey, brand]) => (
        <Link
          key={brandKey}
          href={`#${brandKey}`}
          className={`px-4 py-2 rounded-full ${
            brandKey === activeBrand ? 'bg-purple-600 text-white' : 'bg-gray-100 hover:bg-gray-200'
          }`}
          onClick={(e) => {
            e.preventDefault();
            if (onSelectBrand) {
              onSelectBrand(brandKey);
            }
            // Scroll to the brand section
            const element = document.getElementById(brandKey);
            if (element) {
              element.scrollIntoView({ behavior: 'smooth' });
            }
          }}
        >
          {brand.name}
        </Link>
      ))}
    </div>
  )
}
