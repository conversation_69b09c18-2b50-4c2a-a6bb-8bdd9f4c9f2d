// Script pour exécuter le fichier SQL qui ajoute les tables manquantes
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Utiliser les valeurs de fallback de lib/supabase.ts
const supabaseUrl = 'https://bekwlxorzlyoudsnajsn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJla3dseG9yemx5b3Vkc25hanNuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NzUzOTgsImV4cCI6MjA2MDU1MTM5OH0.yRKV3bo0ww0GIW1LtBB0HJQQErVWHbbbpkWVcFScS34';

// Créer le client Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

// Fonction pour exécuter une requête SQL
async function executeSql(sql) {
  try {
    // Diviser le SQL en requêtes individuelles
    const queries = sql
      .split(';')
      .map(query => query.trim())
      .filter(query => query.length > 0);
    
    console.log(`Exécution de ${queries.length} requêtes SQL...`);
    
    // Exécuter chaque requête séparément
    for (let i = 0; i < queries.length; i++) {
      const query = queries[i] + ';';
      console.log(`Exécution de la requête ${i + 1}/${queries.length}...`);
      
      const { error } = await supabase.rpc('execute_sql', { query });
      
      if (error) {
        console.error(`Erreur lors de l'exécution de la requête ${i + 1}:`, error);
        console.error('Requête problématique:', query);
      }
    }
    
    console.log('Toutes les requêtes ont été exécutées.');
  } catch (err) {
    console.error('Exception lors de l\'exécution des requêtes SQL:', err);
  }
}

// Lire le fichier SQL
const sqlFilePath = path.join(__dirname, 'add-missing-tables.sql');
const sql = fs.readFileSync(sqlFilePath, 'utf8');

// Exécuter le SQL
console.log('Exécution du fichier SQL pour ajouter les tables manquantes...');
executeSql(sql)
  .then(() => {
    console.log('Terminé !');
  })
  .catch(err => {
    console.error('Erreur:', err);
  });
