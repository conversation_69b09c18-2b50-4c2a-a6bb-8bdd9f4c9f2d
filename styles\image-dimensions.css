/* Styles pour les dimensions d'images */
.image-dimensions {
  width: var(--img-width);
  height: var(--img-height);
}

/* Appliquer les dimensions à partir des attributs data */
.image-dimensions[data-width][data-height] {
  /* CSS ne peut pas utiliser attr() pour les dimensions, donc on utilise JavaScript pour le faire */
}

/* Styles pour le placeholder */
.image-placeholder {
  background-color: #f3f4f6; /* bg-gray-100 */
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Styles pour l'erreur */
.image-error-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb; /* bg-gray-50 */
  color: #6b7280; /* text-gray-500 */
  font-size: 0.875rem; /* text-sm */
}

/* Styles pour les images base64 */
.base64-image {
  object-position: center;
}

/* Classes pour les styles personnalisés */
.custom-image-style {
  /* Styles de base pour toutes les images */
  display: block;
}

/* Styles pour différents types d'ajustement d'objet */
.object-fit-contain {
  object-fit: contain;
}

.object-fit-cover {
  object-fit: cover;
}

.object-fit-fill {
  object-fit: fill;
}

.object-fit-none {
  object-fit: none;
}

.object-fit-scale-down {
  object-fit: scale-down;
}
