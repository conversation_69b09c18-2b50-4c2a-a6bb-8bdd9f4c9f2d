const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const fetch = require('node-fetch');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Configuration Telegram
const TELEGRAM_TOKEN = '**********************************************';
// Remplacez par votre chat_id obtenu avec le script précédent
// Vous devrez mettre à jour cette valeur après avoir exécuté get-chat-id.js
const CHAT_ID = 'VOTRE_CHAT_ID'; 

// Fonction pour formater le message Telegram avec Markdown
function formatOrderMessage(orderData) {
  return `
*🛒 Nouvelle Commande HCP Design*

*👤 Client*
Nom: *${orderData.name}*
Téléphone: *${orderData.phone}*

*📦 Articles Commandés*
${orderData.items.map(item => `- ${item.quantity}x ${item.name} (${item.price} Fr)`).join('\n')}

*💰 Paiement*
Montant Total: *${orderData.totalAmount} Fr*
Avance Payée: *${orderData.advancePayment} Fr*
Reste à Payer: *${orderData.remainingAmount} Fr*
Méthode de Paiement: *${orderData.paymentMethod}*

*🚚 Livraison*
Ville: *${orderData.deliveryCity}*
Adresse: *${orderData.deliveryAddress}*

*📝 Instructions Spéciales*
${orderData.specialInstructions || 'Aucune'}

*⏱️ Date de Commande*
${new Date().toLocaleString('fr-FR', { timeZone: 'Africa/Abidjan' })}
`;
}

// Endpoint pour recevoir les commandes
app.post('/api/orders', async (req, res) => {
  try {
    const orderData = req.body;
    
    // Validation basique
    if (!orderData.name || !orderData.phone || !orderData.items) {
      return res.status(400).json({ success: false, message: 'Données de commande incomplètes' });
    }
    
    // Formater le message
    const message = formatOrderMessage(orderData);
    
    // Envoyer à Telegram
    const telegramResponse = await fetch(
      `https://api.telegram.org/bot${TELEGRAM_TOKEN}/sendMessage`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: CHAT_ID,
          text: message,
          parse_mode: 'Markdown'
        }),
      }
    );
    
    const telegramData = await telegramResponse.json();
    
    if (telegramData.ok) {
      // Enregistrer la commande dans votre système si nécessaire
      
      return res.status(200).json({ 
        success: true, 
        message: 'Commande reçue et notification envoyée' 
      });
    } else {
      console.error('Erreur Telegram:', telegramData);
      return res.status(500).json({ 
        success: false, 
        message: 'Erreur lors de l\'envoi de la notification' 
      });
    }
    
  } catch (error) {
    console.error('Erreur serveur:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Erreur serveur lors du traitement de la commande' 
    });
  }
});

// Route de test pour vérifier que le serveur fonctionne
app.get('/api/test', (req, res) => {
  res.json({ message: 'Le serveur fonctionne correctement!' });
});

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`Serveur en écoute sur le port ${PORT}`);
  console.log(`Test du serveur: http://localhost:${PORT}/api/test`);
});
