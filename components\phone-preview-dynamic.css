/* Styles dynamiques pour le composant PhonePreview */

.phone-preview-container {
  position: relative;
  margin: 0 auto;
}

.phone-preview-dimensions {
  width: var(--phone-width, 280px);
  height: var(--phone-height, 560px);
}

.phone-preview-background {
  background-color: var(--background-color, transparent);
}

.phone-preview-image-transform {
  transform: 
    scale(var(--image-zoom, 1)) 
    rotate(var(--image-rotation, 0deg)) 
    translateX(var(--image-offset-x, 0%)) 
    translateY(var(--image-offset-y, 0%));
  transition: transform 0.2s ease-in-out;
}

.phone-preview-text {
  color: var(--text-color, #000);
  font-size: var(--text-size, 16px);
  transform: 
    translateX(var(--text-pos-x, 0%)) 
    translateY(var(--text-pos-y, 0%)) 
    rotate(var(--text-rotation, 0deg));
  transition: transform 0.2s ease-in-out, color 0.2s ease-in-out, font-size 0.2s ease-in-out;
}
