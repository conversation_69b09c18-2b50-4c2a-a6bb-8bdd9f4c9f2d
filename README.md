# HCP-DESIGN CI - Site de coques personnalisées

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/wallymelodyboxs-projects/v0-custom-phone-case-website-2y)
[![Built with Next.js](https://img.shields.io/badge/Built%20with-Next.js-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![Database: Supabase](https://img.shields.io/badge/Database-Supabase-green?style=for-the-badge&logo=supabase)](https://supabase.com/)

## Aperçu

HCP-DESIGN CI est un site de vente de coques personnalisées pour smartphones. Les utilisateurs peuvent personnaliser leurs coques avec des designs uniques et commander facilement.

## Fonctionnalités principales

- **Personnalisation de coques** : Interface intuitive pour créer des designs personnalisés
- **Panier sans connexion** : Les visiteurs peuvent ajouter des articles au panier sans créer de compte
- **Paiement par QR code** : Options de paiement via Wave et Orange Money
- **Designs personnalisés** : Les designs créés par les utilisateurs sont attachés à leurs commandes
- **Responsive** : Interface adaptée à tous les appareils

## Nouvelles fonctionnalités

### Panier sans connexion
Les visiteurs peuvent désormais ajouter des articles au panier sans avoir à créer un compte ou se connecter. Le panier est sauvegardé localement et synchronisé avec Supabase.

### Options de paiement par QR code
- **Wave** : Paiement via QR code Wave avec lien alternatif
- **Orange Money** : Paiement via QR code Orange Money avec numéro de téléphone et upload de capture d'écran

### Designs personnalisés liés aux commandes
Les designs créés par les utilisateurs dans la section personnalisation sont maintenant attachés à leurs commandes avec les références choisies (modèle, qualité).

## Technologies utilisées

- **Frontend** : Next.js, React, Tailwind CSS
- **Backend** : Supabase (PostgreSQL, Auth, Storage)
- **Déploiement** : Vercel

## Installation et configuration

1. Cloner le dépôt
   ```bash
   git clone https://github.com/votre-utilisateur/hcp-design-ci.git
   cd hcp-design-ci
   ```

2. Installer les dépendances
   ```bash
   npm install
   ```

3. Configurer les variables d'environnement
   Créez un fichier `.env.local` avec les variables suivantes :
   ```
   NEXT_PUBLIC_SUPABASE_URL=votre-url-supabase
   NEXT_PUBLIC_SUPABASE_ANON_KEY=votre-clé-anon-supabase
   ```

4. Appliquer les migrations Supabase
   ```bash
   node scripts/apply-migrations.js
   ```

5. Lancer le serveur de développement
   ```bash
   npm run dev
   ```

## Déploiement

Le projet est déployé sur Vercel et se met à jour automatiquement à chaque push sur la branche principale.

**[https://vercel.com/wallymelodyboxs-projects/v0-custom-phone-case-website-2y](https://vercel.com/wallymelodyboxs-projects/v0-custom-phone-case-website-2y)**