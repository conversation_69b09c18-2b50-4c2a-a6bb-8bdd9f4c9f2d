/**
 * Données statiques pour les images des modèles de téléphone
 * Ce fichier remplace la lecture dynamique du système de fichiers
 * qui n'est pas compatible avec l'environnement Vercel
 */

import { PhoneModelImage } from '@/services/phone-model-images';

// Structure des données :
// {
//   [modelId: string]: {
//     images: PhoneModelImage[]
//   }
// }

export const phoneModelImagesData: Record<string, { images: PhoneModelImage[] }> = {
  // iPhone
  'iphone16promax': {
    images: [
      {
        id: 'static-iphone16promax-1',
        modelId: 'iphone16promax',
        imageUrl: '/images/phone-cases/iphone/iphone-16-pro-max/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone16pro': {
    images: [
      {
        id: 'static-iphone16pro-1',
        modelId: 'iphone16pro',
        imageUrl: '/images/phone-cases/iphone/iphone-16-pro/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone16plus': {
    images: [
      {
        id: 'static-iphone16plus-1',
        modelId: 'iphone16plus',
        imageUrl: '/images/phone-cases/iphone/iphone-16-plus/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone16': {
    images: [
      {
        id: 'static-iphone16-1',
        modelId: 'iphone16',
        imageUrl: '/images/phone-cases/iphone/iphone-16/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone15promax': {
    images: [
      {
        id: 'static-iphone15promax-1',
        modelId: 'iphone15promax',
        imageUrl: '/images/phone-cases/iphone/iphone-15-pro-max/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone15pro': {
    images: [
      {
        id: 'static-iphone15pro-1',
        modelId: 'iphone15pro',
        imageUrl: '/images/phone-cases/iphone/iphone-15-pro/frame.png?v=' + Date.now(),
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone15plus': {
    images: [
      {
        id: 'static-iphone15plus-1',
        modelId: 'iphone15plus',
        imageUrl: '/images/phone-cases/iphone/iphone-15-plus/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone15': {
    images: [
      {
        id: 'static-iphone15-1',
        modelId: 'iphone15',
        imageUrl: '/images/phone-cases/iphone/iphone-15/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone14promax': {
    images: [
      {
        id: 'static-iphone14promax-1',
        modelId: 'iphone14promax',
        imageUrl: '/images/phone-cases/iphone/iphone-14-pro-max/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone14pro': {
    images: [
      {
        id: 'static-iphone14pro-1',
        modelId: 'iphone14pro',
        imageUrl: '/images/phone-cases/iphone/iphone-14-pro/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone14plus': {
    images: [
      {
        id: 'static-iphone14plus-1',
        modelId: 'iphone14plus',
        imageUrl: '/images/phone-cases/iphone/iphone-14-plus/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone14': {
    images: [
      {
        id: 'static-iphone14-1',
        modelId: 'iphone14',
        imageUrl: '/images/phone-cases/iphone/iphone-14/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone13promax': {
    images: [
      {
        id: 'static-iphone13promax-1',
        modelId: 'iphone13promax',
        imageUrl: '/images/phone-cases/iphone/iphone-13-pro-max/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'iphone13pro': {
    images: [
      {
        id: 'static-iphone13pro-1',
        modelId: 'iphone13pro',
        imageUrl: '/images/phone-cases/iphone/iphone-13-pro/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },

  // Samsung
  'samsungs25ultra': {
    images: [
      {
        id: 'static-samsungs25ultra-1',
        modelId: 'samsungs25ultra',
        imageUrl: '/images/phone-cases/samsung/galaxy-s25-ultra/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'samsungs25plus': {
    images: [
      {
        id: 'static-samsungs25plus-1',
        modelId: 'samsungs25plus',
        imageUrl: '/images/phone-cases/samsung/galaxy-s25-plus/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'samsungs25': {
    images: [
      {
        id: 'static-samsungs25-1',
        modelId: 'samsungs25',
        imageUrl: '/images/phone-cases/samsung/galaxy-s25/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'samsungs24ultra': {
    images: [
      {
        id: 'static-samsungs24ultra-1',
        modelId: 'samsungs24ultra',
        imageUrl: '/images/phone-cases/samsung/galaxy-s24-ultra/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'samsungs24plus': {
    images: [
      {
        id: 'static-samsungs24plus-1',
        modelId: 'samsungs24plus',
        imageUrl: '/images/phone-cases/samsung/galaxy-s24-plus/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'samsungs24': {
    images: [
      {
        id: 'static-samsungs24-1',
        modelId: 'samsungs24',
        imageUrl: '/images/phone-cases/samsung/galaxy-s24/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },

  // Google Pixel
  'pixel8pro': {
    images: [
      {
        id: 'static-pixel8pro-1',
        modelId: 'pixel8pro',
        imageUrl: '/images/phone-cases/google/pixel-8-pro/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  },
  'pixel8': {
    images: [
      {
        id: 'static-pixel8-1',
        modelId: 'pixel8',
        imageUrl: '/images/phone-cases/google/pixel-8/frame.png',
        isDefault: true,
        name: 'frame'
      }
    ]
  }
};
