#!/bin/bash

# Script pour créer un fichier placeholder.png simple
# Ce script utilise ImageMagick pour créer une image PNG

# Vérifier si ImageMagick est installé
if ! command -v convert &> /dev/null; then
    echo "ImageMagick n'est pas installé. Installation en cours..."
    apt-get update && apt-get install -y imagemagick
fi

# Créer un placeholder PNG simple
convert -size 300x400 xc:#F3F4F6 \
    -fill "#E5E7EB" -draw "roundrectangle 75,150 225,250 8,8" \
    -fill "#9CA3AF" -draw "circle 110,180 110,190" \
    -stroke "#9CA3AF" -strokewidth 4 -draw "path 'M 125,200 L 150,175 L 175,200 L 200,175'" \
    -pointsize 14 -gravity center -fill "#6B7280" -annotate +0+30 "Image à venir" \
    -pointsize 12 -gravity center -fill "#9CA3AF" -annotate +0+50 "HCP-DESIGN CI" \
    ./public/images/placeholder.png

echo "Placeholder PNG créé avec succès"

# Créer des placeholders spécifiques pour les différents types d'images
mkdir -p ./public/images/phone-cases
mkdir -p ./public/images/banners
mkdir -p ./public/images/testimonials

cp ./public/images/placeholder.png ./public/images/phone-cases/phone-case-placeholder.png
cp ./public/images/placeholder.png ./public/images/banners/banner-placeholder.png
cp ./public/images/placeholder.png ./public/images/testimonials/testimonial-placeholder.png

echo "Placeholders spécifiques créés avec succès"
