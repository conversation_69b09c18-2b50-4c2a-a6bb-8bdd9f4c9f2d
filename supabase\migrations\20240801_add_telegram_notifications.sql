-- Migration pour ajouter la table telegram_notifications et les fonctionnalités associées

-- C<PERSON>er la table pour stocker les configurations de notification Telegram
CREATE TABLE IF NOT EXISTS public.telegram_notifications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  bot_token TEXT NOT NULL,
  chat_id TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ajouter un index pour accélérer les recherches par nom
CREATE INDEX IF NOT EXISTS idx_telegram_notifications_name ON public.telegram_notifications(name);

-- Activer RLS (Row Level Security)
ALTER TABLE public.telegram_notifications ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON>er une politique pour que seuls les administrateurs puissent gérer les notifications
CREATE POLICY "Admins can manage telegram notifications" 
  ON public.telegram_notifications 
  USING (auth.role() = 'service_role');

-- Créer une fonction pour envoyer une notification Telegram
CREATE OR REPLACE FUNCTION public.send_telegram_notification(
  notification_name TEXT,
  message TEXT,
  parse_mode TEXT DEFAULT 'Markdown'
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  notification_config RECORD;
  http_response JSONB;
  telegram_url TEXT;
BEGIN
  -- Récupérer la configuration de notification
  SELECT * INTO notification_config
  FROM public.telegram_notifications
  WHERE name = notification_name AND is_active = TRUE
  LIMIT 1;
  
  -- Vérifier si la configuration existe
  IF notification_config IS NULL THEN
    RETURN jsonb_build_object(
      'success', FALSE,
      'message', 'Configuration de notification non trouvée ou inactive'
    );
  END IF;
  
  -- Construire l'URL de l'API Telegram
  telegram_url := 'https://api.telegram.org/bot' || notification_config.bot_token || '/sendMessage';
  
  -- Envoyer la requête HTTP à l'API Telegram
  SELECT
    content INTO http_response
  FROM
    http((
      'POST',
      telegram_url,
      ARRAY[http_header('Content-Type', 'application/json')],
      jsonb_build_object(
        'chat_id', notification_config.chat_id,
        'text', message,
        'parse_mode', parse_mode
      )::text,
      5000
    ));
  
  -- Retourner la réponse
  RETURN jsonb_build_object(
    'success', TRUE,
    'response', http_response
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', FALSE,
      'message', SQLERRM
    );
END;
$$;

-- Créer un déclencheur pour envoyer une notification Telegram lors de la création d'une commande
CREATE OR REPLACE FUNCTION public.notify_telegram_on_order_created()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  order_items_json JSONB;
  customer_name TEXT;
  customer_phone TEXT;
  message TEXT;
BEGIN
  -- Récupérer les articles de la commande
  SELECT 
    jsonb_agg(
      jsonb_build_object(
        'name', p.name,
        'quantity', oi.quantity,
        'price', oi.price,
        'customized', oi.customized
      )
    ) INTO order_items_json
  FROM 
    public.order_items oi
    JOIN public.products p ON oi.product_id = p.id
  WHERE 
    oi.order_id = NEW.id;
  
  -- Construire le message
  message := '
*🛒 Nouvelle Commande HCP Design*

*👤 Client*
Nom: *' || COALESCE(NEW.customer_name, 'Non spécifié') || '*
Téléphone: *' || COALESCE(NEW.customer_phone, 'Non spécifié') || '*

*📦 Articles Commandés*
';

  -- Ajouter les articles
  IF order_items_json IS NOT NULL AND jsonb_array_length(order_items_json) > 0 THEN
    FOR i IN 0..jsonb_array_length(order_items_json) - 1 LOOP
      message := message || '- ' || 
                 order_items_json->i->>'quantity' || 'x ' || 
                 order_items_json->i->>'name' || ' (' || 
                 order_items_json->i->>'price' || ' Fr)' || E'\n';
    END LOOP;
  ELSE
    message := message || 'Aucun article trouvé' || E'\n';
  END IF;

  -- Ajouter les informations de paiement et livraison
  message := message || '
*💰 Paiement*
Montant Total: *' || NEW.total || ' Fr*
Méthode de Paiement: *' || NEW.payment_method || '*
Message de Paiement: ' || COALESCE(NEW.payment_message, 'Non spécifié') || '

*🚚 Livraison*
Ville: *' || NEW.shipping_city || '*
Adresse: *' || NEW.shipping_address || '*
Notes: ' || COALESCE(NEW.shipping_notes, 'Aucune') || '

*⏱️ Date de Commande*
' || to_char(NEW.created_at, 'DD/MM/YYYY, HH24:MI:SS');

  -- Envoyer la notification
  PERFORM public.send_telegram_notification('order_notification', message);
  
  RETURN NEW;
END;
$$;

-- Créer le déclencheur sur la table des commandes
DROP TRIGGER IF EXISTS trigger_order_telegram_notification ON public.orders;
CREATE TRIGGER trigger_order_telegram_notification
  AFTER INSERT ON public.orders
  FOR EACH ROW
  EXECUTE FUNCTION public.notify_telegram_on_order_created();

-- Insérer la configuration par défaut pour les notifications de commande
INSERT INTO public.telegram_notifications (name, description, bot_token, chat_id, is_active)
VALUES (
  'order_notification',
  'Notification Telegram pour les nouvelles commandes',
  '**********************************************',
  'VOTRE_CHAT_ID', -- À remplacer par votre chat_id réel
  TRUE
)
ON CONFLICT (name) DO UPDATE
SET 
  description = EXCLUDED.description,
  bot_token = EXCLUDED.bot_token,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- Activer l'extension http si elle n'est pas déjà activée
CREATE EXTENSION IF NOT EXISTS http;
