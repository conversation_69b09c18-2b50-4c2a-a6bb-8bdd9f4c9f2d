# Correction de l'erreur supabaseApi.siteSettings

Ce document explique comment corriger l'erreur `supabaseApi.siteSettings is undefined` qui se produit dans le fichier `hooks\use-site-settings.tsx`.

## Problème

L'erreur se produit car la méthode `siteSettings` n'est pas définie dans l'objet `supabaseApi` dans le fichier `lib/supabase.ts`. Cela empêche le chargement des paramètres du site.

## Solution

Nous avons apporté les modifications suivantes pour résoudre ce problème :

1. Ajout de la méthode `siteSettings` à l'objet `supabaseApi` dans le fichier `lib/supabase.ts`
2. Modification du fichier `hooks/use-site-settings.tsx` pour gérer correctement les erreurs et les cas où la table `site_settings` n'existe pas encore
3. Création d'un script SQL pour initialiser la table `site_settings` dans Supabase
4. Création d'un script pour appliquer la migration SQL

## Comment appliquer la solution

### 1. Mettre à jour le fichier `lib/supabase.ts`

Nous avons ajouté la méthode `siteSettings` à l'objet `supabaseApi` dans le fichier `lib/supabase.ts`. Cette méthode contient les fonctions suivantes :

- `getAll()` : Récupère tous les paramètres du site
- `getByKey(key)` : Récupère un paramètre spécifique
- `update(key, value)` : Met à jour un paramètre

### 2. Mettre à jour le fichier `hooks/use-site-settings.tsx`

Nous avons modifié le fichier `hooks/use-site-settings.tsx` pour gérer correctement les erreurs et les cas où la table `site_settings` n'existe pas encore. Les modifications incluent :

- Vérification de l'existence de `supabaseApi.siteSettings` avant de l'utiliser
- Gestion des erreurs lors de l'accès à la table `site_settings`
- Fallback à l'ancienne table `configurations` si la nouvelle table n'existe pas

### 3. Créer la table `site_settings` dans Supabase

Nous avons créé un script SQL pour initialiser la table `site_settings` dans Supabase. Ce script crée la table, définit les politiques de sécurité et insère les paramètres par défaut.

Pour appliquer cette migration, exécutez le script suivant :

```bash
node scripts/apply-site-settings-migration.js
```

### 4. Initialiser les paramètres par défaut

Nous avons créé un script pour initialiser les paramètres par défaut dans la table `site_settings`. Ce script insère les paramètres par défaut dans la table.

Pour exécuter ce script, utilisez la commande suivante :

```bash
node scripts/init-site-settings.js
```

## Vérification

Pour vérifier que la solution fonctionne correctement, redémarrez l'application et vérifiez que les paramètres du site sont correctement chargés. Vous ne devriez plus voir l'erreur `supabaseApi.siteSettings is undefined` dans la console.

## Remarques

- Si vous avez déjà des paramètres dans l'ancienne table `configurations`, ils seront toujours utilisés si la table `site_settings` n'existe pas ou est vide.
- Les paramètres par défaut sont définis dans le fichier `hooks/use-site-settings.tsx` et dans le script SQL.
- Si vous modifiez les paramètres par défaut, assurez-vous de les mettre à jour dans les deux endroits.
