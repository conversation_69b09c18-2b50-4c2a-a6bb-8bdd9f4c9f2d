import { NextRequest, NextResponse } from 'next/server';
import { verifyCode, devVerifyCode } from '@/lib/verification-code-service';
import { isAdminEmail } from '@/lib/admin-config';

// Limite de tentatives de vérification par email (10 par heure)
const MAX_ATTEMPTS_PER_HOUR = 10;
const attemptsByEmail: Record<string, { count: number; resetTime: number }> = {};

export async function POST(request: NextRequest) {
  try {
    // Récupérer l'email et le code du corps de la requête
    const body = await request.json();
    const { email, code } = body;

    if (!email || !code) {
      return NextResponse.json(
        { success: false, message: 'Email ou code manquant' },
        { status: 400 }
      );
    }

    // Vérifier si l'email est autorisé à accéder à l'administration
    if (!isAdminEmail(email)) {
      return NextResponse.json(
        { success: false, message: 'Email non autorisé' },
        { status: 403 }
      );
    }

    // Vérifier la limite de tentatives
    const now = Date.now();
    const emailAttempts = attemptsByEmail[email] || { count: 0, resetTime: now + 3600000 };

    // Réinitialiser le compteur si le temps de réinitialisation est dépassé
    if (now > emailAttempts.resetTime) {
      emailAttempts.count = 0;
      emailAttempts.resetTime = now + 3600000;
    }

    // Vérifier si la limite est atteinte
    if (emailAttempts.count >= MAX_ATTEMPTS_PER_HOUR) {
      const minutesRemaining = Math.ceil((emailAttempts.resetTime - now) / 60000);
      return NextResponse.json(
        {
          success: false,
          message: `Trop de tentatives. Veuillez réessayer dans ${minutesRemaining} minutes.`,
        },
        { status: 429 }
      );
    }

    // Incrémenter le compteur de tentatives
    emailAttempts.count += 1;
    attemptsByEmail[email] = emailAttempts;

    // Déterminer si nous sommes en production ou en développement
    const isProd = process.env.NODE_ENV === 'production';

    // Vérifier le code
    let isValid;
    if (isProd && process.env.SMTP_USER && process.env.SMTP_PASSWORD) {
      // En production avec SMTP configuré
      isValid = await verifyCode(email, code);
    } else {
      // En développement ou sans SMTP
      isValid = await devVerifyCode(email, code);
    }

    if (isValid) {
      // Réinitialiser le compteur de tentatives en cas de succès
      emailAttempts.count = 0;
      attemptsByEmail[email] = emailAttempts;

      return NextResponse.json({
        success: true,
        message: 'Code de vérification valide',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: 'Code de vérification invalide ou expiré',
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Erreur lors de la vérification du code:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Erreur serveur lors de la vérification du code',
      },
      { status: 500 }
    );
  }
}
