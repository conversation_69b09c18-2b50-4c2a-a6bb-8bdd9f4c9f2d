"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Calendar, Gift, Tag, Clock } from "lucide-react"
import TypedAnimatedBanner from "@/components/typed-animated-banner"
import AnimatedBanner from "@/components/animated-banner"
import { getAnimatedBannerImagePaths } from "@/lib/image-paths"
import { useSimpleCart } from "@/hooks/use-simple-cart"

// Type pour les promotions
interface Promotion {
  id: string
  title: string
  description: string
  imageUrl: string
  discount: number
  startDate: string
  endDate: string
  code: string
  category: string
}

export default function PromosPage() {
  const [promotions, setPromotions] = useState<Promotion[]>([])
  const [activeTab, setActiveTab] = useState("all")
  const [isLoading, setIsLoading] = useState(true)
  const { addToCart } = useSimpleCart()

  // Simuler le chargement des promotions depuis une API
  useEffect(() => {
    // Dans une version réelle, ceci serait remplacé par un appel à l'API
    const mockPromotions: Promotion[] = [
      {
        id: "1",
        title: "Spécial Fête des Mères",
        description: "Offrez à votre mère une coque personnalisée avec une photo de famille et bénéficiez de 20% de réduction sur toute commande.",
        imageUrl: "/images/promos/seasonal/fete-des-meres.png?v=1747269269123",
        discount: 20,
        startDate: "2023-05-15",
        endDate: "2023-05-31",
        code: "MAMAN2023",
        category: "seasonal"
      },
      {
        id: "2",
        title: "Offre de Bienvenue",
        description: "15% de réduction sur votre première commande de coque personnalisée.",
        imageUrl: "/images/promos/new/bienvenue.png?v=1747269269123",
        discount: 15,
        startDate: "2023-01-01",
        endDate: "2023-12-31",
        code: "BIENVENUE",
        category: "new"
      },
      {
        id: "3",
        title: "Pack Famille",
        description: "Commandez 3 coques ou plus et bénéficiez de 25% de réduction sur le total.",
        imageUrl: "/images/promos/bundle/pack-famille.png?v=1747269269123",
        discount: 25,
        startDate: "2023-04-01",
        endDate: "2023-06-30",
        code: "FAMILLE3",
        category: "bundle"
      }
    ]

    setTimeout(() => {
      setPromotions(mockPromotions)
      setIsLoading(false)
    }, 1000)
  }, [])

  // Filtrer les promotions en fonction de l'onglet actif
  const filteredPromotions = activeTab === "all"
    ? promotions
    : promotions.filter(promo => promo.category === activeTab)

  const [clickedPromoId, setClickedPromoId] = useState<string | null>(null)

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-2">🏷️ Nos Promotions</h1>
        <p className="text-lg opacity-90">
          Découvrez nos offres spéciales et profitez de réductions exclusives sur nos coques personnalisées.
        </p>
      </div>

      {/* Bannière animée avec transition zoom */}
      <div className="mb-12">
        <AnimatedBanner
          images={getAnimatedBannerImagePaths()}
          title="Spécial Fête des Mères"
          subtitle="Offrez un cadeau unique à votre mère avec une coque personnalisée et profitez de 20% de réduction"
          badge="Offre Limitée"
          code="MAMAN2023"
          interval={4000}
        />
      </div>

      {/* Filtres par catégorie */}
      <Tabs defaultValue="all" className="mb-8" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 max-w-md mx-auto">
          <TabsTrigger value="all">Toutes</TabsTrigger>
          <TabsTrigger value="seasonal">Saisonnières</TabsTrigger>
          <TabsTrigger value="bundle">Packs</TabsTrigger>
          <TabsTrigger value="new">Nouveaux clients</TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Liste des promotions */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 animate-pulse">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-gray-200 rounded-xl h-96"></div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPromotions.map((promo) => (
            <Card 
              key={promo.id}
              className={`overflow-hidden transition-all duration-300 hover:shadow-xl cursor-pointer ${clickedPromoId === promo.id ? 'ring-2 ring-purple-500' : ''}`}
              onClick={() => setClickedPromoId(clickedPromoId === promo.id ? null : promo.id)}
            >
              <CardContent className="p-0">
                <div className="relative aspect-square">
                  {clickedPromoId === promo.id ? (
                    <div className="absolute inset-0 bg-purple-500 text-white p-4 flex flex-col justify-center items-center">
                      <h3 className="font-semibold text-center mb-2">{promo.title}</h3>
                      <p className="text-xs text-center opacity-90 line-clamp-4">
                        {promo.description}
                      </p>
                    </div>
                  ) : (
                    <>
                      <Image
                        src={promo.imageUrl}
                        alt={promo.title}
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        quality={90}
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-purple-600 text-lg px-3 py-1 shadow-md">-{promo.discount}%</Badge>
                      </div>
                    </>
                  )}
                </div>
                <div className="p-4">
                  <h3 className="font-semibold truncate group-hover:text-purple-600 transition-colors">
                    {/* Lien optionnel vers une page de détail de la promo si elle existe */}
                    {promo.title}
                  </h3>
                  <div className="flex items-center text-sm text-gray-500 mt-2 mb-2">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>Valable du {new Date(promo.startDate).toLocaleDateString('fr-FR')} au {new Date(promo.endDate).toLocaleDateString('fr-FR')}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <Tag className="h-4 w-4 mr-2" />
                    <span>Code: <strong>{promo.code}</strong></span>
                  </div>
                  <Button 
                    className="w-full"
                    onClick={(e) => {
                      e.stopPropagation() // Empêche le clic de se propager à la Card
                      const promoProduct = {
                        id: `promo-${promo.id}`,
                        name: promo.title,
                        price: 15000, // Prix de base pour les promotions, à ajuster si nécessaire
                        image_url: promo.imageUrl,
                        category: "Promotions",
                        subcategory: promo.category,
                        customized: false
                      }
                      addToCart(promoProduct, 1)
                    }}
                  >
                    Profiter de l'offre
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {filteredPromotions.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">Aucune promotion disponible dans cette catégorie pour le moment.</p>
        </div>
      )}

      {/* Section d'information */}
      <div className="mt-16 bg-gray-50 rounded-xl p-8">
        <h2 className="text-2xl font-bold mb-6 text-center">Comment utiliser nos codes promo ?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="bg-purple-100 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
              <Gift className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="font-bold mb-2">1. Choisissez votre promotion</h3>
            <p className="text-gray-600">Sélectionnez l'offre qui vous intéresse parmi nos promotions en cours.</p>
          </div>
          <div className="text-center">
            <div className="bg-purple-100 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
              <Tag className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="font-bold mb-2">2. Notez le code promo</h3>
            <p className="text-gray-600">Copiez le code de réduction associé à l'offre choisie.</p>
          </div>
          <div className="text-center">
            <div className="bg-purple-100 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="font-bold mb-2">3. Appliquez-le au panier</h3>
            <p className="text-gray-600">Entrez le code dans le champ dédié lors de la finalisation de votre commande.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
