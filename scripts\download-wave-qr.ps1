# Script pour télécharger le code QR Wave et le logo Wave

# Fonction pour télécharger une image à partir d'une URL
function Download-Image {
    param (
        [string]$url,
        [string]$outputPath
    )

    try {
        # Créer le dossier parent s'il n'existe pas
        $directory = Split-Path -Path $outputPath -Parent
        if (-not (Test-Path -Path $directory)) {
            New-Item -Path $directory -ItemType Directory -Force | Out-Null
        }

        # Télécharger l'image
        Invoke-WebRequest -Uri $url -OutFile $outputPath
        Write-Host "Image téléchargée avec succès: $outputPath"
    }
    catch {
        Write-Host "Erreur lors du téléchargement de l'image: $_"
    }
}

# URL du code QR Wave (base64 depuis la page)
$waveQrBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALkAAAC5AQAAAABc1qPxAAABWElEQVR42u1XsW3EMBCjo8KlR9Am78UM2IAW02+iEVy6EHzhSUqXdAEfSN6F4H8V1Ik88gz7/jnw3vjDGyeAicuFpcY589fMjQ/88PzihoPvZs9zL1wQ5yePpATfQMhk1wNhnEANbgWrVbwEnNfOE9QoB2+cI2IiuJzzpvaNuL6I1d6eM7B8cj5/9aCs8nhNZyjETcZ/9iKt3IVOpu/lcKcxKXiNwHKQbuJa9jep2vEA+y0vfgwEJed2RPf2yKJ5920RgqdiN6i61VXnXqe89lDIOZ31JufU/S41GQbasLkNWP1NbK/tsuk0ansdaep9njoBWs7d4Sj0nudBWnkvlX0OH2fU3g5zfxnX7uWrU22LJN67TDxMeJ5bK7qFurjP+yRjuaWpuM/bAFm74NTBMsA753S4l0yv7u3GdJFOMp1zBlqm4DjTJHGfw7OsCy5Dqvb3t/M/2/gEGyRPhzpUa7oAAAAASUVORK5CYII="

# Chemin de sortie pour le code QR Wave
$waveQrOutputPath = "public\images\payments\wave\qr\wave-qr.png"

# URL du logo Wave (à remplacer par l'URL réelle)
$waveLogoUrl = "https://wave.com/static/images/wave-logo.png"

# Chemin de sortie pour le logo Wave
$waveLogoOutputPath = "public\images\payments\wave\logo\wave.png"

# Extraire l'image base64 et la sauvegarder
try {
    # Extraire la partie base64 après la virgule
    $base64Data = $waveQrBase64 -replace "data:image/png;base64,", ""
    
    # Convertir la chaîne base64 en bytes
    $bytes = [Convert]::FromBase64String($base64Data)
    
    # Créer le dossier parent s'il n'existe pas
    $directory = Split-Path -Path $waveQrOutputPath -Parent
    if (-not (Test-Path -Path $directory)) {
        New-Item -Path $directory -ItemType Directory -Force | Out-Null
    }
    
    # Écrire les bytes dans un fichier
    [System.IO.File]::WriteAllBytes($waveQrOutputPath, $bytes)
    
    Write-Host "Code QR Wave sauvegardé avec succès: $waveQrOutputPath"
}
catch {
    Write-Host "Erreur lors de la sauvegarde du code QR Wave: $_"
}

# Télécharger le logo Wave
# Note: Cette partie est commentée car l'URL n'est pas valide
# Vous devrez remplacer l'URL par l'URL réelle du logo Wave
# Download-Image -url $waveLogoUrl -outputPath $waveLogoOutputPath

Write-Host "Téléchargement terminé !"
