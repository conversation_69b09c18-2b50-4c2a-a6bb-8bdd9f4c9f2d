'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Search, Plus, Edit, Trash2, Image, Eye, Upload } from 'lucide-react';
import { toast } from 'sonner';

interface GalleryDesign {
  id: number;
  titre: string;
  theme: string;
  fichier: string;
  tags: string;
  utilisations: number;
}

export function GalleryDesignManager() {
  const [designs, setDesigns] = useState<GalleryDesign[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [themeFilter, setThemeFilter] = useState<string>('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedDesign, setSelectedDesign] = useState<GalleryDesign | null>(null);
  const [newDesign, setNewDesign] = useState<Partial<GalleryDesign>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Charger les designs
  const loadDesigns = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/designs');
      if (!response.ok) {
        throw new Error('Erreur lors du chargement des designs');
      }
      const data = await response.json();
      // Transformer les données pour correspondre à la structure de la base
      const transformedData = data.map((design: any) => ({
        id: design.id,
        titre: design.title,
        theme: design.category,
        fichier: design.image,
        tags: Array.isArray(design.tags) ? design.tags.join(', ') : design.tags || '',
        utilisations: design.views || 0
      }));
      setDesigns(transformedData);
    } catch (err) {
      console.error('Erreur lors du chargement des designs:', err);
      setError(err instanceof Error ? err.message : 'Erreur inconnue');
      setDesigns([]);
    } finally {
      setLoading(false);
    }
  };

  // Ajouter un design
  const handleAddDesign = async () => {
    try {
      if (!newDesign.titre || !newDesign.theme || !newDesign.fichier) {
        toast.error('Veuillez remplir tous les champs obligatoires');
        return;
      }

      const response = await fetch('/api/designs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newDesign),
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la création du design');
      }

      toast.success('Design ajouté avec succès');
      setIsAddDialogOpen(false);
      setNewDesign({});
      loadDesigns();
    } catch (err) {
      console.error('Erreur lors de l\'ajout du design:', err);
      toast.error('Erreur lors de l\'ajout du design');
    }
  };

  // Modifier un design
  const handleEditDesign = async () => {
    try {
      if (!selectedDesign) return;

      const response = await fetch('/api/designs', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(selectedDesign),
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la modification du design');
      }

      toast.success('Design modifié avec succès');
      setIsEditDialogOpen(false);
      setSelectedDesign(null);
      loadDesigns();
    } catch (err) {
      console.error('Erreur lors de la modification du design:', err);
      toast.error('Erreur lors de la modification du design');
    }
  };

  // Supprimer un design
  const handleDeleteDesign = async (id: number) => {
    try {
      if (!confirm('Êtes-vous sûr de vouloir supprimer ce design ?')) {
        return;
      }

      const response = await fetch(`/api/designs?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression du design');
      }

      toast.success('Design supprimé avec succès');
      loadDesigns();
    } catch (err) {
      console.error('Erreur lors de la suppression du design:', err);
      toast.error('Erreur lors de la suppression du design');
    }
  };

  // Charger les designs au montage
  useEffect(() => {
    loadDesigns();
  }, []);

  // Filtrer les designs
  const filteredDesigns = designs.filter(design => {
    const matchesSearch = design.titre.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         design.tags.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTheme = themeFilter === 'all' || design.theme === themeFilter;
    return matchesSearch && matchesTheme;
  });

  const themes = ['abstract', 'nature', 'geometric', 'text', 'photo', 'pattern', 'minimal', 'space', 'gospel'];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Image className="h-5 w-5" />
            Gestion des Designs de Galerie
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Barre de recherche et filtres */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Rechercher par titre ou tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={themeFilter} onValueChange={setThemeFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par thème" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les thèmes</SelectItem>
                {themes.map(theme => (
                  <SelectItem key={theme} value={theme}>
                    {theme.charAt(0).toUpperCase() + theme.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter un Design
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Ajouter un nouveau design</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="titre">Titre *</Label>
                    <Input
                      id="titre"
                      value={newDesign.titre || ''}
                      onChange={(e) => setNewDesign({...newDesign, titre: e.target.value})}
                      placeholder="Nom du design"
                    />
                  </div>
                  <div>
                    <Label htmlFor="theme">Thème *</Label>
                    <Select value={newDesign.theme || ''} onValueChange={(value) => setNewDesign({...newDesign, theme: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un thème" />
                      </SelectTrigger>
                      <SelectContent>
                        {themes.map(theme => (
                          <SelectItem key={theme} value={theme}>
                            {theme.charAt(0).toUpperCase() + theme.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="fichier">URL du fichier *</Label>
                    <Input
                      id="fichier"
                      value={newDesign.fichier || ''}
                      onChange={(e) => setNewDesign({...newDesign, fichier: e.target.value})}
                      placeholder="/images/gallery/..."
                    />
                  </div>
                  <div>
                    <Label htmlFor="tags">Tags</Label>
                    <Input
                      id="tags"
                      value={newDesign.tags || ''}
                      onChange={(e) => setNewDesign({...newDesign, tags: e.target.value})}
                      placeholder="tag1, tag2, tag3"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddDesign} className="flex-1">
                      Ajouter
                    </Button>
                    <Button variant="outline" onClick={() => setIsAddDialogOpen(false)} className="flex-1">
                      Annuler
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* État de chargement */}
          {loading && (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Chargement des designs...</p>
            </div>
          )}

          {/* État d'erreur */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-600">Erreur: {error}</p>
              <Button onClick={loadDesigns} variant="outline" size="sm" className="mt-2">
                Réessayer
              </Button>
            </div>
          )}

          {/* Tableau des designs */}
          {!loading && !error && (
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Aperçu</TableHead>
                    <TableHead>Titre</TableHead>
                    <TableHead>Thème</TableHead>
                    <TableHead>Tags</TableHead>
                    <TableHead>Utilisations</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDesigns.map((design) => (
                    <TableRow key={design.id}>
                      <TableCell>
                        <img
                          src={design.fichier || '/placeholder.svg'}
                          alt={design.titre}
                          className="w-12 h-12 object-cover rounded"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder.svg';
                          }}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{design.titre}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {design.theme.charAt(0).toUpperCase() + design.theme.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell className="max-w-xs truncate">{design.tags}</TableCell>
                      <TableCell>{design.utilisations}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedDesign(design);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteDesign(design.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {filteredDesigns.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  Aucun design trouvé
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog de modification */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Modifier le design</DialogTitle>
          </DialogHeader>
          {selectedDesign && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-titre">Titre *</Label>
                <Input
                  id="edit-titre"
                  value={selectedDesign.titre}
                  onChange={(e) => setSelectedDesign({...selectedDesign, titre: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="edit-theme">Thème *</Label>
                <Select value={selectedDesign.theme} onValueChange={(value) => setSelectedDesign({...selectedDesign, theme: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {themes.map(theme => (
                      <SelectItem key={theme} value={theme}>
                        {theme.charAt(0).toUpperCase() + theme.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-fichier">URL du fichier *</Label>
                <Input
                  id="edit-fichier"
                  value={selectedDesign.fichier}
                  onChange={(e) => setSelectedDesign({...selectedDesign, fichier: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="edit-tags">Tags</Label>
                <Input
                  id="edit-tags"
                  value={selectedDesign.tags}
                  onChange={(e) => setSelectedDesign({...selectedDesign, tags: e.target.value})}
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleEditDesign} className="flex-1">
                  Sauvegarder
                </Button>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} className="flex-1">
                  Annuler
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}