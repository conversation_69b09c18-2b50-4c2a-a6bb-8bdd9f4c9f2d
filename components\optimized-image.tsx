"use client"

import Image from "next/image"
import { useState } from "react"
import { cn } from "@/lib/utils"
import "@/styles/image-components.css"

interface OptimizedImageProps {
  src: string
  alt: string
  width: number
  height: number
  className?: string
  priority?: boolean
  objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down"
}

// Ensure you import cn if not already: import { cn } from "@/lib/utils";
// No need to import image-components.css here if imported globally

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  objectFit = "cover"
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(false)

  const objectFitClass = {
    "contain": "object-contain",
    "cover": "object-cover",
    "fill": "object-fill",
    "none": "object-none",
    "scale-down": "object-scale-down"
  }[objectFit]

  if (error) {
    return (
      <div
        className={cn("image-error-fallback image-dimensions", className)}
        data-width={width}
        data-height={height}
      >
        Image non disponible
      </div>
    )
  }

  return (
    <div className={cn("image-container", className)}>
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        className={cn(
          objectFitClass,
          isLoading ? "image-loading" : "image-loaded",
          "image-transition"
        )}
        onLoad={() => setIsLoading(false)}
        onError={() => setError(true)}
      />
    </div>
  )
}
