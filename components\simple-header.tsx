"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Menu, Search, Smartphone, X } from "lucide-react"
import MiniCart from "@/components/mini-cart"
import FavoritesMenu from "@/components/favorites-menu"

export default function SimpleHeader() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  
  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)
  const toggleSearch = () => setIsSearchOpen(!isSearchOpen)
  
  return (
    <header className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <img 
              src="/images/logos/hcp-design-logo.png" 
              alt="HCP-DESIGN CI Logo" 
              className="h-8 w-auto mr-2"
            />
            <span className="font-bold text-xl">HCP-DESIGN CI</span>
          </Link>
          
          {/* Navigation desktop */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/promotions" className="text-gray-700 hover:text-purple-600 transition-colors">
              Promotions
            </Link>
            <Link href="/customize" className="text-gray-700 hover:text-purple-600 transition-colors">
              Personnaliser
            </Link>
            <Link href="/cadeaux" className="text-gray-700 hover:text-purple-600 transition-colors">
              🎁 Cadeaux & Gadgets
            </Link>
            <Link href="/products" className="text-gray-700 hover:text-purple-600 transition-colors">
              Tous les produits
            </Link>
            <Link href="/test-cart" className="text-gray-700 hover:text-purple-600 transition-colors">
              Test Panier
            </Link>
          </nav>
          
          {/* Actions */}
          <div className="flex items-center space-x-2">
            {/* Recherche */}
            <Button variant="ghost" size="icon" onClick={toggleSearch} className="hidden md:flex">
              <Search className="h-5 w-5" />
            </Button>
            
            {/* Favoris */}
            <div className="hidden md:block">
              <FavoritesMenu />
            </div>
            
            {/* Panier */}
            <div className="hidden md:block">
              <MiniCart />
            </div>
            
            {/* Menu mobile */}
            <Button variant="ghost" size="icon" onClick={toggleMenu} className="md:hidden">
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>
        
        {/* Barre de recherche */}
        {isSearchOpen && (
          <div className="py-3 border-t border-gray-200">
            <div className="relative">
              <Input
                type="text"
                placeholder="Rechercher un produit..."
                className="pr-10"
              />
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0"
                onClick={toggleSearch}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
      
      {/* Menu mobile */}
      {isMenuOpen && (
        <div className="md:hidden border-t border-gray-200">
          <div className="container mx-auto px-4 py-3">
            <nav className="flex flex-col space-y-3">
              <Link
                href="/promotions"
                className="py-2 text-gray-700 hover:text-purple-600 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Promotions
              </Link>
              <Link
                href="/customize"
                className="py-2 text-gray-700 hover:text-purple-600 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Personnaliser
              </Link>
              <Link
                href="/cadeaux"
                className="py-2 text-gray-700 hover:text-purple-600 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                🎁 Cadeaux & Gadgets
              </Link>
              <Link
                href="/products"
                className="py-2 text-gray-700 hover:text-purple-600 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Tous les produits
              </Link>
              <Link
                href="/test-cart"
                className="py-2 text-gray-700 hover:text-purple-600 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Test Panier
              </Link>
              
              <div className="flex items-center space-x-4 py-2">
                <FavoritesMenu />
                <MiniCart />
              </div>
            </nav>
          </div>
        </div>
      )}
    </header>
  )
}
