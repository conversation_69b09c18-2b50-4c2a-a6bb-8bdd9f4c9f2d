# Script pour générer des placeholders PNG pour les dossiers de promotions
# Ce script utilise le nom du dossier comme nom de fichier

# Types de promotions
$promoTypes = @(
    "seasonal",
    "bundle",
    "new"
)

# Fonction pour générer un PNG placeholder
function New-PNGPlaceholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title,
        [string]$subtitle
    )

    # Créer le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="1200" height="400" viewBox="0 0 1200 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="1200" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="1100" height="300" rx="5" fill="#E5E7EB"/>
  <text x="600" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="600" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force

    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force

    Write-Host "Créé: $filePath"
}

# Créer des placeholders pour les types de promotions
foreach ($type in $promoTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)

    # Créer l'image principale du dossier
    New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName $type -title "Promotion $typeName" -subtitle "Image principale"

    # Créer des exemples d'images pour chaque type
    if ($type -eq "seasonal") {
        New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName "fete-des-meres" -title "Fête des Mères" -subtitle "Promotion saisonnière"
        New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName "rentree-scolaire" -title "Rentrée Scolaire" -subtitle "Promotion saisonnière"
        New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName "noel" -title "Noël" -subtitle "Promotion saisonnière"
    }
    elseif ($type -eq "bundle") {
        New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName "pack-famille" -title "Pack Famille" -subtitle "3 coques ou plus"
        New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName "pack-duo" -title "Pack Duo" -subtitle "2 coques identiques"
        New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName "pack-accessoires" -title "Pack Accessoires" -subtitle "Coque + accessoires"
    }
    elseif ($type -eq "new") {
        New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName "bienvenue" -title "Offre de Bienvenue" -subtitle "Première commande"
        New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName "parrainage" -title "Parrainage" -subtitle "Invitez vos amis"
        New-PNGPlaceholder -folderPath "public\images\promos\$type" -fileName "inscription" -title "Inscription Newsletter" -subtitle "Réduction immédiate"
    }
}

# Générer des bannières animées pour les promotions
function New-AnimatedBannerImages {
    param (
        [string]$bannerType,
        [int]$count = 3
    )

    $directory = "public\images\promos"

    # Vérifier si le dossier existe
    if (-not (Test-Path -Path $directory)) {
        Write-Host "Le dossier $directory n'existe pas."
        return
    }

    # Créer les images de la bannière animée
    for ($i = 1; $i -le $count; $i++) {
        $filePath = Join-Path -Path $directory -ChildPath "banner$i.png"

        # Vérifier si le fichier existe déjà
        if (Test-Path -Path $filePath) {
            Write-Host "Le fichier $filePath existe déjà."
            continue
        }

        # Créer une image pour la bannière animée
        $title = "Bannière Promotionnelle $i"
        $subtitle = "Image $i pour animation"

        # Créer un fichier SVG temporaire
        $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
        $svgContent = @"
<svg width="1200" height="400" viewBox="0 0 1200 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="1200" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="1100" height="300" rx="5" fill="#E5E7EB"/>
  <text x="600" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="600" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
        Set-Content -Path $tempSvgPath -Value $svgContent -Force

        # Copier le fichier SVG avec l'extension PNG
        Copy-Item -Path $tempSvgPath -Destination $filePath -Force

        # Supprimer le fichier temporaire
        Remove-Item -Path $tempSvgPath -Force

        Write-Host "Créé: $filePath"
    }
}

# Générer des bannières animées pour les promotions
New-AnimatedBannerImages -bannerType "promos" -count 3

Write-Host "Génération des placeholders terminée !"
