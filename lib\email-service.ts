import nodemailer from 'nodemailer';

// Configuration du transporteur SMTP
const smtpConfig = {
  host: process.env.SMTP_HOST || 'smtp.mailersend.net',
  port: parseInt(process.env.SMTP_PORT || '587', 10),
  secure: false, // true pour 465, false pour les autres ports
  auth: {
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASSWORD || '',
  },
};

// Créer le transporteur
const transporter = nodemailer.createTransport(smtpConfig);

// Vérifier si la configuration SMTP est valide
if (!process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
  console.warn('Configuration SMTP incomplète. L\'envoi d\'emails ne fonctionnera pas correctement.');
}

// Email de l'expéditeur
const fromEmail = process.env.MAILERSEND_FROM_EMAIL || '<EMAIL>';
const fromName = process.env.MAILERSEND_FROM_NAME || 'HCP Design';

/**
 * Envoie un email avec un code de vérification via SMTP
 * @param to Email du destinataire
 * @param code Code de vérification
 * @returns Promise<boolean> Indique si l'email a été envoyé avec succès
 */
export async function sendVerificationEmail(to: string, code: string): Promise<boolean> {
  try {
    // Vérifier si la configuration SMTP est valide
    if (!process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
      console.error('Configuration SMTP incomplète. Impossible d\'envoyer l\'email.');
      return false;
    }

    // Contenu HTML de l'email
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #6c5ce7; margin-bottom: 10px;">HCP Design</h1>
          <h2 style="color: #333; margin-top: 0;">Code de vérification</h2>
        </div>

        <p style="color: #555; font-size: 16px; line-height: 1.5;">
          Bonjour,
        </p>

        <p style="color: #555; font-size: 16px; line-height: 1.5;">
          Vous avez demandé à vous connecter à l'espace d'administration de HCP Design.
          Veuillez utiliser le code ci-dessous pour compléter votre connexion :
        </p>

        <div style="text-align: center; margin: 30px 0;">
          <div style="display: inline-block; padding: 15px 30px; background-color: #f5f5f5; border-radius: 5px; letter-spacing: 5px; font-size: 24px; font-weight: bold; color: #333;">
            ${code}
          </div>
        </div>

        <p style="color: #555; font-size: 16px; line-height: 1.5;">
          Ce code est valable pendant <strong>10 minutes</strong>. Si vous n'avez pas demandé ce code,
          veuillez ignorer cet email ou contacter l'administrateur.
        </p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 14px;">
          <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
          <p>&copy; ${new Date().getFullYear()} HCP Design. Tous droits réservés.</p>
        </div>
      </div>
    `;

    // Contenu texte de l'email (version sans HTML)
    const textContent = `Votre code de vérification est : ${code}. Ce code est valable pendant 10 minutes.`;

    // Options de l'email
    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to,
      subject: 'Code de vérification pour HCP Design',
      text: textContent,
      html: htmlContent,
    };

    // Envoyer l'email
    const info = await transporter.sendMail(mailOptions);
    console.log('Email envoyé avec succès via SMTP:', info.messageId);
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'envoi de l\'email via SMTP:', error);
    return false;
  }
}

/**
 * Fonction de secours pour simuler l'envoi d'email en développement
 * @param to Email du destinataire
 * @param code Code de vérification
 * @returns Promise<boolean> Toujours true en développement
 */
export async function simulateSendVerificationEmail(to: string, code: string): Promise<boolean> {
  console.log(`[SIMULATION] Email envoyé à ${to} avec le code: ${code}`);
  // Afficher une alerte en développement pour faciliter les tests
  if (typeof window !== 'undefined') {
    alert(`Code de vérification: ${code}`);
  }
  return true;
}

/**
 * Envoie un email de vérification, utilise la simulation en développement si SMTP n'est pas configuré
 * @param to Email du destinataire
 * @param code Code de vérification
 * @returns Promise<boolean> Indique si l'email a été envoyé avec succès
 */
export async function sendVerificationCode(to: string, code: string): Promise<boolean> {
  // Utiliser SMTP si configuré, sinon utiliser la simulation
  if (process.env.SMTP_USER && process.env.SMTP_PASSWORD) {
    return sendVerificationEmail(to, code);
  } else {
    return simulateSendVerificationEmail(to, code);
  }
}
