/**
 * Script pour vérifier l'alignement avec Supabase
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Créer un client Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Les variables d\'environnement NEXT_PUBLIC_SUPABASE_URL et SUPABASE_SERVICE_KEY doivent être définies.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Fonction pour vérifier si une table existe
async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('count(*)', { count: 'exact', head: true });
    
    if (error) {
      if (error.code === 'PGRST116') {
        return { exists: false, message: `La table '${tableName}' n'existe pas.` };
      }
      return { exists: false, message: `Erreur lors de la vérification de la table '${tableName}': ${error.message}` };
    }
    
    return { exists: true, message: `La table '${tableName}' existe.` };
  } catch (error) {
    return { exists: false, message: `Erreur lors de la vérification de la table '${tableName}': ${error.message}` };
  }
}

// Fonction pour vérifier les colonnes d'une table
async function checkTableColumns(tableName) {
  try {
    const { data, error } = await supabase.rpc('get_table_columns', { table_name: tableName });
    
    if (error) {
      return { success: false, message: `Erreur lors de la vérification des colonnes de la table '${tableName}': ${error.message}` };
    }
    
    return { success: true, columns: data };
  } catch (error) {
    return { success: false, message: `Erreur lors de la vérification des colonnes de la table '${tableName}': ${error.message}` };
  }
}

// Fonction pour vérifier les données d'une table
async function checkTableData(tableName, limit = 5) {
  try {
    const { data, error, count } = await supabase
      .from(tableName)
      .select('*', { count: 'exact' })
      .limit(limit);
    
    if (error) {
      return { success: false, message: `Erreur lors de la vérification des données de la table '${tableName}': ${error.message}` };
    }
    
    return { success: true, count, data };
  } catch (error) {
    return { success: false, message: `Erreur lors de la vérification des données de la table '${tableName}': ${error.message}` };
  }
}

// Fonction principale
async function main() {
  console.log('Vérification de l\'alignement avec Supabase...\n');
  
  // Liste des tables à vérifier
  const tables = [
    'products',
    'categories',
    'collections',
    'orders',
    'order_items',
    'users',
    'cart_items',
    'favorites',
    'payment_methods'
  ];
  
  // Vérifier chaque table
  for (const table of tables) {
    console.log(`\n=== Table: ${table} ===`);
    
    // Vérifier si la table existe
    const tableExists = await checkTableExists(table);
    console.log(tableExists.message);
    
    if (tableExists.exists) {
      // Vérifier les colonnes de la table
      const columnsResult = await checkTableColumns(table);
      
      if (columnsResult.success) {
        console.log(`Colonnes de la table '${table}':`);
        columnsResult.columns.forEach(column => {
          console.log(`  - ${column.column_name} (${column.data_type}${column.is_nullable === 'NO' ? ', NOT NULL' : ''})`);
        });
      } else {
        console.log(columnsResult.message);
      }
      
      // Vérifier les données de la table
      const dataResult = await checkTableData(table);
      
      if (dataResult.success) {
        console.log(`\nNombre total d'enregistrements: ${dataResult.count}`);
        
        if (dataResult.count > 0) {
          console.log(`Exemple de données (${Math.min(dataResult.data.length, 5)} premiers enregistrements):`);
          dataResult.data.forEach((record, index) => {
            console.log(`  - Enregistrement #${index + 1}: ${JSON.stringify(record).substring(0, 100)}...`);
          });
        } else {
          console.log('La table ne contient aucune donnée.');
        }
      } else {
        console.log(dataResult.message);
      }
    }
  }
  
  console.log('\n=== Vérification des fonctions RPC ===');
  
  // Liste des fonctions RPC à vérifier
  const rpcFunctions = [
    'get_table_columns'
  ];
  
  for (const func of rpcFunctions) {
    try {
      const { data, error } = await supabase.rpc(func, { table_name: 'products' });
      
      if (error) {
        console.log(`❌ La fonction '${func}' n'est pas disponible ou a généré une erreur: ${error.message}`);
      } else {
        console.log(`✅ La fonction '${func}' est disponible.`);
      }
    } catch (error) {
      console.log(`❌ Erreur lors de la vérification de la fonction '${func}': ${error.message}`);
    }
  }
  
  console.log('\n=== Vérification des buckets de stockage ===');
  
  // Liste des buckets de stockage à vérifier
  const buckets = [
    'images',
    'uploads'
  ];
  
  for (const bucket of buckets) {
    try {
      const { data, error } = await supabase.storage.getBucket(bucket);
      
      if (error) {
        console.log(`❌ Le bucket '${bucket}' n'existe pas ou n'est pas accessible: ${error.message}`);
      } else {
        console.log(`✅ Le bucket '${bucket}' existe.`);
        
        // Vérifier les fichiers dans le bucket
        const { data: files, error: filesError } = await supabase.storage.from(bucket).list();
        
        if (filesError) {
          console.log(`  ❌ Erreur lors de la récupération des fichiers du bucket '${bucket}': ${filesError.message}`);
        } else {
          console.log(`  Nombre de fichiers/dossiers à la racine: ${files.length}`);
          
          if (files.length > 0) {
            console.log('  Exemples de fichiers/dossiers:');
            files.slice(0, 5).forEach(file => {
              console.log(`    - ${file.name} (${file.metadata ? 'Fichier' : 'Dossier'})`);
            });
          }
        }
      }
    } catch (error) {
      console.log(`❌ Erreur lors de la vérification du bucket '${bucket}': ${error.message}`);
    }
  }
  
  console.log('\nVérification terminée.');
}

// Exécuter la fonction principale
main().catch(error => {
  console.error('Erreur non gérée:', error);
  process.exit(1);
});
