// Types pour les modèles de téléphone
export interface PhoneModel {
  id: string;
  name: string;
  image: string;
  dimensions: string;
  releaseYear: number;
  popular: boolean;
  availableQualities?: string[]; // Qualités disponibles pour ce modèle
}

// Types pour les marques de téléphone
export interface PhoneBrand {
  name: string;
  logo: string;
  models: PhoneModel[];
  defaultQualities?: string[]; // Qualités disponibles par défaut pour tous les modèles de cette marque
}

// Type pour les modèles à venir
export interface UpcomingModel {
  id: string;
  name: string;
  brand: string;
  image: string;
  expectedRelease: string;
}
