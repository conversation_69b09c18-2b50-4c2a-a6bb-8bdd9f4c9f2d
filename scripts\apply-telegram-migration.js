// Script pour appliquer la migration Telegram à Supabase
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Vérifier les variables d'environnement
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Erreur: Variables d\'environnement manquantes.');
  console.error('Assurez-vous que NEXT_PUBLIC_SUPABASE_URL et SUPABASE_SERVICE_KEY sont définis dans .env.local');
  process.exit(1);
}

// Créer le client Supabase avec la clé de service
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Fonction pour exécuter le SQL
async function executeSql(sql) {
  try {
    // Diviser le script en instructions individuelles
    const statements = sql
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);
    
    console.log(`Exécution de ${statements.length} instructions SQL...`);
    
    // Exécuter chaque instruction
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Exécution de l'instruction ${i + 1}/${statements.length}...`);
      
      // Utiliser la fonction rpc pour exécuter le SQL
      const { error } = await supabase.rpc('exec_sql', { sql_query: statement + ';' });
      
      if (error) {
        console.error(`Erreur lors de l'exécution de l'instruction ${i + 1}:`, error);
        console.error('Instruction SQL:', statement);
      }
    }
    
    console.log('Toutes les instructions ont été exécutées.');
  } catch (err) {
    console.error('Exception lors de l\'exécution des requêtes SQL:', err);
  }
}

// Fonction principale
async function main() {
  try {
    // Lire le fichier SQL
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20240801_add_telegram_notifications.sql');
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('Lecture du fichier de migration réussie.');
    console.log('Application de la migration Telegram...');
    
    // Exécuter le SQL
    await executeSql(sql);
    
    console.log('Migration Telegram appliquée avec succès!');
    
    // Vérifier que la table a été créée
    const { data, error } = await supabase
      .from('telegram_notifications')
      .select('*');
    
    if (error) {
      console.error('Erreur lors de la vérification de la table telegram_notifications:', error);
    } else {
      console.log('Table telegram_notifications créée avec succès!');
      console.log('Configurations de notification:', data);
    }
    
  } catch (error) {
    console.error('Erreur lors de l\'application de la migration:', error);
  }
}

// Exécuter le script
main();
