"use client"

import { useEffect, useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Trash2, ShoppingBag, ArrowLeft, CreditCard } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import CheckoutForm from "@/components/payment/checkout-form"
import SimpleLogin from "@/components/auth/simple-login"
import styles from "./cart.module.css"

export default function CartPage() {
  const { items, subtotal, shippingCost, total, updateQuantity, removeFromCart, clearCart } = useSimpleCart()
  const [isCheckout, setIsCheckout] = useState(false)
  const [showLogin, setShowLogin] = useState(false)
  const { toast } = useToast()

  // Fonction pour formater les prix
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF'
    }).format(price)
  }

  // Fonction pour mettre à jour la quantité d'un article
  const handleUpdateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return
    updateQuantity(itemId, newQuantity)
  }

  // Fonction pour supprimer un article
  const handleRemoveItem = (itemId: string) => {
    removeFromCart(itemId)
  }

  // Frais de livraison fixes (si non défini dans le hook)
  const shipping = shippingCost || 2000

  if (items.length === 0) {
    return (
      <div className="container mx-auto p-8 text-center">
        <ShoppingBag className="w-16 h-16 mx-auto text-gray-400" />
        <h1 className="text-2xl font-bold mt-4">Votre panier est vide</h1>
        <p className="text-gray-600 mt-2">Découvrez nos produits et commencez vos achats</p>
        <Link href="/products">
          <Button className="mt-6 bg-purple-600 hover:bg-purple-700">
            Voir les produits
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-8">
      <div className="flex items-center gap-4 mb-8">
        <Link href="/products">
          <Button variant="outline" className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            Continuer les achats
          </Button>
        </Link>
      </div>

      <h1 className="text-3xl font-bold mb-8">Votre panier</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Liste des articles */}
        <div className="lg:col-span-2 space-y-4">
          {items.map((item) => (
            <Card key={item.id} className="p-4">
              <div className="flex gap-4">
                <div className="relative h-24 w-24 flex-shrink-0">
                  <Image
                    src={item.image_url || "/placeholder.png"}
                    alt={item.name || "Produit"}
                    fill
                    style={{ objectFit: "cover" }}
                    className="rounded-md"
                  />
                </div>

                <div className="flex-grow">
                  <h3 className="font-semibold">{item.name}</h3>
                  {item.customized && (
                    <p className="text-sm text-purple-600">Personnalisé</p>
                  )}
                  <p className="text-purple-600 font-bold mt-1">
                    {formatPrice(item.price || 0)}
                  </p>

                  <div className="flex items-center gap-4 mt-2">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                      >
                        -
                      </Button>
                      <Input
                        type="number"
                        min="1"
                        value={item.quantity}
                        onChange={(e) => handleUpdateQuantity(item.id, parseInt(e.target.value) || 1)}
                        className="w-16 text-center"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                      >
                        +
                      </Button>
                    </div>

                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveItem(item.id)}
                      className="text-red-500 hover:text-red-600"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <div className="text-right">
                  <p className="font-bold">
                    {formatPrice((item.price || 0) * item.quantity)}
                  </p>
                </div>
              </div>

              {/* Afficher les détails de personnalisation si présents */}
              {item.customized && item.customization_data && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <p className="text-sm font-medium">Détails de personnalisation:</p>
                  <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
                    {item.customization_data.model && (
                      <div>
                        <span className="text-gray-500">Modèle:</span> {item.customization_data.model}
                      </div>
                    )}
                    {item.customization_data.backgroundColor && (
                      <div>
                        <span className="text-gray-500">Couleur:</span>
                        <span
                          className={`${styles.colorSwatch} color-preview`}
                          data-color={item.customization_data.backgroundColor}
                        ></span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </Card>
          ))}
        </div>

        {/* Résumé de la commande */}
        <div className="lg:col-span-1">
          <Card className="p-6 sticky top-24">
            <h2 className="text-xl font-bold mb-4">Résumé de la commande</h2>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Sous-total</span>
                <span>{formatPrice(subtotal)}</span>
              </div>
              <div className="flex justify-between">
                <span>Livraison</span>
                <span>{formatPrice(shipping)}</span>
              </div>
              <Separator className="my-4" />
              <div className="flex justify-between font-bold text-lg">
                <span>Total</span>
                <span>{formatPrice(total)}</span>
              </div>
            </div>

            <Button
              className="w-full mt-4 bg-green-600 hover:bg-green-700 flex items-center justify-center gap-2"
              onClick={() => {
                // Ici, vous pouvez ouvrir une page de paiement ou afficher un message
                // Par défaut, on redirige vers la page checkout
                window.location.href = "/checkout"
              }}
            >
              <CreditCard className="w-4 h-4" />
              Commander
            </Button>
            <Button
              className="w-full mt-6 bg-purple-600 hover:bg-purple-700 flex items-center justify-center gap-2"
              onClick={() => clearCart()}
            >
              Vider le panier
            </Button>
          </Card>
        </div>
      </div>
    </div>
  )
}
