// Types pour les modèles de téléphone
interface PhoneModel {
  id: string;
  name: string;
  image: string;
  dimensions: string;
  releaseYear: number;
  popular: boolean;
  availableQualities?: string[]; // Qualités disponibles pour ce modèle
}

// Types pour les marques de téléphone
interface PhoneBrand {
  name: string;
  logo: string;
  models: PhoneModel[];
  defaultQualities?: string[]; // Qualités disponibles par défaut pour tous les modèles de cette marque
}

// Données des modèles de téléphone par marque
export const phoneData: Record<string, PhoneBrand> = {
  apple: {
    name: "Apple",
    logo: "/images/brands/apple.svg",
    defaultQualities: ["Vip-1"],
    models: [
      // iPhone 16 Series
      {
        id: "iphone16promax",
        name: "iPhone 16 Pro Max",
        image: "/placeholder.svg",
        dimensions: "163.0 x 77.6 x 8.3 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone16pro",
        name: "iPhone 16 Pro",
        image: "/placeholder.svg",
        dimensions: "149.6 x 71.5 x 8.3 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone16plus",
        name: "iPhone 16 Plus",
        image: "/placeholder.svg",
        dimensions: "160.9 x 77.8 x 7.8 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone16",
        name: "iPhone 16",
        image: "/placeholder.svg",
        dimensions: "147.6 x 71.6 x 7.8 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      // iPhone 15 Series
      {
        id: "iphone15promax",
        name: "iPhone 15 Pro Max",
        image: "/placeholder.svg",
        dimensions: "159.9 x 76.7 x 8.3 mm",
        releaseYear: 2023,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone15pro",
        name: "iPhone 15 Pro",
        image: "/placeholder.svg",
        dimensions: "146.7 x 71.5 x 8.3 mm",
        releaseYear: 2023,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone15plus",
        name: "iPhone 15 Plus",
        image: "/placeholder.svg",
        dimensions: "160.9 x 77.8 x 7.8 mm",
        releaseYear: 2023,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone15",
        name: "iPhone 15",
        image: "/placeholder.svg",
        dimensions: "147.6 x 71.6 x 7.8 mm",
        releaseYear: 2023,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      // iPhone 14 Series
      {
        id: "iphone14promax",
        name: "iPhone 14 Pro Max",
        image: "/placeholder.svg",
        dimensions: "160.7 x 77.6 x 7.9 mm",
        releaseYear: 2022,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone14pro",
        name: "iPhone 14 Pro",
        image: "/placeholder.svg",
        dimensions: "147.5 x 71.5 x 7.9 mm",
        releaseYear: 2022,
        popular: true,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone14plus",
        name: "iPhone 14 Plus",
        image: "/placeholder.svg",
        dimensions: "160.8 x 78.1 x 7.8 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone14",
        name: "iPhone 14",
        image: "/placeholder.svg",
        dimensions: "146.7 x 71.5 x 7.8 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      // iPhone 13 Series
      {
        id: "iphone13promax",
        name: "iPhone 13 Pro Max",
        image: "/placeholder.svg",
        dimensions: "160.8 x 78.1 x 7.7 mm",
        releaseYear: 2021,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone13pro",
        name: "iPhone 13 Pro",
        image: "/placeholder.svg",
        dimensions: "146.7 x 71.5 x 7.7 mm",
        releaseYear: 2021,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone13",
        name: "iPhone 13",
        image: "/placeholder.svg",
        dimensions: "146.7 x 71.5 x 7.7 mm",
        releaseYear: 2021,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone13mini",
        name: "iPhone 13 Mini",
        image: "/placeholder.svg",
        dimensions: "131.5 x 64.2 x 7.7 mm",
        releaseYear: 2021,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      // iPhone 12 Series
      {
        id: "iphone12promax",
        name: "iPhone 12 Pro Max",
        image: "/placeholder.svg",
        dimensions: "160.8 x 78.1 x 7.4 mm",
        releaseYear: 2020,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone12pro",
        name: "iPhone 12 Pro",
        image: "/placeholder.svg",
        dimensions: "146.7 x 71.5 x 7.4 mm",
        releaseYear: 2020,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone12",
        name: "iPhone 12",
        image: "/placeholder.svg",
        dimensions: "146.7 x 71.5 x 7.4 mm",
        releaseYear: 2020,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      {
        id: "iphone12mini",
        name: "iPhone 12 Mini",
        image: "/placeholder.svg",
        dimensions: "131.5 x 64.2 x 7.4 mm",
        releaseYear: 2020,
        popular: false,
        availableQualities: ["Vip-1", "Premium", "Premium MagSafe"],
      },
      // iPhone 11 Series
      {
        id: "iphone11promax",
        name: "iPhone 11 Pro Max",
        image: "/placeholder.svg",
        dimensions: "158.0 x 77.8 x 8.1 mm",
        releaseYear: 2019,
        popular: false,
        availableQualities: ["Vip-1", "Premium"],
      },
      {
        id: "iphone11pro",
        name: "iPhone 11 Pro",
        image: "/placeholder.svg",
        dimensions: "144.0 x 71.4 x 8.1 mm",
        releaseYear: 2019,
        popular: false,
        availableQualities: ["Vip-1", "Premium"],
      },
      {
        id: "iphone11",
        name: "iPhone 11",
        image: "/placeholder.svg",
        dimensions: "150.9 x 75.7 x 8.3 mm",
        releaseYear: 2019,
        popular: false,
        availableQualities: ["Vip-1", "Premium"],
      },
      // Autres modèles iPhone
      {
        id: "iphonese2022",
        name: "iPhone SE (2022)",
        image: "/placeholder.svg",
        dimensions: "138.4 x 67.3 x 7.3 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "iphonexr",
        name: "iPhone XR",
        image: "/placeholder.svg",
        dimensions: "150.9 x 75.7 x 8.3 mm",
        releaseYear: 2018,
        popular: false,
        availableQualities: ["Vip-1"],
      },
    ],
  },
  samsung: {
    name: "Samsung",
    logo: "/images/brands/samsung.svg",
    defaultQualities: ["Vip-1"],
    models: [
      // Galaxy S25 Series
      {
        id: "samsungs25ultra",
        name: "Galaxy S25 Ultra",
        image: "/placeholder.svg",
        dimensions: "163.5 x 78.2 x 8.9 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsungs25plus",
        name: "Galaxy S25+",
        image: "/placeholder.svg",
        dimensions: "158.5 x 75.0 x 7.7 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsungs25",
        name: "Galaxy S25",
        image: "/placeholder.svg",
        dimensions: "147.0 x 71.0 x 7.6 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      // Galaxy S24 Series
      {
        id: "samsungs24ultra",
        name: "Galaxy S24 Ultra",
        image: "/placeholder.svg",
        dimensions: "162.3 x 79.0 x 8.6 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsungs24plus",
        name: "Galaxy S24+",
        image: "/placeholder.svg",
        dimensions: "158.5 x 75.9 x 7.7 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsungs24",
        name: "Galaxy S24",
        image: "/placeholder.svg",
        dimensions: "147.0 x 70.6 x 7.6 mm",
        releaseYear: 2024,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      // Galaxy S23 Series
      {
        id: "samsungs23ultra",
        name: "Galaxy S23 Ultra",
        image: "/placeholder.svg",
        dimensions: "163.4 x 78.1 x 8.9 mm",
        releaseYear: 2023,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsungs23plus",
        name: "Galaxy S23+",
        image: "/placeholder.svg",
        dimensions: "157.8 x 76.2 x 7.6 mm",
        releaseYear: 2023,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsungs23",
        name: "Galaxy S23",
        image: "/placeholder.svg",
        dimensions: "146.3 x 70.9 x 7.6 mm",
        releaseYear: 2023,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      // Galaxy S22 Series
      {
        id: "samsungs22ultra",
        name: "Galaxy S22 Ultra",
        image: "/placeholder.svg",
        dimensions: "163.3 x 77.9 x 8.9 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsungs22plus",
        name: "Galaxy S22+",
        image: "/placeholder.svg",
        dimensions: "157.4 x 75.8 x 7.6 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsungs22",
        name: "Galaxy S22",
        image: "/placeholder.svg",
        dimensions: "146.0 x 70.6 x 7.6 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      // Galaxy A Series
      {
        id: "samsunga54",
        name: "Galaxy A54",
        image: "/placeholder.svg",
        dimensions: "158.2 x 76.7 x 8.2 mm",
        releaseYear: 2023,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsunga53",
        name: "Galaxy A53",
        image: "/placeholder.svg",
        dimensions: "159.6 x 74.8 x 8.1 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "samsunga34",
        name: "Galaxy A34",
        image: "/placeholder.svg",
        dimensions: "161.3 x 78.1 x 8.2 mm",
        releaseYear: 2023,
        popular: false,
        availableQualities: ["Vip-1"],
      },
    ],
  },
  google: {
    name: "Google",
    logo: "/images/brands/google.svg",
    defaultQualities: ["Vip-1"],
    models: [
      {
        id: "pixel8pro",
        name: "Pixel 8 Pro",
        image: "/placeholder.svg",
        dimensions: "162.6 x 76.5 x 8.8 mm",
        releaseYear: 2023,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      {
        id: "pixel8",
        name: "Pixel 8",
        image: "/placeholder.svg",
        dimensions: "150.5 x 70.8 x 8.9 mm",
        releaseYear: 2023,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "pixel7pro",
        name: "Pixel 7 Pro",
        image: "/placeholder.svg",
        dimensions: "162.9 x 76.6 x 8.9 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "pixel7",
        name: "Pixel 7",
        image: "/placeholder.svg",
        dimensions: "155.6 x 73.2 x 8.7 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "pixel6pro",
        name: "Pixel 6 Pro",
        image: "/placeholder.svg",
        dimensions: "163.9 x 75.9 x 8.9 mm",
        releaseYear: 2021,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "pixel6",
        name: "Pixel 6",
        image: "/placeholder.svg",
        dimensions: "158.6 x 74.8 x 8.9 mm",
        releaseYear: 2021,
        popular: false,
        availableQualities: ["Vip-1"],
      },
    ],
  },
  xiaomi: {
    name: "Xiaomi",
    logo: "/images/brands/xiaomi.svg",
    defaultQualities: ["Vip-1"],
    models: [
      {
        id: "xiaomi13pro",
        name: "Xiaomi 13 Pro",
        image: "/placeholder.svg",
        dimensions: "162.9 x 74.6 x 8.4 mm",
        releaseYear: 2023,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      {
        id: "xiaomi13",
        name: "Xiaomi 13",
        image: "/placeholder.svg",
        dimensions: "152.8 x 71.5 x 8.0 mm",
        releaseYear: 2023,
        popular: false,
        availableQualities: ["Vip-1"],
      },
      {
        id: "redminote12pro",
        name: "Redmi Note 12 Pro",
        image: "/placeholder.svg",
        dimensions: "162.9 x 76.0 x 8.0 mm",
        releaseYear: 2022,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      {
        id: "redminote12",
        name: "Redmi Note 12",
        image: "/placeholder.svg",
        dimensions: "165.7 x 76.0 x 8.0 mm",
        releaseYear: 2022,
        popular: false,
        availableQualities: ["Vip-1"],
      },
    ],
  },
  huawei: {
    name: "Huawei",
    logo: "/images/brands/huawei.svg",
    defaultQualities: ["Vip-1"],
    models: [
      {
        id: "huaweip60pro",
        name: "P60 Pro",
        image: "/placeholder.svg",
        dimensions: "161.0 x 74.5 x 8.3 mm",
        releaseYear: 2023,
        popular: true,
        availableQualities: ["Vip-1"],
      },
      {
        id: "huaweip60",
        name: "P60",
        image: "/placeholder.svg",
        dimensions: "161.0 x 74.5 x 8.3 mm",
        releaseYear: 2023,
        popular: false,
        availableQualities: ["Vip-1"],
      },
    ],
  },
};

// Modèles à venir
export const upcomingModels: UpcomingModel[] = [
  {
    id: "iphone16pro",
    name: "iPhone 16 Pro",
    brand: "Apple",
    image: "/placeholder.svg",
    expectedRelease: "Septembre 2024",
  },
  {
    id: "samsungs24ultra",
    name: "Galaxy S24 Ultra",
    brand: "Samsung",
    image: "/placeholder.svg",
    expectedRelease: "Janvier 2024",
  },
  {
    id: "pixel9pro",
    name: "Pixel 9 Pro",
    brand: "Google",
    image: "/placeholder.svg",
    expectedRelease: "Octobre 2024",
  },
];

