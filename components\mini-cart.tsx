"use client"

import { Shopping<PERSON><PERSON>, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import Link from "next/link"
import Image from "next/image"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function MiniCart() {
  const { items, subtotal, removeFromCart } = useSimpleCart()
  
  // Formater le prix
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF'
    }).format(price)
  }
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <ShoppingCart className="h-5 w-5" />
          {items.length > 0 && (
            <Badge className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-purple-600 p-0 text-xs">
              {items.length}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <div className="p-4">
          <h3 className="font-medium">Mon panier</h3>
          <p className="text-sm text-gray-500">{items.length} article(s)</p>
        </div>
        
        <DropdownMenuSeparator />
        
        {items.length === 0 ? (
          <div className="p-4 text-center">
            <p className="mb-2 text-gray-500">Votre panier est vide</p>
            <Link href="/products">
              <Button variant="outline" size="sm" className="mt-2">
                Voir les produits
              </Button>
            </Link>
          </div>
        ) : (
          <>
            <div className="max-h-60 overflow-y-auto">
              {items.map((item) => (
                <div key={item.id} className="flex items-center p-3 hover:bg-gray-50">
                  <div className="relative h-12 w-12 flex-shrink-0 overflow-hidden rounded-md">
                    <Image
                      src={item.image_url || "/images/products/placeholder.png"}
                      alt={item.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="ml-3 flex-1">
                    <p className="text-sm font-medium line-clamp-1">{item.name}</p>
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-gray-500">
                        {item.quantity} x {formatPrice(item.price)}
                      </p>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => removeFromCart(item.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <DropdownMenuSeparator />
            
            <div className="p-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Sous-total:</span>
                <span className="font-bold">{formatPrice(subtotal)}</span>
              </div>
              
              <div className="mt-4 flex gap-2">
                <Link href="/cart" className="flex-1">
                  <Button variant="outline" className="w-full">
                    Voir le panier
                  </Button>
                </Link>
                <Link href="/checkout" className="flex-1">
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">
                    Commander
                  </Button>
                </Link>
              </div>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
