"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import "./color-swatches.css"
import {
  ImageIcon,
  Type,
  Palette,
  Smartphone,
  Upload,
  Save,
  ShoppingCart,
  RotateCcw,
  Layers,
  ZoomIn,
  MoveHorizontal,
  MoveVertical,
  RotateCw,
  Trash2
} from "lucide-react"
import PhonePreview from "@/components/phone-preview"
import ColorPicker from "@/components/color-picker"
import DesignGallery from "@/components/design-gallery"
import { getImagesForModel } from "@/services/phone-model-images"

const phoneModels = [
  { id: "iphonex", name: "iPhone X", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 70.9, height: 143.6 } },
  { id: "iphonexs", name: "iPhone XS", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 70.9, height: 143.6 } },
  { id: "iphonexsmax", name: "iPhone XS Max", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 77.4, height: 157.5 } },
  { id: "iphonexr", name: "iPhone XR", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 75.7, height: 150.9 } },
  { id: "iphone11", name: "iPhone 11", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 75.7, height: 150.9 } },
  { id: "iphone11pro", name: "iPhone 11 Pro", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.4, height: 144.0 } },
  { id: "iphone11promax", name: "iPhone 11 Pro Max", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 77.8, height: 158.0 } },
  { id: "iphone12mini", name: "iPhone 12 mini", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 64.2, height: 131.5 } },
  { id: "iphone12", name: "iPhone 12", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.5, height: 146.7 } },
  { id: "iphone12pro", name: "iPhone 12 Pro", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.5, height: 146.7 } },
  { id: "iphone12promax", name: "iPhone 12 Pro Max", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 78.1, height: 160.8 } },
  { id: "iphone13mini", name: "iPhone 13 mini", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 64.2, height: 131.5 } },
  { id: "iphone13", name: "iPhone 13", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.5, height: 146.7 } },
  { id: "iphone13pro", name: "iPhone 13 Pro", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.5, height: 146.7 } },
  { id: "iphone13promax", name: "iPhone 13 Pro Max", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 78.1, height: 160.8 } },
  { id: "iphonese2022", name: "iPhone SE (2022)", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 67.3, height: 138.4 } },
  { id: "iphone14", name: "iPhone 14", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.5, height: 146.7 } },
  { id: "iphone14plus", name: "iPhone 14 Plus", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 78.1, height: 160.8 } },
  { id: "iphone14pro", name: "iPhone 14 Pro", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.5, height: 147.5 } },
  { id: "iphone14promax", name: "iPhone 14 Pro Max", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 77.6, height: 160.7 } },
  { id: "iphone15", name: "iPhone 15", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.6, height: 147.6 } },
  { id: "iphone15plus", name: "iPhone 15 Plus", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 77.8, height: 160.9 } },
  { id: "iphone15pro", name: "iPhone 15 Pro", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 70.6, height: 146.6 } },
  { id: "iphone15promax", name: "iPhone 15 Pro Max", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 76.7, height: 159.9 } },
  { id: "iphone16", name: "iPhone 16", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 72.0, height: 148.0 } },
  { id: "iphone16plus", name: "iPhone 16 Plus", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 78.5, height: 161.5 } },
  { id: "iphone16pro", name: "iPhone 16 Pro", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.0, height: 147.0 } },
  { id: "iphone16promax", name: "iPhone 16 Pro Max", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 77.0, height: 161.0 } },
  { id: "iphone17", name: "iPhone 17", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 72.5, height: 149.0 } },
  { id: "iphone17plus", name: "iPhone 17 Plus", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 79.0, height: 162.0 } },
  { id: "iphone17pro", name: "iPhone 17 Pro", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 71.5, height: 148.0 } },
  { id: "iphone17promax", name: "iPhone 17 Pro Max", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 77.5, height: 162.0 } },
  { id: "samsungs23", name: "Samsung Galaxy S23", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 70.9, height: 146.3 } },
  { id: "pixel7", name: "Google Pixel 7", image: "/images/placeholder.svg?v=1747269269112", dimensions: { width: 73.2, height: 155.6 } },
]

export default function CustomizePage() {
  // Définir l'iPhone 16 Pro comme modèle par défaut pour mettre en avant les nouveaux modèles
  const [selectedModel, setSelectedModel] = useState("iphone16pro")
  const [activeTab, setActiveTab] = useState("upload")
  const [customText, setCustomText] = useState("")
  const [textColor, setTextColor] = useState("#000000")
  const [textSize, setTextSize] = useState(24)
  const [backgroundColor, setBackgroundColor] = useState("#ffffff")
  const [uploadedImage, setUploadedImage] = useState<string | null>(null)

  // Nouveaux états pour la position et la rotation du texte
  const [textPosX, setTextPosX] = useState(0)
  const [textPosY, setTextPosY] = useState(0)
  const [textRotation, setTextRotation] = useState(0)
  const [imageZoom, setImageZoom] = useState(1.0) // Valeur par défaut réduite pour mieux voir l'image entière
  const [imageRotation, setImageRotation] = useState(0)
  const [imageOffsetX, setImageOffsetX] = useState(0)
  const [imageOffsetY, setImageOffsetY] = useState(0)
  const [isAddingToCart, setIsAddingToCart] = useState(false) // État pour suivre l'ajout au panier

  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        setUploadedImage(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }



  // Charger les images disponibles pour le modèle sélectionné
  useEffect(() => {
    const loadModelImages = async () => {
      try {
        // Cette fonction est maintenant asynchrone
        const images = await getImagesForModel(selectedModel)
        // Vous pouvez utiliser ces images pour afficher les options disponibles
        console.log('Images disponibles pour le modèle:', images)
      } catch (error) {
        console.error('Erreur lors du chargement des images du modèle:', error)
      }
    }

    loadModelImages()
  }, [selectedModel])

  const { addToCart } = useSimpleCart();

  const handleAddToCart = async () => {
    // Si déjà en cours d'ajout, ne rien faire
    if (isAddingToCart) return;

    try {
      // Mettre à jour l'état pour indiquer que l'ajout au panier est en cours
      setIsAddingToCart(true);
      console.log("Début de l'ajout au panier...");

      // Vérifier si l'image uploadée est trop volumineuse
      let optimizedUploadedImage = uploadedImage;
      if (uploadedImage && uploadedImage.length > 500000) { // Si l'image fait plus de 500 Ko
        console.log("Image trop volumineuse, optimisation en cours...");
        // Simplifier en ne stockant pas l'image complète
        optimizedUploadedImage = "Image personnalisée (stockée séparément)";
      }

      // Créer un objet avec les données de personnalisation
      const customizationData = {
        model: selectedModel,
        backgroundColor,
        customText,
        textColor,
        textSize,
        textPosX,
        textPosY,
        textRotation,
        // Ne pas inclure l'image complète dans les données de personnalisation
        hasUploadedImage: !!uploadedImage,
        imageZoom,
        imageRotation,
        imageOffsetX,
        imageOffsetY
      };

      console.log("Données de personnalisation préparées:", customizationData);

      // Déterminer le prix en fonction du modèle
      let price = 4500; // Prix de base en FCFA

      // Ajuster le prix pour les modèles premium
      const modelName = phoneModels.find(model => model.id === selectedModel)?.name || '';
      console.log("Modèle sélectionné:", modelName);

      // Prix plus élevé pour les modèles premium (iPhone Pro, Pro Max, Samsung Ultra)
      if (modelName.includes('Pro Max') || modelName.includes('Ultra')) {
        price = 5500; // Prix premium en FCFA
      } else if (modelName.includes('Pro') || modelName.includes('Plus')) {
        price = 5000; // Prix intermédiaire en FCFA
      }

      console.log("Prix calculé:", price, "FCFA");

      // Simuler un produit (à remplacer par un produit réel de la base de données)
      const product = {
        id: "custom-case-" + selectedModel,
        name: "Coque personnalisée " + modelName,
        price: price,
        description: "Coque personnalisée avec votre design unique",
        image_url: "/images/products/personnalisation/personnalisation.png",
        category: "accessoires",
        subcategory: "coques",
        is_new: true,
        is_bestseller: false,
        type: "custom",
        collections: [],
        colors: [],
        created_at: new Date().toISOString() // Ajout de la propriété manquante requise par le type Product
      };

      console.log("Produit préparé:", product);

      // Ajouter au panier (fonctionne pour utilisateurs connectés et non connectés)
      console.log("Appel de la fonction addToCart...");
      // Correction : n'utiliser que les deux premiers arguments (produit, quantité)
      await addToCart(product, 1)
      console.log("Produit ajouté au panier avec succès!");

      // Afficher une alerte pour confirmer l'ajout au panier
      alert("Votre coque personnalisée a été ajoutée au panier!");
    } catch (error) {
      console.error("Erreur lors de l'ajout au panier:", error);
      alert("Une erreur s'est produite lors de l'ajout au panier. Veuillez réessayer.");
    } finally {
      // Réinitialiser l'état d'ajout au panier, que ce soit un succès ou une erreur
      setIsAddingToCart(false);
    }
  }

  const handleSaveDesign = () => {
    alert("Design sauvegardé !")
    // In a real app, this would save the design to the user's account
  }

  const handleResetImage = () => {
    setUploadedImage(null)
    setImageZoom(1.0) // Réinitialiser au zoom par défaut de 1.0
    setImageRotation(0)
    setImageOffsetX(0)
    setImageOffsetY(0)
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6 text-primary">Personnalisez votre coque</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 relative">
        {/* Left Column - Phone Preview */}
        <div className="lg:col-span-1 order-2 lg:order-1">
          <Card className="sticky top-24 shadow-lg border-2 border-muted rounded-xl overflow-hidden">
            <CardContent className="p-6 flex flex-col items-center">
              <PhonePreview
                model={phoneModels.find((model) => model.id === selectedModel)!}
                backgroundColor={backgroundColor}
                uploadedImage={uploadedImage}
                customText={customText}
                textColor={textColor}
                textSize={textSize}
                imageZoom={imageZoom}
                imageRotation={imageRotation}
                imageOffsetX={imageOffsetX}
                imageOffsetY={imageOffsetY}
                textPosX={textPosX}
                textPosY={textPosY}
                textRotation={textRotation}
              />

              <div className="flex justify-between mt-6 w-full gap-4">
                <Button variant="outline" onClick={handleSaveDesign} className="flex-1 border-primary/20 hover:bg-primary/5">
                  <Save className="mr-2 h-4 w-4" /> Sauvegarder
                </Button>
                <Button
                  onClick={handleAddToCart}
                  className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground"
                  disabled={isAddingToCart}
                >
                  {isAddingToCart ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Ajout en cours...
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="mr-2 h-4 w-4" /> Ajouter au panier
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Customization Options */}
        <div className="lg:col-span-2 order-1 lg:order-2">
          <Card>
            <CardContent className="p-6">
              {/* Phone Model Selection */}
              <div className="mb-6">
                <Label htmlFor="phone-model" className="text-lg font-medium mb-2 block">
                  <Smartphone className="inline mr-2 h-5 w-5" /> Modèle de téléphone
                </Label>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger id="phone-model" className="w-full">
                    <SelectValue placeholder="Sélectionnez un modèle" />
                  </SelectTrigger>
                  <SelectContent>
                    {phoneModels.map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        {model.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Customization Tabs */}
              <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
                <TabsList className="grid grid-cols-3 mb-6 bg-muted/50 p-1 rounded-xl">
                  <TabsTrigger value="upload" className="flex items-center data-[state=active]:bg-background data-[state=active]:shadow-sm">
                    <Upload className="mr-2 h-4 w-4" /> Image
                  </TabsTrigger>
                  <TabsTrigger value="text" className="flex items-center data-[state=active]:bg-background data-[state=active]:shadow-sm">
                    <Type className="mr-2 h-4 w-4" /> Texte
                  </TabsTrigger>
                  <TabsTrigger value="background" className="flex items-center data-[state=active]:bg-background data-[state=active]:shadow-sm">
                    <Palette className="mr-2 h-4 w-4" /> Fond
                  </TabsTrigger>
                </TabsList>

                {/* Upload Image Tab */}
                <TabsContent value="upload" className="space-y-4">
                  <div className="grid gap-4">
                    <div className="flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-10 bg-muted/10 hover:bg-muted/20 transition-colors">
                      <Label htmlFor="upload-image" className="sr-only">Télécharger une image</Label>
                      <input
                        id="upload-image"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        ref={fileInputRef}
                        onChange={handleFileUpload}
                        aria-label="Télécharger une image pour personnaliser votre coque"
                      />
                      <Button onClick={triggerFileInput} variant="outline" className="bg-background shadow-sm hover:bg-muted/50">
                        <Upload className="mr-2 h-4 w-4" /> Choisir une image
                      </Button>
                      <p className="text-sm text-muted-foreground mt-2">
                        JPG, PNG, GIF jusqu'à 10MB
                      </p>
                    </div>

                    {uploadedImage && (
                      <div className="mt-6 space-y-6">
                        <h3 className="text-lg font-medium">Ajuster l'image</h3>

                        {/* Zoom control */}
                        <div className="bg-muted/10 p-4 rounded-lg">
                          <Label htmlFor="image-zoom" className="text-sm font-medium mb-2 flex items-center">
                            <ZoomIn className="h-4 w-4 mr-2" /> Zoom
                          </Label>
                          <div className="flex items-center space-x-2">
                            <Slider
                              id="image-zoom"
                              value={[imageZoom]}
                              min={0.2} // Réduit à 0.2 pour permettre de voir l'image très petite
                              max={3} // Limité à 3 pour éviter les débordements excessifs
                              step={0.01}
                              onValueChange={(value) => setImageZoom(value[0])}
                              className="flex-1"
                            />
                            <span className="w-12 text-center font-medium">{Math.round(imageZoom * 100)}%</span>
                          </div>
                        </div>

                        {/* Rotation control */}
                        <div className="bg-muted/10 p-4 rounded-lg">
                          <Label htmlFor="image-rotation" className="text-sm font-medium mb-2 flex items-center">
                            <RotateCw className="h-4 w-4 mr-2" /> Rotation
                          </Label>
                          <div className="flex items-center space-x-2">
                            <Slider
                              id="image-rotation"
                              value={[imageRotation]}
                              min={-180}
                              max={180}
                              step={1}
                              onValueChange={(value) => setImageRotation(value[0])}
                              className="flex-1"
                            />
                            <span className="w-12 text-center font-medium">{imageRotation}°</span>
                          </div>
                          <div className="flex justify-center mt-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setImageRotation(0)}
                              className="text-xs"
                            >
                              <RotateCcw className="h-3 w-3 mr-1" /> Réinitialiser
                            </Button>
                          </div>
                        </div>

                        {/* Horizontal position control */}
                        <div className="bg-muted/10 p-4 rounded-lg">
                          <Label htmlFor="image-offset-x" className="text-sm font-medium mb-2 flex items-center">
                            <MoveHorizontal className="h-4 w-4 mr-2" /> Position horizontale
                          </Label>
                          <div className="flex items-center space-x-2">
                            <Slider
                              id="image-offset-x"
                              value={[imageOffsetX]}
                              min={-50}
                              max={50}
                              step={1}
                              onValueChange={(value) => setImageOffsetX(value[0])}
                              className="flex-1"
                            />
                            <span className="w-12 text-center font-medium">{imageOffsetX}</span>
                          </div>
                        </div>

                        {/* Vertical position control */}
                        <div className="bg-muted/10 p-4 rounded-lg">
                          <Label htmlFor="image-offset-y" className="text-sm font-medium mb-2 flex items-center">
                            <MoveVertical className="h-4 w-4 mr-2" /> Position verticale
                          </Label>
                          <div className="flex items-center space-x-2">
                            <Slider
                              id="image-offset-y"
                              value={[imageOffsetY]}
                              min={-50}
                              max={50}
                              step={1}
                              onValueChange={(value) => setImageOffsetY(value[0])}
                              className="flex-1"
                            />
                            <span className="w-12 text-center font-medium">{imageOffsetY}</span>
                          </div>
                        </div>

                        {/* Reset all button */}
                        <div className="flex justify-center mt-4 gap-2">
                          <Button
                            variant="outline"
                            onClick={() => {
                              setImageZoom(1.0); // Réinitialiser au zoom par défaut de 1.0
                              setImageRotation(0);
                              setImageOffsetX(0);
                              setImageOffsetY(0);
                            }}
                          >
                            <RotateCcw className="mr-2 h-4 w-4" /> Réinitialiser les ajustements
                          </Button>
                          <Button
                            variant="destructive"
                            onClick={handleResetImage}
                          >
                            <Trash2 className="mr-2 h-4 w-4" /> Supprimer l'image
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>

                {/* Text Tab */}
                <TabsContent value="text" className="space-y-4">
                  <div className="grid gap-6 p-2">
                    <div className="bg-muted/10 p-4 rounded-lg">
                      <Label htmlFor="custom-text" className="text-sm font-medium mb-2 block">Votre texte</Label>
                      <Input
                        id="custom-text"
                        value={customText}
                        onChange={(e) => setCustomText(e.target.value)}
                        placeholder="Entrez votre texte personnalisé"
                        className="bg-background shadow-sm"
                      />
                    </div>
                    <div className="bg-muted/10 p-4 rounded-lg">
                      <Label htmlFor="text-color" className="text-sm font-medium mb-2 block">Couleur du texte</Label>
                      <ColorPicker color={textColor} onChange={setTextColor} id="text-color" />
                    </div>
                    <div className="bg-muted/10 p-4 rounded-lg">
                      <Label htmlFor="text-size" className="text-sm font-medium mb-2 block">Taille du texte</Label>
                      <div className="flex items-center space-x-2">
                        <Slider
                          id="text-size"
                          value={[textSize]}
                          min={12}
                          max={72}
                          step={1}
                          onValueChange={(value) => setTextSize(value[0])}
                          className="flex-1"
                        />
                        <span className="w-12 text-center font-medium">{textSize}px</span>
                      </div>
                    </div>

                    {/* Nouvelles options pour le texte */}
                    <div className="bg-muted/10 p-4 rounded-lg">
                      <Label htmlFor="text-rotation" className="text-sm font-medium mb-2 flex items-center">
                        <RotateCw className="h-4 w-4 mr-2" /> Rotation du texte
                      </Label>
                      <div className="flex items-center space-x-2">
                        <Slider
                          id="text-rotation"
                          value={[textRotation]}
                          min={-180}
                          max={180}
                          step={1}
                          onValueChange={(value) => setTextRotation(value[0])}
                          className="flex-1"
                        />
                        <span className="w-12 text-center font-medium">{textRotation}°</span>
                      </div>
                      <div className="flex justify-center mt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setTextRotation(0)}
                          className="text-xs"
                        >
                          <RotateCcw className="h-3 w-3 mr-1" /> Réinitialiser
                        </Button>
                      </div>
                    </div>

                    <div className="bg-muted/10 p-4 rounded-lg">
                      <Label htmlFor="text-pos-x" className="text-sm font-medium mb-2 flex items-center">
                        <MoveHorizontal className="h-4 w-4 mr-2" /> Position horizontale
                      </Label>
                      <div className="flex items-center space-x-2">
                        <Slider
                          id="text-pos-x"
                          value={[textPosX]}
                          min={-50}
                          max={50}
                          step={1}
                          onValueChange={(value) => setTextPosX(value[0])}
                          className="flex-1"
                        />
                        <span className="w-12 text-center font-medium">{textPosX}</span>
                      </div>
                    </div>

                    <div className="bg-muted/10 p-4 rounded-lg">
                      <Label htmlFor="text-pos-y" className="text-sm font-medium mb-2 flex items-center">
                        <MoveVertical className="h-4 w-4 mr-2" /> Position verticale
                      </Label>
                      <div className="flex items-center space-x-2">
                        <Slider
                          id="text-pos-y"
                          value={[textPosY]}
                          min={-50}
                          max={50}
                          step={1}
                          onValueChange={(value) => setTextPosY(value[0])}
                          className="flex-1"
                        />
                        <span className="w-12 text-center font-medium">{textPosY}</span>
                      </div>
                    </div>

                    <div className="flex justify-center mt-4">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setTextPosX(0);
                          setTextPosY(0);
                          setTextRotation(0);
                        }}
                      >
                        <RotateCcw className="mr-2 h-4 w-4" /> Réinitialiser le texte
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                {/* Background Tab */}
                <TabsContent value="background" className="space-y-4">
                  <div className="bg-muted/10 p-4 rounded-lg">
                    <Label htmlFor="background-color" className="text-sm font-medium mb-2 block">Couleur de fond</Label>
                    <ColorPicker
                      color={backgroundColor}
                      onChange={setBackgroundColor}
                      id="background-color"
                    />
                    <p className="text-xs text-muted-foreground mt-2">Choisissez une couleur pour l'arrière-plan de votre coque</p>
                  </div>

                  <div className="grid grid-cols-4 gap-2 mt-4">
                    {[
                      { color: "#ffffff", className: "color-swatch color-swatch-white" },
                      { color: "#000000", className: "color-swatch color-swatch-black" },
                      { color: "#ff5555", className: "color-swatch color-swatch-red" },
                      { color: "#55ff55", className: "color-swatch color-swatch-green" },
                      { color: "#5555ff", className: "color-swatch color-swatch-blue" },
                      { color: "#ffff55", className: "color-swatch color-swatch-yellow" },
                      { color: "#ff55ff", className: "color-swatch color-swatch-magenta" },
                      { color: "#55ffff", className: "color-swatch color-swatch-cyan" }
                    ].map(
                      (item) => (
                        <div
                          key={item.color}
                          className={item.className}
                          onClick={() => setBackgroundColor(item.color)}
                        />
                      ),
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  )
}
