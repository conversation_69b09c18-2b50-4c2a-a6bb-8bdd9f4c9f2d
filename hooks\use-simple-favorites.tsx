"use client"

import { useFavoritesStore, FavoriteProduct } from "@/lib/store/favorites-store"
import { useToast } from "@/components/ui/use-toast"

export function useSimpleFavorites() {
  const favoritesStore = useFavoritesStore()
  const { toast } = useToast()
  
  // Ajouter un produit aux favoris
  const addToFavorites = (product: FavoriteProduct) => {
    try {
      favoritesStore.addFavorite(product)
      
      toast({
        title: "Ajouté aux favoris",
        description: `${product.name} a été ajouté à vos favoris.`,
      })
      
      return true
    } catch (error) {
      console.error("Erreur lors de l'ajout aux favoris:", error)
      
      toast({
        title: "Erreur",
        description: "Impossible d'ajouter ce produit aux favoris.",
        variant: "destructive"
      })
      
      return false
    }
  }
  
  // Supprimer un produit des favoris
  const removeFromFavorites = (productId: string) => {
    try {
      favoritesStore.removeFavorite(productId)
      
      toast({
        title: "Retiré des favoris",
        description: "Le produit a été retiré de vos favoris.",
      })
      
      return true
    } catch (error) {
      console.error("Erreur lors de la suppression des favoris:", error)
      
      toast({
        title: "Erreur",
        description: "Impossible de retirer ce produit des favoris.",
        variant: "destructive"
      })
      
      return false
    }
  }
  
  // Basculer un produit dans les favoris (ajouter ou supprimer)
  const toggleFavorite = (product: FavoriteProduct) => {
    try {
      const isFavorite = favoritesStore.favorites.some(item => item.id === product.id)
      
      if (isFavorite) {
        removeFromFavorites(product.id)
      } else {
        addToFavorites(product)
      }
      
      return true
    } catch (error) {
      console.error("Erreur lors du toggle des favoris:", error)
      
      toast({
        title: "Erreur",
        description: "Une erreur est survenue.",
        variant: "destructive"
      })
      
      return false
    }
  }
  
  // Vérifier si un produit est dans les favoris
  const isFavorite = (productId: string) => {
    try {
      return favoritesStore.favorites.some(item => item.id === productId)
    } catch (error) {
      console.error("Erreur lors de la vérification des favoris:", error)
      return false
    }
  }
  
  return {
    favorites: favoritesStore.favorites,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    isFavorite
  }
}
