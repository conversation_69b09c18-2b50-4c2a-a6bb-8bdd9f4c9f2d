"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { supabase<PERSON>pi } from "@/lib/supabase"
import { useAuth } from "@/hooks/use-auth"
import { useEffect } from "react"

// Type pour un produit favori
export interface FavoriteProduct {
  id: string
  name: string
  price: number
  image_url: string
  category?: string
  subcategory?: string
  is_new?: boolean
  is_bestseller?: boolean
  created_at?: string
}

// Interface pour le store de favoris
interface FavoritesStore {
  favorites: FavoriteProduct[]
  isLoading: boolean
  isInitialized: boolean
  addFavorite: (product: FavoriteProduct) => void
  removeFavorite: (productId: string) => void
  toggleFavorite: (product: FavoriteProduct) => void
  setFavorites: (favorites: FavoriteProduct[]) => void
  setIsLoading: (isLoading: boolean) => void
  setIsInitialized: (isInitialized: boolean) => void
}

// Créer le store Zustand avec persistance
const useFavoritesStore = create<FavoritesStore>()(
  persist(
    (set, get) => ({
      favorites: [],
      isLoading: false,
      isInitialized: false,

      addFavorite: (product) => {
        try {
          console.log("Ajout du produit aux favoris:", product);
          set((state) => ({
            favorites: [...state.favorites, product]
          }));
        } catch (error) {
          console.error("Erreur lors de l'ajout aux favoris:", error);
        }
      },

      removeFavorite: (productId) => {
        try {
          console.log("Suppression du produit des favoris:", productId);
          set((state) => ({
            favorites: state.favorites.filter((item) => item.id !== productId)
          }));
        } catch (error) {
          console.error("Erreur lors de la suppression des favoris:", error);
        }
      },

      toggleFavorite: (product) => {
        try {
          console.log("Toggle du produit dans les favoris:", product);
          const { favorites } = get();
          const exists = favorites.some((item) => item.id === product.id);

          if (exists) {
            get().removeFavorite(product.id);
          } else {
            get().addFavorite(product);
          }
        } catch (error) {
          console.error("Erreur lors du toggle des favoris:", error);
        }
      },

      setFavorites: (favorites) => {
        try {
          console.log("Mise à jour de la liste des favoris:", favorites.length, "produits");
          set({ favorites });
        } catch (error) {
          console.error("Erreur lors de la mise à jour des favoris:", error);
        }
      },

      setIsLoading: (isLoading) => {
        set({ isLoading });
      },

      setIsInitialized: (isInitialized) => {
        set({ isInitialized });
      }
    }),
    {
      name: "favorites-storage",
      // Utiliser localStorage au lieu de sessionStorage
      storage: {
        getItem: (name) => {
          try {
            // Vérifier si nous sommes côté client
            if (typeof window !== 'undefined') {
              const str = localStorage.getItem(name);
              if (!str) return null;
              return JSON.parse(str);
            }
            return null;
          } catch (error) {
            console.error("Erreur lors de la récupération des favoris:", error);
            return null;
          }
        },
        setItem: (name, value) => {
          try {
            // Vérifier si nous sommes côté client
            if (typeof window !== 'undefined') {
              localStorage.setItem(name, JSON.stringify(value));
            }
          } catch (error) {
            console.error("Erreur lors de l'enregistrement des favoris:", error);
          }
        },
        removeItem: (name) => {
          try {
            // Vérifier si nous sommes côté client
            if (typeof window !== 'undefined') {
              localStorage.removeItem(name);
            }
          } catch (error) {
            console.error("Erreur lors de la suppression des favoris:", error);
          }
        }
      }
    }
  )
)

// Hook pour utiliser les favoris
export const useFavorites = () => {
  try {
    const { user } = useAuth()
    const {
      favorites,
      isLoading,
      isInitialized,
      addFavorite,
      removeFavorite,
      toggleFavorite,
      setFavorites,
      setIsLoading,
      setIsInitialized
    } = useFavoritesStore()

    console.log("useFavorites hook initialisé avec", favorites.length, "favoris")

    // Fonction pour ajouter un favori avec gestion des erreurs
    const safeAddFavorite = (product: FavoriteProduct) => {
      try {
        console.log("safeAddFavorite - Ajout du produit aux favoris:", product);
        addFavorite(product);
      } catch (error) {
        console.error("safeAddFavorite - Erreur lors de l'ajout aux favoris:", error);
      }
    };

    // Fonction pour supprimer un favori avec gestion des erreurs
    const safeRemoveFavorite = (productId: string) => {
      try {
        console.log("safeRemoveFavorite - Suppression du produit des favoris:", productId);
        removeFavorite(productId);
      } catch (error) {
        console.error("safeRemoveFavorite - Erreur lors de la suppression des favoris:", error);
      }
    };

    // Fonction pour basculer un favori avec gestion des erreurs
    const safeToggleFavorite = (product: FavoriteProduct) => {
      try {
        console.log("safeToggleFavorite - Toggle du produit dans les favoris:", product);
        const exists = favorites.some((item) => item.id === product.id);

        if (exists) {
          safeRemoveFavorite(product.id);
        } else {
          safeAddFavorite(product);
        }
      } catch (error) {
        console.error("safeToggleFavorite - Erreur lors du toggle des favoris:", error);
      }
    };

  // Synchroniser les favoris avec Supabase pour les utilisateurs connectés
  useEffect(() => {
    const syncFavorites = async () => {
      // Si l'utilisateur n'est pas connecté ou si les favoris sont déjà initialisés, ne rien faire
      if (!user || isInitialized) return

      setIsLoading(true)

      try {
        // Récupérer les favoris de l'utilisateur depuis Supabase
        const { data, error } = await supabaseApi.favorites.getByUser(user.id)

        if (error) {
          throw error
        }

        if (data) {
          // Convertir les données Supabase en format FavoriteProduct
          const favoriteProducts: FavoriteProduct[] = data.map((favorite) => ({
            id: favorite.product_id,
            name: favorite.product?.name || "Produit",
            price: favorite.product?.price || 0,
            image_url: favorite.product?.image_url || "/images/products/placeholder.png",
            category: favorite.product?.category,
            subcategory: favorite.product?.subcategory,
            is_new: favorite.product?.is_new,
            is_bestseller: favorite.product?.is_bestseller,
            created_at: favorite.product?.created_at
          }))

          // Mettre à jour le store avec les favoris de Supabase
          setFavorites(favoriteProducts)
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des favoris:", error)
      } finally {
        setIsLoading(false)
        setIsInitialized(true)
      }
    }

    syncFavorites()
  }, [user, isInitialized, setFavorites, setIsLoading, setIsInitialized])

  // Synchroniser les modifications de favoris avec Supabase pour les utilisateurs connectés
  useEffect(() => {
    const syncFavoriteChanges = async () => {
      // Si l'utilisateur n'est pas connecté ou si les favoris ne sont pas initialisés, ne rien faire
      if (!user || !isInitialized) return

      try {
        // Récupérer les favoris actuels de l'utilisateur depuis Supabase
        const { data: currentFavorites, error: fetchError } = await supabaseApi.favorites.getByUser(user.id)

        if (fetchError) {
          throw fetchError
        }

        // Comparer les favoris locaux avec ceux de Supabase
        const currentProductIds = currentFavorites?.map(fav => fav.product_id) || []
        const localProductIds = favorites.map(fav => fav.id)

        // Produits à ajouter (présents localement mais pas dans Supabase)
        const productsToAdd = favorites.filter(fav => !currentProductIds.includes(fav.id))

        // Produits à supprimer (présents dans Supabase mais pas localement)
        const productIdsToRemove = currentProductIds.filter(id => !localProductIds.includes(id))

        // Ajouter les nouveaux favoris à Supabase
        for (const product of productsToAdd) {
          await supabaseApi.favorites.add(user.id, product.id)
        }

        // Supprimer les favoris retirés de Supabase
        for (const productId of productIdsToRemove) {
          await supabaseApi.favorites.remove(user.id, productId)
        }
      } catch (error) {
        console.error("Erreur lors de la synchronisation des favoris:", error)
      }
    }

    // Synchroniser les changements lorsque les favoris sont modifiés
    syncFavoriteChanges()
  }, [user, favorites, isInitialized])

    return {
      favorites,
      isLoading,
      addFavorite: safeAddFavorite,
      removeFavorite: safeRemoveFavorite,
      toggleFavorite: safeToggleFavorite
    }
  } catch (error) {
    console.error("Erreur critique dans le hook useFavorites:", error);
    // Retourner des valeurs par défaut en cas d'erreur
    return {
      favorites: [],
      isLoading: false,
      addFavorite: (product: FavoriteProduct) => {
        console.error("Fonction addFavorite non disponible - Mode de secours");
        // Essayer de sauvegarder localement en cas d'erreur
        try {
          if (typeof window !== 'undefined') {
            const localFavorites = JSON.parse(localStorage.getItem('local-favorites') || '[]');
            if (!localFavorites.some((fav: any) => fav.id === product.id)) {
              localFavorites.push(product);
              localStorage.setItem('local-favorites', JSON.stringify(localFavorites));
              console.log("Produit ajouté aux favoris locaux:", product);
            }
          }
        } catch (e) {
          console.error("Erreur lors de la sauvegarde locale des favoris:", e);
        }
      },
      removeFavorite: (productId: string) => {
        console.error("Fonction removeFavorite non disponible - Mode de secours");
        // Essayer de supprimer localement en cas d'erreur
        try {
          if (typeof window !== 'undefined') {
            const localFavorites = JSON.parse(localStorage.getItem('local-favorites') || '[]');
            const updatedFavorites = localFavorites.filter((fav: any) => fav.id !== productId);
            localStorage.setItem('local-favorites', JSON.stringify(updatedFavorites));
            console.log("Produit supprimé des favoris locaux:", productId);
          }
        } catch (e) {
          console.error("Erreur lors de la suppression locale des favoris:", e);
        }
      },
      toggleFavorite: (product: FavoriteProduct) => {
        console.error("Fonction toggleFavorite non disponible - Mode de secours");
        // Essayer de basculer localement en cas d'erreur
        try {
          if (typeof window !== 'undefined') {
            const localFavorites = JSON.parse(localStorage.getItem('local-favorites') || '[]');
            const exists = localFavorites.some((fav: any) => fav.id === product.id);

            if (exists) {
              const updatedFavorites = localFavorites.filter((fav: any) => fav.id !== product.id);
              localStorage.setItem('local-favorites', JSON.stringify(updatedFavorites));
              console.log("Produit supprimé des favoris locaux:", product.id);
            } else {
              localFavorites.push(product);
              localStorage.setItem('local-favorites', JSON.stringify(localFavorites));
              console.log("Produit ajouté aux favoris locaux:", product);
            }
          }
        } catch (e) {
          console.error("Erreur lors du toggle local des favoris:", e);
        }
      }
    }
  }
}
