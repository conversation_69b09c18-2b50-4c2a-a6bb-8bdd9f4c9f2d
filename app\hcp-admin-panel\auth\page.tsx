"use client"

// Force dynamic rendering to avoid prerendering issues with useSearchParams
export const dynamic = 'force-dynamic'

import { useState, useEffect, useCallback, Suspense } from "react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { LogIn, AlertCircle, Mail, Lock, ShieldAlert, Clock } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { isAdminEmail } from "@/lib/admin-config"
import { toast } from "sonner"
import { supabase } from "@/lib/supabase"

import {
  isUserLocked,
  incrementLoginAttempts,
  resetLockoutInfo,
  formatRemainingLockoutTime,
  MAX_LOGIN_ATTEMPTS
} from "@/lib/auth-lockout"

// Étapes d'authentification - Simplifiées (plus de vérification par email)
enum AuthStep {
  LOGIN = 'login',
}

function AuthPageContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, isLoading: authLoading, signIn } = useAuth()
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  })
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [authStep, setAuthStep] = useState<AuthStep>(AuthStep.LOGIN)

  const [lockoutTimeRemaining, setLockoutTimeRemaining] = useState<string>("")
  const [isLocked, setIsLocked] = useState<boolean>(false)

  // Récupérer la page de redirection après connexion
  const redirectPath = searchParams.get('from') || '/hcp-admin-panel'

  // Vérifier si l'utilisateur est bloqué
  useEffect(() => {
    const checkLockout = () => {
      const locked = isUserLocked();
      setIsLocked(locked);

      if (locked) {
        setLockoutTimeRemaining(formatRemainingLockoutTime());
      }
    };

    // Vérifier immédiatement
    checkLockout();

    // Vérifier toutes les secondes pour mettre à jour le compteur
    const interval = setInterval(checkLockout, 1000);

    return () => clearInterval(interval);
  }, []);

  // Vérifier si l'utilisateur est déjà connecté et est un admin
  useEffect(() => {
    console.log('Auth state check:', { 
      user: user ? { email: user.email, id: user.id } : null, 
      authLoading, 
      isAdmin: user ? isAdminEmail(user.email) : false,
      redirectPath 
    });
    
    if (!authLoading && user) {
      // Si l'utilisateur est connecté et est un admin, rediriger vers le tableau de bord
      if (isAdminEmail(user.email)) {
        console.log("Utilisateur admin déjà connecté, redirection vers:", redirectPath);
        router.push(redirectPath);
      } else if (user) {
        // Si l'utilisateur est connecté mais n'est pas un admin
        console.log("Utilisateur connecté mais non admin:", user.email);
        toast.error("Vous n'avez pas les droits d'accès à l'administration");
        router.push('/');
      }
    }
  }, [user, authLoading, router, redirectPath]);


  // Gérer la soumission du formulaire de connexion
  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    console.log('[Auth] Tentative de connexion avec:', { email: formData.email });
    
    try {
      // Vérifier si l'utilisateur est bloqué
      if (isUserLocked()) {
        const remainingTime = formatRemainingLockoutTime();
        console.log('[Auth] Utilisateur bloqué, temps restant:', remainingTime);
        setError(`Trop de tentatives échouées. Veuillez réessayer dans ${remainingTime}.`);
        setIsLoading(false);
        return;
      }

      // Vérifier que l'email est un email admin
      if (!isAdminEmail(formData.email)) {
        console.log('[Auth] Email non autorisé:', formData.email);
        setError('Cet email n\'est pas autorisé à accéder à l\'administration.');
        setIsLoading(false);
        incrementLoginAttempts();
        return;
      }

      console.log('[Auth] Appel de la fonction signIn');
      const { success, error } = await signIn(formData.email, formData.password);
      console.log('[Auth] Résultat de signIn:', { success, error });

      if (success) {
        // Réinitialiser le compteur de tentatives
        resetLockoutInfo();
        console.log('[Auth] Connexion réussie, vérification de la session');

        // Vérifier que la session est bien établie avant de rediriger
        const { data: { session } } = await supabase.auth.getSession();
        console.log('[Auth] Session après connexion:', session ? 'Présente' : 'Absente');
        
        if (session) {
          toast.success("Connexion réussie");
          console.log('[Auth] Redirection vers:', redirectPath);
          
          // Petit délai pour s'assurer que l'état d'authentification est propagé
          setTimeout(() => {
            router.push(redirectPath);
          }, 500);
        } else {
          console.error('[Auth] Session non établie après connexion');
          setError('Erreur lors de l\'établissement de la session');
        }
      } else {
        // Incrémenter le compteur de tentatives
        const lockoutInfo = incrementLoginAttempts();
        console.error("[Auth] Erreur de connexion:", error);
        
        setError(
          `Erreur de connexion: ${error || "Email ou mot de passe incorrect"}.`
        );

        // Afficher un message différent après plusieurs tentatives
        if (lockoutInfo.attempts >= 2 && lockoutInfo.attempts < MAX_LOGIN_ATTEMPTS) {
          setError(`Plusieurs tentatives échouées. Vérifiez vos identifiants. (${lockoutInfo.attempts}/${MAX_LOGIN_ATTEMPTS})`);
        }
      }
    } catch (err: any) {
      console.error("[Auth] Exception lors de la connexion:", err);
      setError(`Une erreur s'est produite lors de la connexion.`);
      incrementLoginAttempts();
    } finally {
      setIsLoading(false);
    }
  }

  // Fonctions de vérification par email supprimées - Authentification simplifiée

  // Afficher le formulaire de connexion (seule étape maintenant)
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <div className="flex items-center justify-center mb-2">
              <ShieldAlert className="h-8 w-8 text-purple-600 mr-2" />
              <CardTitle className="text-2xl font-bold text-center">Accès sécurisé</CardTitle>
            </div>
            <CardDescription className="text-center">
              Connectez-vous pour accéder à l'espace d'administration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLoginSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {isLocked && (
                <Alert variant="destructive" className="mb-4">
                  <Clock className="h-4 w-4" />
                  <AlertDescription>
                    Compte temporairement bloqué. Réessayez dans {lockoutTimeRemaining}.
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="pl-10"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    required
                    disabled={isLoading || isLocked}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Mot de passe</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    className="pl-10"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    required
                    disabled={isLoading || isLocked}
                  />
                </div>
              </div>



              <Button
                type="submit"
                className="w-full bg-purple-600 hover:bg-purple-700"
                disabled={isLoading || isLocked}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Connexion en cours...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <LogIn className="mr-2 h-4 w-4" /> Se connecter
                  </span>
                )}
              </Button>


            </form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Link href="/" className="text-sm text-purple-600 hover:text-purple-800">
              Retour au site
            </Link>
          </CardFooter>
        </Card>
      </div>
    )
  }

export default function SecureLoginPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 flex items-center justify-center p-4"><div>Loading...</div></div>}>
      <AuthPageContent />
    </Suspense>
  )
}

