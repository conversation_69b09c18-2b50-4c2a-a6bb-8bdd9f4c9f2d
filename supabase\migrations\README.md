# Migrations Supabase pour HCP-DESIGN CI

Ce dossier contient les migrations SQL pour mettre à jour le schéma de la base de données Supabase.

## Migrations disponibles

1. `20240701_update_orders_table.sql` - Ajoute des champs pour le paiement par QR code et les informations client
2. `20240702_update_cart_items_policies.sql` - Met à jour les politiques de sécurité pour permettre les utilisateurs anonymes

## Comment appliquer les migrations

### Option 1 : Via l'interface Supabase

1. Connectez-vous à votre projet Supabase
2. Allez dans "SQL Editor"
3. Copiez-collez le contenu du fichier de migration
4. Exécutez le SQL

### Option 2 : Via le script automatisé

1. Assurez-vous d'avoir un fichier `.env.local` avec les variables suivantes :
   ```
   NEXT_PUBLIC_SUPABASE_URL=votre-url-supabase
   NEXT_PUBLIC_SUPABASE_ANON_KEY=votre-clé-anon-supabase
   SUPABASE_SERVICE_ROLE_KEY=votre-clé-service-supabase
   ```

2. Installez les dépendances nécessaires :
   ```bash
   npm install @supabase/supabase-js dotenv
   ```

3. Exécutez le script d'application des migrations :
   ```bash
   node scripts/apply-migrations.js
   ```

## Création du bucket de stockage

Certaines migrations nécessitent la création d'un bucket de stockage, ce qui ne peut pas être fait via SQL. Suivez ces étapes manuellement :

1. Dans la console Supabase, allez dans "Storage"
2. Cliquez sur "Create bucket"
3. Nom: "orders"
4. Type: "Private"
5. Cliquez sur "Create bucket"

Puis créez des politiques pour permettre l'accès aux fichiers :

1. Sélectionnez le bucket "orders"
2. Allez dans "Policies"
3. Ajoutez une politique pour "INSERT" avec la condition "true"
4. Ajoutez une politique pour "SELECT" avec la condition "true" pour les administrateurs

## Vérification des migrations

Après avoir appliqué les migrations, vérifiez que les tables et les politiques ont été correctement créées :

1. Dans la console Supabase, allez dans "Table Editor"
2. Vérifiez que les tables `orders` et `cart_items` ont les champs et contraintes attendus
3. Allez dans "Authentication" > "Policies" et vérifiez que les politiques ont été créées
