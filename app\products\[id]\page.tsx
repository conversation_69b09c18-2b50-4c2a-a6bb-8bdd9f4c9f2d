"use client"

import { useEffect, useState, use } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Star, ShoppingCart, Heart, Share2 } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { useToast } from "@/components/ui/use-toast"

interface Product {
  id: number
  name: string
  price: number
  image: string
  rating: number
  reviews: number
  isNew: boolean
  isBestseller: boolean
  category: string
  type: string
  colors: string[]
  collections: string[]
  description?: string
}

// Données fictives pour les produits (à remplacer par des données réelles)
const products: Product[] = [
  {
    id: 1,
    name: "Tasse Personnalisable Classique",
    price: 7990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.7,
    reviews: 128,
    isNew: true,
    isBestseller: true,
    category: "tasses",
    type: "Céramique",
    colors: ["Blanc", "Noir"],
    collections: ["Classique"],
    description: "Tasse en céramique de haute qualité, parfaite pour personnaliser avec vos designs préférés. Idéale pour les boissons chaudes et froides."
  },
  {
    id: 2,
    name: "Tasse Magique Thermosensible",
    price: 9990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.5,
    reviews: 86,
    isNew: true,
    isBestseller: false,
    category: "tasses",
    type: "Thermosensible",
    colors: ["Noir", "Bleu"],
    collections: ["Premium"],
    description: "Tasse magique qui révèle des motifs au contact de boissons chaudes. Design unique et interactif."
  },
  {
    id: 3,
    name: "Tasse Isotherme Inox",
    price: 12990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.8,
    reviews: 210,
    isNew: false,
    isBestseller: true,
    category: "tasses",
    type: "Isotherme",
    colors: ["Argent", "Noir", "Blanc"],
    collections: ["Premium"],
    description: "Tasse isotherme en acier inoxydable de haute qualité. Garde vos boissons chaudes ou froides pendant des heures."
  },
  {
    id: 4,
    name: "T-shirt Personnalisable Coton",
    price: 8990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.6,
    reviews: 156,
    isNew: false,
    isBestseller: true,
    category: "tshirts",
    type: "Coton",
    colors: ["Blanc", "Noir", "Gris", "Bleu"],
    collections: ["Basique"],
    description: "T-shirt en coton 100% de haute qualité, parfait pour personnaliser avec vos designs préférés."
  },
  {
    id: 5,
    name: "T-shirt Premium Impression Totale",
    price: 12990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.9,
    reviews: 78,
    isNew: true,
    isBestseller: false,
    category: "tshirts",
    type: "Premium",
    colors: ["Blanc"],
    collections: ["Premium"],
    description: "T-shirt premium avec impression totale, offrant une qualité d'image exceptionnelle et un confort optimal."
  },
  {
    id: 6,
    name: "Polo Brodé Personnalisable",
    price: 14990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.7,
    reviews: 92,
    isNew: false,
    isBestseller: false,
    category: "tshirts",
    type: "Polo",
    colors: ["Blanc", "Noir", "Bleu marine"],
    collections: ["Business"],
    description: "Polo élégant avec broderie personnalisable, idéal pour un look professionnel et personnalisé."
  },
  {
    id: 7,
    name: "Tapis de Souris Standard",
    price: 4990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.5,
    reviews: 112,
    isNew: false,
    isBestseller: true,
    category: "tapis",
    type: "Standard",
    colors: ["Noir"],
    collections: ["Basique"],
    description: "Tapis de souris standard avec surface antidérapante et base en caoutchouc pour une utilisation confortable."
  },
  {
    id: 8,
    name: "Tapis de Souris XXL Gamer",
    price: 9990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.8,
    reviews: 145,
    isNew: true,
    isBestseller: true,
    category: "tapis",
    type: "XXL",
    colors: ["Noir", "Rouge"],
    collections: ["Gaming"],
    description: "Tapis de souris XXL pour gamers avec surface ultra-lisse et design gaming agressif."
  },
  {
    id: 9,
    name: "Tapis de Souris avec Repose-Poignet",
    price: 7990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.6,
    reviews: 87,
    isNew: false,
    isBestseller: false,
    category: "tapis",
    type: "Ergonomique",
    colors: ["Noir", "Bleu"],
    collections: ["Confort"],
    description: "Tapis de souris ergonomique avec repose-poignet intégré pour un confort optimal pendant de longues sessions."
  },
  {
    id: 10,
    name: "Coussin Décoratif Personnalisable",
    price: 11990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.7,
    reviews: 76,
    isNew: true,
    isBestseller: false,
    category: "coussins",
    type: "Décoratif",
    colors: ["Blanc", "Beige", "Gris"],
    collections: ["Maison"],
    description: "Coussin décoratif personnalisable pour ajouter une touche unique à votre intérieur."
  },
  {
    id: 11,
    name: "Coussin Photo Recto-Verso",
    price: 14990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.9,
    reviews: 58,
    isNew: true,
    isBestseller: true,
    category: "coussins",
    type: "Photo",
    colors: ["Blanc"],
    collections: ["Premium"],
    description: "Coussin photo recto-verso permettant d'afficher deux photos différentes sur chaque face."
  },
  {
    id: 12,
    name: "Coussin de Sol XXL",
    price: 17990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.8,
    reviews: 45,
    isNew: true,
    isBestseller: false,
    category: "coussins",
    type: "XXL",
    colors: ["Beige", "Gris", "Marron"],
    collections: ["Confort"],
    description: "Coussin de sol XXL ultra-confortable pour créer un espace détente dans votre salon."
  },
  {
    id: 13,
    name: "Coque Gospel Personnalisée",
    price: 15000,
    image: "/images/accueil/produits_vedettes/coque _gospel_personnalisée/gospel.png",
    rating: 5,
    reviews: 24,
    isNew: true,
    isBestseller: false,
    category: "coques",
    type: "Gospel",
    colors: ["Noir", "Blanc", "Transparent"],
    collections: ["Gospel", "Spirituel"],
    description: "Exprimez votre foi avec style grâce à notre collection de coques Gospel. Des designs inspirants et des messages spirituels pour votre téléphone."
  },
  {
    id: 14,
    name: "Design Moderne Premium",
    price: 12000,
    image: "/images/accueil/produits_vedettes/design_moderne_premium/moderne.jpeg",
    rating: 4,
    reviews: 18,
    isNew: false,
    isBestseller: true,
    category: "coques",
    type: "Design",
    colors: ["Noir", "Blanc", "Gris", "Bleu"],
    collections: ["Premium", "Moderne"],
    description: "Un design contemporain et élégant pour votre téléphone. Matériaux premium et finition haut de gamme pour une protection optimale."
  },
  {
    id: 15,
    name: "Coque Tendance 2024",
    price: 10000,
    image: "/images/accueil/produits_vedettes/coque-tendance_2024/tendance.png",
    rating: 5,
    reviews: 32,
    isNew: true,
    isBestseller: false,
    category: "coques",
    type: "Tendance",
    colors: ["Rose", "Violet", "Vert", "Orange"],
    collections: ["Tendance", "2024"],
    description: "Restez à la pointe de la mode avec notre collection 2024. Des motifs tendance et des couleurs vibrantes pour un style unique."
  },
  {
    id: 16,
    name: "Stylo Personnalisable Standard",
    price: 2990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.3,
    reviews: 67,
    isNew: false,
    isBestseller: true,
    category: "stylos",
    type: "Standard",
    colors: ["Bleu", "Noir", "Rouge"],
    collections: ["Basique"],
    description: "Stylo personnalisable de qualité standard, parfait pour vos besoins quotidiens."
  },
  {
    id: 17,
    name: "Coque iPhone Personnalisée",
    price: 8990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.8,
    reviews: 156,
    isNew: true,
    isBestseller: true,
    category: "phone_cases",
    type: "Silicone",
    colors: ["Transparent", "Noir", "Blanc"],
    collections: ["Protection"],
    description: "Coque de protection personnalisable pour iPhone, alliant style et protection."
  },
  {
    id: 18,
    name: "Porte-clé Personnalisé",
    price: 3990,
    image: "/images/placeholder.svg?height=400&width=400",
    rating: 4.2,
    reviews: 89,
    isNew: false,
    isBestseller: false,
    category: "accessoires",
    type: "Métal",
    colors: ["Argent", "Or"],
    collections: ["Accessoires"],
    description: "Porte-clé en métal personnalisable, idéal pour vos clés ou comme cadeau."
  }
]

export default function ProductPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const productId = parseInt(resolvedParams.id)
  const product = products.find(p => p.id === productId)
  const relatedProducts = products.filter(p => p.category === product?.category && p.id !== productId).slice(0, 4)
  const { toast } = useToast()

  const [selectedColor, setSelectedColor] = useState<string>("")  
  const [selectedPhoneSeries, setSelectedPhoneSeries] = useState<string>("")  
  const [selectedSize, setSelectedSize] = useState<string>("")  
  const [selectedMugType, setSelectedMugType] = useState<string>("")  
  const [quantity, setQuantity] = useState<number>(1)

  // Déterminer le type de produit basé sur la catégorie
  const getProductType = () => {
    if (!product) return 'other'
    
    if (product.category === 'phone_cases' || product.category === 'coques') {
      return 'phone_case'
    }
    if (product.category === 'tshirts') {
      return 'tshirt'
    }
    if (product.category === 'tasses') {
      return 'mug'
    }
    if (product.category === 'stylos' || product.name.toLowerCase().includes('stylo')) {
      return 'pen'
    }
    return 'other'
  }

  const productType = getProductType()

  if (!product) {
    return <div className="container mx-auto p-8">Produit non trouvé</div>
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF'
    }).format(price)
  }

  const handleAddToCart = async () => {
    // Validation selon le type de produit
    if (productType === 'phone_case') {
      if (!selectedPhoneSeries) {
        toast({
          title: "Série du téléphone requise",
          description: "Veuillez sélectionner une série de téléphone",
          variant: "destructive"
        })
        return
      }
    } else if (productType === 'tshirt') {
      if (!selectedSize) {
        toast({
          title: "Taille requise",
          description: "Veuillez sélectionner une taille",
          variant: "destructive"
        })
        return
      }
      if (!selectedColor) {
        toast({
          title: "Couleur requise",
          description: "Veuillez sélectionner une couleur",
          variant: "destructive"
        })
        return
      }
    } else if (productType === 'mug') {
      if (!selectedMugType) {
        toast({
          title: "Type de tasse requis",
          description: "Veuillez sélectionner le type de tasse (standard ou magique)",
          variant: "destructive"
        })
        return
      }
    } else if (productType === 'other') {
      // Pour les autres produits, rediriger vers WhatsApp
      const whatsappMessage = encodeURIComponent(
        `Bonjour, je suis intéressé(e) par le produit "${product.name}". Pouvez-vous me donner plus de détails sur les options disponibles ?`
      )
      const whatsappUrl = `https://wa.me/22500000000?text=${whatsappMessage}`
      window.open(whatsappUrl, '_blank')
      return
    }
    // Pour les stylos (pen), pas de validation nécessaire

    try {
      const customizationData: any = {
        productId: product.id,
        quantity,
        price: product.price
      }

      // Ajouter les données spécifiques selon le type
      if (productType === 'phone_case') {
        customizationData.phoneSeries = selectedPhoneSeries
      } else if (productType === 'tshirt') {
        customizationData.size = selectedSize
        customizationData.color = selectedColor
      } else if (productType === 'mug') {
        customizationData.mugType = selectedMugType
      }

      const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customizationData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Erreur lors de l\'ajout au panier')
      }

      toast({
        title: "Produit ajouté",
        description: "Le produit a été ajouté à votre panier",
      })
    } catch (error) {
      console.error('Erreur:', error)
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Une erreur est survenue lors de l'ajout au panier",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="container mx-auto p-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Image du produit */}
        <div className="relative h-[500px] w-full">
          <Image
            src={product.image}
            alt={product.name}
            fill
            style={{ objectFit: "cover" }}
            className="rounded-lg"
          />
        </div>

        {/* Informations du produit */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold">{product.name}</h1>
            <div className="flex items-center gap-2 mt-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(product.rating)
                        ? "text-yellow-400 fill-yellow-400"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-500">
                ({product.reviews} avis)
              </span>
            </div>
          </div>

          <div className="text-2xl font-bold text-purple-600">
            {formatPrice(product.price)}
          </div>

          <Separator />

          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Description</h2>
            <p className="text-gray-600">{product.description}</p>
          </div>

          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Caractéristiques</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-gray-500">Catégorie</p>
                <p className="font-medium">{product.category}</p>
              </div>
              <div>
                <p className="text-gray-500">Type</p>
                <p className="font-medium">{product.type}</p>
              </div>
              <div>
                <p className="text-gray-500">Couleurs disponibles</p>
                <p className="font-medium">{product.colors.join(", ")}</p>
              </div>
              <div>
                <p className="text-gray-500">Collections</p>
                <p className="font-medium">{product.collections.join(", ")}</p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {/* Options pour les coques de téléphone */}
            {productType === 'phone_case' && (
              <div>
                <label className="block text-sm font-medium mb-2">Série du téléphone</label>
                <Select value={selectedPhoneSeries} onValueChange={setSelectedPhoneSeries}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez une série" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="iphone-13">iPhone 13</SelectItem>
                    <SelectItem value="iphone-14">iPhone 14</SelectItem>
                    <SelectItem value="iphone-15">iPhone 15</SelectItem>
                    <SelectItem value="samsung-s21">Samsung Galaxy S21</SelectItem>
                    <SelectItem value="samsung-s22">Samsung Galaxy S22</SelectItem>
                    <SelectItem value="samsung-s23">Samsung Galaxy S23</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Options pour les t-shirts */}
            {productType === 'tshirt' && (
              <>
                <div>
                  <label className="block text-sm font-medium mb-2">Taille</label>
                  <Select value={selectedSize} onValueChange={setSelectedSize}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionnez une taille" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="xs">XS</SelectItem>
                      <SelectItem value="s">S</SelectItem>
                      <SelectItem value="m">M</SelectItem>
                      <SelectItem value="l">L</SelectItem>
                      <SelectItem value="xl">XL</SelectItem>
                      <SelectItem value="xxl">XXL</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Couleur</label>
                  <Select value={selectedColor} onValueChange={setSelectedColor}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionnez une couleur" />
                    </SelectTrigger>
                    <SelectContent>
                      {product.colors.map((color) => (
                        <SelectItem key={color} value={color}>
                          {color}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}

            {/* Options pour les tasses */}
            {productType === 'mug' && (
              <div>
                <label className="block text-sm font-medium mb-2">Type de tasse</label>
                <Select value={selectedMugType} onValueChange={setSelectedMugType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez le type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="magique">Magique</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Message pour les autres produits */}
            {productType === 'other' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">Personnalisation sur mesure</h3>
                <p className="text-blue-700 text-sm mb-3">
                  Ce produit nécessite une consultation personnalisée pour définir les options disponibles.
                </p>
                <p className="text-blue-600 text-sm">
                  Cliquez sur "Ajouter au panier" pour nous contacter via WhatsApp et discuter de vos besoins.
                </p>
              </div>
            )}

            {/* Pas d'options spéciales pour les stylos */}
            {productType === 'pen' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-700 text-sm">
                  Ce produit est prêt à être ajouté au panier sans options supplémentaires.
                </p>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium mb-2">Quantité</label>
              <Input
                type="number"
                min="1"
                value={quantity}
                onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                className="w-24"
              />
            </div>
          </div>

          <div className="flex gap-4">
            <Button 
              className="flex-1 bg-purple-600 hover:bg-purple-700"
              onClick={handleAddToCart}
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              {productType === 'other' ? 'Contacter via WhatsApp' : 'Ajouter au panier'}
            </Button>
            <Button variant="outline" size="icon">
              <Heart className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="icon">
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Produits similaires */}
      {relatedProducts.length > 0 && (
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-8">Produits similaires</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {relatedProducts.map((relatedProduct) => (
              <Card key={relatedProduct.id} className="overflow-hidden">
                <div className="relative h-48 w-full">
                  <Image
                    src={relatedProduct.image}
                    alt={relatedProduct.name}
                    fill
                    style={{ objectFit: "cover" }}
                  />
                </div>
                <div className="p-4">
                  <h3 className="font-semibold">{relatedProduct.name}</h3>
                  <p className="text-purple-600 font-bold mt-2">
                    {formatPrice(relatedProduct.price)}
                  </p>
                  <Button className="w-full mt-4 bg-purple-600 hover:bg-purple-700">
                    Voir le produit
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}