import { createClient } from '@supabase/supabase-js';

// Vérifier si nous sommes dans un environnement de navigateur
const isBrowser = typeof window !== 'undefined';

// Définir des valeurs par défaut pour le développement local
// Ces valeurs ne seront utilisées que si les variables d'environnement sont manquantes
const fallbackUrl = 'https://bekwlxorzlyoudsnajsn.supabase.co';
const fallbackKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJla3dseG9yemx5b3Vkc25hanNuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NzUzOTgsImV4cCI6MjA2MDU1MTM5OH0.yRKV3bo0ww0GIW1LtBB0HJQQErVWHbbbpkWVcFScS34';

// Récupérer les variables d'environnement ou utiliser les valeurs par défaut
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || fallbackUrl;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || fallbackKey;

// Avertir si les variables d'environnement sont manquantes
if ((!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) && isBrowser) {
  console.warn('Avertissement: Supabase URL ou Anon Key manquant. Utilisation des valeurs par défaut. Veuillez vérifier votre fichier .env.local.');
}

// Créer le client Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    storageKey: 'sb-auth-token',
    storage: {
      getItem: (key) => {
        if (typeof window === 'undefined') return null;
        return JSON.parse(window.localStorage.getItem(key) || 'null');
      },
      setItem: (key, value) => {
        if (typeof window === 'undefined') return;
        window.localStorage.setItem(key, JSON.stringify(value));
      },
      removeItem: (key) => {
        if (typeof window === 'undefined') return;
        window.localStorage.removeItem(key);
      },
    },
  },
});

// Fonction pour vérifier si les cookies Supabase sont présents
export function checkSupabaseCookies() {
  if (typeof window === 'undefined') return false;
  
  const hasAccessToken = document.cookie.includes('sb-access-token');
  const hasRefreshToken = document.cookie.includes('sb-refresh-token');
  
  console.log('Vérification des cookies Supabase:', {
    hasAccessToken,
    hasRefreshToken,
    cookies: document.cookie.split(';').map(c => c.trim())
  });
  
  return hasAccessToken && hasRefreshToken;
}

// Fonction pour vérifier la connexion à Supabase et la structure de la base de données
export async function checkSupabaseConnection() {
  try {
    console.log("Vérification de la connexion à Supabase...");

    // Vérifier si les variables d'environnement sont définies
    if (!supabaseUrl || !supabaseAnonKey) {
      console.warn("Variables d'environnement Supabase manquantes");
      return {
        connected: false,
        error: "Configuration Supabase incomplète. Vérifiez vos variables d'environnement.",
      };
    }

    // Vérifier la connexion à Supabase avec un timeout
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error("Délai d'attente dépassé")), 5000)
    );
    
    const queryPromise = supabase.from('cart_items').select('count(*)', { count: 'exact', head: true });
    
    // Race entre la requête et le timeout
    const { data, error } = await Promise.race([
      queryPromise,
      timeoutPromise.then(() => ({ data: null, error: { message: "Délai d'attente dépassé" } }))
    ]);

    if (error) {
      console.warn("Erreur lors de la vérification de la connexion à Supabase:", error.message || "Erreur inconnue");

      // Vérifier si l'erreur est due à une table manquante
      if (error && 'code' in error && error.code === '42P01') {
        return {
          connected: false,
          error: "La table cart_items n'existe pas. Veuillez exécuter le script SQL pour créer la table.",
          details: error
        };
      }

      return {
        connected: false,
        error: error.message || "Erreur de connexion à Supabase",
        details: error
      };
    }

    console.log("Connexion à Supabase établie avec succès!");
    return {
      connected: true,
      message: "Connexion à Supabase établie avec succès!"
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Erreur inconnue";
    console.warn("Erreur inattendue lors de la vérification de la connexion à Supabase:", errorMessage);
    return {
      connected: false,
      error: "Erreur de connexion à Supabase",
      details: errorMessage
    };
  }
}


// Types pour les tables Supabase
export type User = {
  id: string;
  first_name: string;
  last_name: string;
  whatsapp: string;
  email?: string;
  created_at: string;
};

export type Product = {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  subcategory: string;
  image_url: string;
  is_new: boolean;
  is_bestseller: boolean;
  type: string;
  colors: string[];
  collections: string[];
  created_at: string;
};

export type CartItem = {
  id: string;
  user_id: string;
  product_id: string;
  quantity: number;
  customized: boolean;
  customization_data?: any;
  created_at: string;
  product?: Product;
};

export type Order = {
  id: string;
  user_id: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  shipping_address: string;
  shipping_city: string;
  shipping_notes?: string;
  payment_method: string;
  created_at: string;
};

export type OrderItem = {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  price: number;
  customized: boolean;
  customization_data?: any;
  created_at: string;
  product?: Product;
};

// Fonctions d'aide pour interagir avec Supabase
export const supabaseApi = {
  // Paramètres du site
  siteSettings: {
    getAll: async () => {
      const { data, error } = await supabase
        .from('site_settings')
        .select('*');
      return { data, error };
    },

    getByKey: async (key: string) => {
      const { data, error } = await supabase
        .from('site_settings')
        .select('*')
        .eq('key', key)
        .single();
      return { data, error };
    },

    update: async (key: string, value: any) => {
      const { data, error } = await supabase
        .from('site_settings')
        .update({ value })
        .eq('key', key)
        .select();
      return { data, error };
    },
  },

  // Favoris
  favorites: {
    add: async (userId: string, productId: string) => {
      const { data, error } = await supabase
        .from('favorites')
        .insert({
          user_id: userId,
          product_id: productId,
        })
        .select();
      return { data, error };
    },

    remove: async (userId: string, productId: string) => {
      const { data, error } = await supabase
        .from('favorites')
        .delete()
        .eq('user_id', userId)
        .eq('product_id', productId);
      return { data, error };
    },

    getByUser: async (userId: string) => {
      const { data, error } = await supabase
        .from('favorites')
        .select('*, product:products(*)')
        .eq('user_id', userId);
      return { data, error };
    },

    getCount: async (userId: string) => {
      const { count, error } = await supabase
        .from('favorites')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);
      return { count, error };
    },

    check: async (userId: string, productId: string) => {
      const { data, error } = await supabase
        .from('favorites')
        .select('*')
        .eq('user_id', userId)
        .eq('product_id', productId)
        .single();
      return { exists: !!data, error };
    },
  },

  // Authentification
  auth: {
    signUp: async (email: string, password: string, userData: Partial<User>) => {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      });
      return { data, error };
    },
    signIn: async (email: string, password: string) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { data, error };
    },
    signOut: async () => {
      const { error } = await supabase.auth.signOut();
      return { error };
    },
    getUser: async () => {
      const { data, error } = await supabase.auth.getUser();
      return { data, error };
    },
  },

  // Produits
  products: {
    getAll: async () => {
      const { data, error } = await supabase
        .from('products')
        .select('*');
      return { data, error };
    },
    getByCategory: async (category: string) => {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('category', category);
      return { data, error };
    },
    getById: async (id: string) => {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('id', id)
        .single();
      return { data, error };
    },
  },

  // Panier
  cart: {
    getItems: async (userId: string) => {
      try {
        console.log("supabaseApi.cart.getItems - Début de la fonction");

        // Vérifier si l'ID utilisateur est valide
        if (!userId || userId === 'null' || userId === 'undefined') {
          console.error("supabaseApi.cart.getItems - ID utilisateur invalide:", userId);
          return {
            data: [],
            error: {
              message: "ID utilisateur invalide",
              details: `ID fourni: ${userId}`
            }
          };
        }

        // Vérifier si c'est un ID de session anonyme (commence par 'anonymous_')
        const isAnonymous = userId.startsWith('anonymous_');
        console.log("supabaseApi.cart.getItems - Type d'utilisateur:", isAnonymous ? "anonyme" : "connecté");

        console.log("supabaseApi.cart.getItems - Récupération des articles pour l'utilisateur:", userId);
        const { data, error } = await supabase
          .from('cart_items')
          .select('*, product:products(*)')
          .eq('user_id', userId);

        if (error) {
          console.error("supabaseApi.cart.getItems - Erreur Supabase:", error);

          // Vérifier si l'erreur est due à une table manquante
          if (error.code === '42P01') {
            console.error("supabaseApi.cart.getItems - La table cart_items n'existe pas");
            return {
              data: [],
              error: {
                ...error,
                message: "La table cart_items n'existe pas. Veuillez créer la table dans Supabase."
              }
            };
          }
        } else {
          console.log("supabaseApi.cart.getItems - Articles récupérés avec succès:", data?.length || 0);
        }

        return { data, error };
      } catch (error) {
        console.error("supabaseApi.cart.getItems - Erreur inattendue:", error);
        return {
          data: [],
          error: {
            message: "Erreur inattendue lors de la récupération des articles du panier",
            details: error instanceof Error ? error.message : String(error)
          }
        };
      }
    },
    addItem: async (item: Omit<CartItem, 'id' | 'created_at'>) => {
      // Pour les utilisateurs anonymes, on stocke les données dans la même table
      // mais avec un préfixe spécial pour l'ID utilisateur
      const { data, error } = await supabase
        .from('cart_items')
        .insert(item)
        .select();
      return { data, error };
    },
    updateItem: async (id: string, updates: Partial<CartItem>) => {
      const { data, error } = await supabase
        .from('cart_items')
        .update(updates)
        .eq('id', id)
        .select();
      return { data, error };
    },
    removeItem: async (id: string) => {
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('id', id);
      return { error };
    },
    clearCart: async (userId: string) => {
      // Fonctionne de la même manière pour les utilisateurs connectés et anonymes
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('user_id', userId);
      return { error };
    },
  },

  // Commandes
  orders: {
    create: async (order: Omit<Order, 'id' | 'created_at'>, items: Omit<OrderItem, 'id' | 'order_id' | 'created_at'>[]) => {
      // Créer la commande
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .insert(order)
        .select()
        .single();

      if (orderError || !orderData) {
        return { error: orderError };
      }

      // Ajouter les articles de la commande
      const orderItems = items.map(item => ({
        ...item,
        order_id: orderData.id,
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) {
        return { error: itemsError };
      }

      return { data: orderData, error: null };
    },
    getByUser: async (userId: string) => {
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      return { data, error };
    },
    getById: async (id: string) => {
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .select('*')
        .eq('id', id)
        .single();

      if (orderError || !order) {
        return { error: orderError };
      }

      const { data: items, error: itemsError } = await supabase
        .from('order_items')
        .select('*, product:products(*)')
        .eq('order_id', id);

      if (itemsError) {
        return { error: itemsError };
      }

      return { data: { ...order, items }, error: null };
    },
  },
};
