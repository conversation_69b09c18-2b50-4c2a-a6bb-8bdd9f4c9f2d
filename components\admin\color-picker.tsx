"use client";

import { useState, useEffect, useRef } from "react";
import { Pop<PERSON>, <PERSON>overContent, PopoverTrigger } from "@/components/ui/popover";
import { Input } from "@/components/ui/input";

interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
}

export function ColorPicker({ value, onChange }: ColorPickerProps) {
  const [color, setColor] = useState(value || "#000000");
  const [isOpen, setIsOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setColor(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value;
    setColor(newColor);
    onChange(newColor);
  };

  const presetColors = [
    "#000000", "#ffffff", "#f44336", "#e91e63", "#9c27b0", "#673ab7",
    "#3f51b5", "#2196f3", "#03a9f4", "#00bcd4", "#009688", "#4caf50",
    "#8bc34a", "#cddc39", "#ffeb3b", "#ffc107", "#ff9800", "#ff5722",
    "#795548", "#9e9e9e", "#607d8b"
  ];

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div className="flex items-center space-x-2 cursor-pointer">
          <div
            className="w-8 h-8 rounded-full border shadow-sm color-swatch"
            onClick={() => setIsOpen(true)}
            data-color={color}
          />
          <Input
            ref={inputRef}
            type="text"
            value={color}
            onChange={handleChange}
            className="w-24"
          />
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3">
        <div className="space-y-2">
          <div>
            <Input
              type="color"
              value={color}
              onChange={handleChange}
              className="w-full h-10"
            />
          </div>
          <div className="grid grid-cols-7 gap-1">
            {presetColors.map((presetColor) => (
              <div
                key={presetColor}
                className="w-6 h-6 rounded-full cursor-pointer border color-swatch"
                data-color={presetColor}
                onClick={() => {
                  setColor(presetColor);
                  onChange(presetColor);
                  setIsOpen(false);
                }}
              />
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
