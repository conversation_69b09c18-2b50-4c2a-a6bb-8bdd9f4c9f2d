@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import des styles personnalisés */
@import '../styles/product-colors.css';
@import '../styles/image-components.css';
@import '../styles/sidebar.css';
@import '../styles/image-dimensions.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --radius: 0.5rem;

    /* Mise à jour des couleurs pour correspondre à la maquette */
    --primary: 270 70% 40%; /* Violet principal */
    --primary-foreground: 0 0% 100%;

    --secondary: 270 70% 95%;
    --secondary-foreground: 270 70% 40%;

    --accent: 270 70% 60%;
    --accent-foreground: 0 0% 100%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 40% 98%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Améliorations pour la compatibilité mobile */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  h1 {
    font-size: 1.875rem !important;
  }

  h2 {
    font-size: 1.5rem !important;
  }

  .mobile-stack {
    flex-direction: column !important;
  }

  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-hidden {
    display: none !important;
  }

  .mobile-visible {
    display: block !important;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .mobile-py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .mobile-px-4 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* Améliorations pour les grands écrans */
@media (min-width: 1536px) {
  .container {
    max-width: 1400px;
  }
}

/* Styles personnalisés pour les aperçus de couleur */
.color-preview[data-color] {
  background-color: var(--color-value);
}

.color-preview[data-color="#6d28d9"] {
  --color-value: #6d28d9;
}

.color-preview[data-color="#8b5cf6"] {
  --color-value: #8b5cf6;
}

/* Styles pour les swatches de couleur dans le color picker */
.color-swatch {
  background-color: var(--swatch-color);
}

.color-swatch[data-color] {
  --swatch-color: attr(data-color);
}

/* Styles CSS pour les aperçus de couleur */
.color-preview[data-color="#000000"] { background-color: #000000; }
.color-preview[data-color="#ffffff"] { background-color: #ffffff; }
.color-preview[data-color="#f44336"] { background-color: #f44336; }
.color-preview[data-color="#e91e63"] { background-color: #e91e63; }
.color-preview[data-color="#9c27b0"] { background-color: #9c27b0; }
.color-preview[data-color="#673ab7"] { background-color: #673ab7; }
.color-preview[data-color="#3f51b5"] { background-color: #3f51b5; }
.color-preview[data-color="#2196f3"] { background-color: #2196f3; }
.color-preview[data-color="#03a9f4"] { background-color: #03a9f4; }
.color-preview[data-color="#00bcd4"] { background-color: #00bcd4; }
.color-preview[data-color="#009688"] { background-color: #009688; }
.color-preview[data-color="#4caf50"] { background-color: #4caf50; }
.color-preview[data-color="#8bc34a"] { background-color: #8bc34a; }
.color-preview[data-color="#cddc39"] { background-color: #cddc39; }
.color-preview[data-color="#ffeb3b"] { background-color: #ffeb3b; }
.color-preview[data-color="#ffc107"] { background-color: #ffc107; }
.color-preview[data-color="#ff9800"] { background-color: #ff9800; }
.color-preview[data-color="#ff5722"] { background-color: #ff5722; }
.color-preview[data-color="#795548"] { background-color: #795548; }
.color-preview[data-color="#9e9e9e"] { background-color: #9e9e9e; }
.color-preview[data-color="#607d8b"] { background-color: #607d8b; }

/* Styles CSS pour les swatches de couleur */
.color-swatch[data-color="#000000"] { background-color: #000000; }
.color-swatch[data-color="#ffffff"] { background-color: #ffffff; }
.color-swatch[data-color="#f44336"] { background-color: #f44336; }
.color-swatch[data-color="#e91e63"] { background-color: #e91e63; }
.color-swatch[data-color="#9c27b0"] { background-color: #9c27b0; }
.color-swatch[data-color="#673ab7"] { background-color: #673ab7; }
.color-swatch[data-color="#3f51b5"] { background-color: #3f51b5; }
.color-swatch[data-color="#2196f3"] { background-color: #2196f3; }
.color-swatch[data-color="#03a9f4"] { background-color: #03a9f4; }
.color-swatch[data-color="#00bcd4"] { background-color: #00bcd4; }
.color-swatch[data-color="#009688"] { background-color: #009688; }
.color-swatch[data-color="#4caf50"] { background-color: #4caf50; }
.color-swatch[data-color="#8bc34a"] { background-color: #8bc34a; }
.color-swatch[data-color="#cddc39"] { background-color: #cddc39; }
.color-swatch[data-color="#ffeb3b"] { background-color: #ffeb3b; }
.color-swatch[data-color="#ffc107"] { background-color: #ffc107; }
.color-swatch[data-color="#ff9800"] { background-color: #ff9800; }
.color-swatch[data-color="#ff5722"] { background-color: #ff5722; }
.color-swatch[data-color="#795548"] { background-color: #795548; }
.color-swatch[data-color="#9e9e9e"] { background-color: #9e9e9e; }
.color-swatch[data-color="#607d8b"] { background-color: #607d8b; }

/* Animation de rebond lente pour le bouton WhatsApp */
@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 3s infinite;
}



