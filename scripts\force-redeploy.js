/**
 * Script pour forcer un redéploiement en modifiant légèrement un fichier
 * et en ajoutant un commentaire de version
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Fonction pour lire un fichier
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier ${filePath}:`, error);
    return null;
  }
}

// Fonction pour écrire dans un fichier
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'écriture dans le fichier ${filePath}:`, error);
    return false;
  }
}

// Fonction pour obtenir la date et l'heure actuelles
function getCurrentDateTime() {
  const now = new Date();
  return now.toISOString().replace(/[:.]/g, '-');
}

// Fonction pour mettre à jour le fichier next.config.js
function updateNextConfig() {
  console.log("Mise à jour du fichier next.config.js...");
  
  const configPath = path.join(process.cwd(), 'next.config.js');
  const content = readFile(configPath);
  
  if (!content) return false;
  
  // Ajouter ou mettre à jour le commentaire de version
  const versionComment = `// Version: ${getCurrentDateTime()} - Force redeploy`;
  
  if (content.includes('// Version:')) {
    // Remplacer le commentaire de version existant
    const newContent = content.replace(/\/\/ Version:.*$/m, versionComment);
    return writeFile(configPath, newContent);
  } else {
    // Ajouter le commentaire de version au début du fichier
    const newContent = `${versionComment}\n${content}`;
    return writeFile(configPath, newContent);
  }
}

// Fonction pour mettre à jour le fichier app/models/page.tsx
function updateModelsPage() {
  console.log("Mise à jour du fichier app/models/page.tsx...");
  
  const filePath = path.join(process.cwd(), 'app', 'models', 'page.tsx');
  const content = readFile(filePath);
  
  if (!content) return false;
  
  // Ajouter ou mettre à jour le commentaire de version
  const versionComment = `// Version: ${getCurrentDateTime()} - Force redeploy`;
  
  if (content.includes('// Version:')) {
    // Remplacer le commentaire de version existant
    const newContent = content.replace(/\/\/ Version:.*$/m, versionComment);
    return writeFile(filePath, newContent);
  } else {
    // Ajouter le commentaire de version après les imports
    const importEndIndex = content.indexOf('export default');
    if (importEndIndex === -1) {
      // Si 'export default' n'est pas trouvé, ajouter au début du fichier
      const newContent = `${versionComment}\n${content}`;
      return writeFile(filePath, newContent);
    } else {
      // Ajouter avant 'export default'
      const newContent = 
        content.slice(0, importEndIndex) + 
        `\n${versionComment}\n\n` + 
        content.slice(importEndIndex);
      return writeFile(filePath, newContent);
    }
  }
}

// Fonction pour committer et pousser les modifications
function commitAndPush() {
  console.log("Commit et push des modifications...");
  
  try {
    // Ajouter les fichiers modifiés
    execSync('git add next.config.js app/models/page.tsx');
    
    // Créer un commit
    execSync(`git commit -m "Force redeploy: ${getCurrentDateTime()}"`);
    
    // Pousser vers GitHub
    execSync('git push');
    
    console.log("Modifications poussées avec succès.");
    return true;
  } catch (error) {
    console.error("Erreur lors du commit et push:", error);
    return false;
  }
}

// Fonction principale
function main() {
  console.log("Début du processus de redéploiement forcé...");
  
  // Mettre à jour le fichier next.config.js
  const configUpdated = updateNextConfig();
  
  // Mettre à jour le fichier app/models/page.tsx
  const modelsPageUpdated = updateModelsPage();
  
  if (configUpdated || modelsPageUpdated) {
    // Committer et pousser les modifications
    const pushed = commitAndPush();
    
    if (pushed) {
      console.log("\nRedéploiement forcé initié avec succès.");
      console.log("Le site devrait être redéployé dans quelques minutes.");
      console.log("Vérifiez le tableau de bord Vercel pour suivre le déploiement.");
    } else {
      console.log("\nÉchec du redéploiement forcé.");
      console.log("Veuillez vérifier les erreurs ci-dessus et réessayer.");
    }
  } else {
    console.log("\nAucune modification n'a été effectuée.");
    console.log("Le redéploiement forcé n'a pas été initié.");
  }
}

// Exécuter la fonction principale
main();
