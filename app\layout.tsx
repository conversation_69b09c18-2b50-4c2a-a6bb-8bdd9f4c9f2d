import type React from "react"
import "@/app/globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

// Importez ces éléments manquants
import { cn } from "@/lib/utils"


const inter = Inter({ subsets: ["latin"] })

// Définissez fontSans
const fontSans = inter.className

// Définissez RootLayoutProps
interface RootLayoutProps {
  children: React.ReactNode
}

export const metadata: Metadata = {
  title: "HCP-DESIGN CI",
  description: "Personnalisez vos accessoires de téléphone",
  manifest: "/site.webmanifest",
  icons: {
    icon: [
      {
        url: "/favicon.svg",
        type: "image/svg+xml",
      },
      {
        url: "/favicon.ico",
        sizes: "32x32",
        type: "image/x-icon",
      },
      {
        url: "/icons/icon-16.png",
        sizes: "16x16",
        type: "image/png",
      },
      {
        url: "/icons/icon-32.png",
        sizes: "32x32",
        type: "image/png",
      },
    ],
    shortcut: "/favicon.ico",
    apple: [
      {
        url: "/icons/icon-180.png",
        sizes: "180x180",
        type: "image/png",
      },
    ],
  },
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          fontSans
        )}
      >
        <ThemeProvider attribute="class" defaultTheme="light">
          <SiteSettingsProvider>
            <AuthProvider>
              <CartProvider>
                <FavoritesProvider>
                  <CustomerProvider>
                    <SupabaseConnectionChecker />
                    <AnnouncementBanner />
                    <Header />

                    {children}
                    <Footer />
                    <WhatsAppButton />
                    <Toaster />
                  </CustomerProvider>
                </FavoritesProvider>
              </CartProvider>
            </AuthProvider>
          </SiteSettingsProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
import Header from "@/components/header"
import Footer from "@/components/footer"
import { AuthProvider } from "@/hooks/use-auth"
import { CartProvider } from "@/hooks/use-cart"
import { CustomerProvider } from "@/hooks/use-customer"
import { SiteSettingsProvider } from "@/hooks/use-site-settings"
import { FavoritesProvider } from "@/hooks/favorites-provider"
import { Toaster } from "@/components/ui/sonner"
import WhatsAppButton from "@/components/whatsapp-button"
import AnnouncementBanner from "@/components/announcement-banner"
import SupabaseConnectionChecker from "@/components/supabase-connection-checker"

