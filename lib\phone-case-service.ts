// Types pour les modèles de téléphone et les coques
export interface PhoneModel {
  id: string;
  name: string;
  brand: string;
  dimensions: {
    width: number;
    height: number;
  };
}

export interface PhoneCase {
  id: string;
  modelId: string;
  imageUrl: string;
  name: string;
}

// Dans une application réelle, ces données viendraient d'une API ou d'une base de données
// Pour l'instant, nous utilisons le localStorage pour la persistance côté client

export const PhoneCaseService = {
  // Récupération des données depuis le localStorage
  // TODO: Implémenter les méthodes du service
}