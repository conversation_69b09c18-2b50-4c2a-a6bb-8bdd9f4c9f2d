// Fonction pour générer des textes alternatifs descriptifs
export function generateAltText(imageName: string, context?: string): string {
  // Extraire le nom de base de l'image sans extension
  const baseName = imageName.split('/').pop()?.split('.')[0] || imageName
  
  // Mapper les noms d'images à des descriptions significatives
  const altTextMap: Record<string, string> = {
    "fete-des-meres": "Promotion spéciale pour la fête des mères avec une coque personnalisée",
    "welcome-offer": "Offre de bienvenue pour les nouveaux clients",
    "family-pack": "Pack famille avec réduction pour l'achat de plusieurs coques",
    // Ajouter d'autres mappages selon vos images
  }
  
  // Retourner le texte alternatif correspondant ou une description générique
  return altTextMap[baseName] || 
    `${context ? context + " - " : ""}${baseName.replace(/-/g, " ").replace(/^\w/, c => c.toUpperCase())}`
}

// Fonction pour améliorer le contraste des couleurs
export function ensureAccessibleColor(backgroundColor: string, textColor: string): string {
  // Logique simple pour garantir un contraste suffisant
  // Dans une implémentation réelle, utilisez une bibliothèque comme color-contrast
  if (backgroundColor.includes("white") || backgroundColor.includes("light")) {
    return textColor.includes("light") ? "#333333" : textColor
  }
  
  if (backgroundColor.includes("dark") || backgroundColor.includes("black")) {
    return textColor.includes("dark") ? "#FFFFFF" : textColor
  }
  
  return textColor
}