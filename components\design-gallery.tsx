"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

interface DesignGalleryProps {
  onSelectDesign: (designUrl: string) => void
  selectedDesign: string | null
}

interface Design {
  id: string
  url: string
  name: string
}

interface DesignCategory {
  id: string
  name: string
  designs: Design[]
}

// Catégories de designs
const designCategoryDefinitions = [
  { id: "abstract", name: "Abstrait", folder: "abstraits" },
  { id: "nature", name: "Nature", folder: "nature" },
  { id: "animals", name: "Animaux", folder: "animaux" },
  { id: "patterns", name: "Motifs", folder: "motifs" },
  { id: "personnalises", name: "Personnalisés", folder: "personnalises" },
  { id: "sports", name: "Sports", folder: "sports" },
  { id: "music", name: "Musique", folder: "musique" },
  { id: "quotes", name: "Citations", folder: "citations" },
]

export default function DesignGallery({ onSelectDesign, selectedDesign }: DesignGalleryProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeCategory, setActiveCategory] = useState(designCategoryDefinitions[0].id)
  const [designCategories, setDesignCategories] = useState<DesignCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Charger les designs au chargement du composant
  useEffect(() => {
    async function loadDesigns() {
      setIsLoading(true)
      try {
        // Créer un tableau pour stocker les catégories avec leurs designs
        const categories: DesignCategory[] = []

        // Pour chaque définition de catégorie, créer une entrée avec des designs
        for (const category of designCategoryDefinitions) {
          // Dans une application réelle, vous feriez un appel API ici
          // Pour l'instant, nous simulons le chargement des designs à partir des dossiers

          // Construire le chemin du dossier
          const folderPath = `/images/designs/${category.folder}`

          // Simuler le chargement des designs (dans une application réelle, ce serait un appel API)
          // Pour l'instant, nous créons quelques designs fictifs basés sur le dossier
          const designs: Design[] = []

          // Ajouter quelques designs fictifs (à remplacer par un vrai chargement)
          for (let i = 1; i <= 4; i++) {
            designs.push({
              id: `${category.id}-${i}`,
              url: `${folderPath}/${category.folder}-${i}.png`,
              name: `${category.name} ${i}`
            })
          }

          // Ajouter une entrée spéciale pour les designs personnalisés
          if (category.id === 'personnalises') {
            designs.push({
              id: 'personnalises-main',
              url: `/images/designs/personnalises/personnalises.png`,
              name: 'Design Personnalisé'
            })
          }

          // Ajouter la catégorie avec ses designs
          categories.push({
            id: category.id,
            name: category.name,
            designs
          })
        }

        setDesignCategories(categories)
      } catch (error) {
        console.error('Erreur lors du chargement des designs:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadDesigns()
  }, [])

  // Filter designs based on search query
  const filteredDesigns = searchQuery
    ? designCategories.flatMap((category) =>
        category.designs.filter((design) => design.name.toLowerCase().includes(searchQuery.toLowerCase())),
      )
    : designCategories.find((category) => category.id === activeCategory)?.designs || []

  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Rechercher un design..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {!searchQuery && (
        <Tabs value={activeCategory} onValueChange={setActiveCategory}>
          <TabsList className="grid grid-cols-2 md:grid-cols-4 gap-1">
            {designCategoryDefinitions.map((category) => (
              <TabsTrigger key={category.id} value={category.id}>
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      )}

      {isLoading ? (
        <div className="text-center py-12">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          <p className="mt-2 text-muted-foreground">Chargement des designs...</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-4">
            {filteredDesigns.map((design) => (
              <div
                key={design.id}
                className={`relative cursor-pointer rounded-md overflow-hidden border-2 transition-all ${selectedDesign === design.url ? "border-primary shadow-md" : "border-transparent hover:border-muted-foreground/30"}`}
                onClick={() => onSelectDesign(design.url)}
              >
                <img
                  src={design.url || "/images/phone-cases/phone-case-placeholder.png"}
                  alt={design.name}
                  className="w-full aspect-[1/2] object-cover"
                  onError={(e) => {
                    // Si l'image ne peut pas être chargée, utiliser une image de remplacement
                    e.currentTarget.src = "/images/phone-cases/phone-case-placeholder.png";
                  }}
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-2 text-xs">{design.name}</div>
              </div>
            ))}
          </div>

          {filteredDesigns.length === 0 && !isLoading && (
            <div className="text-center py-8 text-muted-foreground">
              {searchQuery
                ? `Aucun design trouvé pour "${searchQuery}"`
                : "Aucun design disponible dans cette catégorie"}
            </div>
          )}
        </>
      )}
    </div>
  )
}
