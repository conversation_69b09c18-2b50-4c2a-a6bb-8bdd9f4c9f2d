#!/bin/bash

# Script pour créer des placeholders PNG pour les variantes de produits

# Dossier de base pour les produits
BASE_DIR="./public/images/products"

# Créer un dossier temporaire pour les placeholders
mkdir -p ./temp_placeholders

# Créer un placeholder PNG simple pour chaque catégorie de produit
# Tasses
echo "Création du placeholder pour les tasses"
cat > ./temp_placeholders/mug_placeholder.html << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Tasse</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# T-shirts
echo "Création du placeholder pour les t-shirts"
cat > ./temp_placeholders/tshirt_placeholder.html << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">T-shirt</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Tapis de souris
echo "Création du placeholder pour les tapis de souris"
cat > ./temp_placeholders/mousepad_placeholder.html << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Tapis de souris</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Coussins
echo "Création du placeholder pour les coussins"
cat > ./temp_placeholders/cushion_placeholder.html << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Coussin</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Porte-clés
echo "Création du placeholder pour les porte-clés"
cat > ./temp_placeholders/keychain_placeholder.html << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Porte-clé</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Créer un placeholder PNG générique
echo "Création du placeholder générique"
cat > ./temp_placeholders/generic_placeholder.html << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Produit</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Créer un fichier placeholder.png dans chaque dossier de variante
echo "Copie des placeholders dans les dossiers de variantes"

# Tasses
for variant in ceramique thermosensible isotherme emaillee; do
  mkdir -p "$BASE_DIR/mugs/$variant"
  cp ./temp_placeholders/mug_placeholder.html "$BASE_DIR/mugs/$variant/placeholder.html"
done

# T-shirts
for variant in coton premium polo "manches-longues"; do
  mkdir -p "$BASE_DIR/tshirts/$variant"
  cp ./temp_placeholders/tshirt_placeholder.html "$BASE_DIR/tshirts/$variant/placeholder.html"
done

# Tapis de souris
for variant in standard xxl ergonomique rgb; do
  mkdir -p "$BASE_DIR/mousepads/$variant"
  cp ./temp_placeholders/mousepad_placeholder.html "$BASE_DIR/mousepads/$variant/placeholder.html"
done

# Coussins
for variant in decoratif photo xxl cervical; do
  mkdir -p "$BASE_DIR/cushions/$variant"
  cp ./temp_placeholders/cushion_placeholder.html "$BASE_DIR/cushions/$variant/placeholder.html"
done

# Porte-clés
for variant in photo metal multifonction bois; do
  mkdir -p "$BASE_DIR/keychains/$variant"
  cp ./temp_placeholders/keychain_placeholder.html "$BASE_DIR/keychains/$variant/placeholder.html"
done

# Copier le placeholder générique dans les dossiers principaux
cp ./temp_placeholders/generic_placeholder.html "$BASE_DIR/placeholder.html"

echo "Création des placeholders terminée"
