"use client";

import { useEffect, useState } from "react";
import { checkSupabaseConnection } from "@/lib/supabase";
import { useToast } from "@/components/ui/use-toast";

export default function SupabaseConnectionChecker() {
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'error'>('checking');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    let isMounted = true;
    
    const checkConnection = async () => {
      try {
        // Ajouter un délai pour éviter de bloquer le rendu initial
        await new Promise(resolve => setTimeout(resolve, 500));
        
        if (!isMounted) return;
        
        const result = await checkSupabaseConnection();
        
        if (!isMounted) return;
        
        if (result.connected) {
          setConnectionStatus('connected');
          console.log("Supabase connecté avec succès!");
        } else {
          setConnectionStatus('error');
          const errorMsg = result.error || "Erreur de connexion à Supabase";
          setErrorMessage(errorMsg);
          console.warn("Problème de connexion à Supabase:", errorMsg);
          
          // Afficher un toast d'erreur
          toast({
            title: "Problème de connexion",
            description: "Impossible de se connecter à la base de données. Certaines fonctionnalités peuvent être limitées.",
            variant: "destructive",
          });
        }
      } catch (error) {
        if (!isMounted) return;
        
        setConnectionStatus('error');
        const errorMsg = error instanceof Error ? error.message : "Erreur inconnue";
        setErrorMessage(errorMsg);
        console.warn("Erreur lors de la vérification de la connexion:", errorMsg);
        
        // Afficher un toast d'erreur
        toast({
          title: "Problème de connexion",
          description: "Impossible de se connecter à la base de données. Certaines fonctionnalités peuvent être limitées.",
          variant: "destructive",
        });
      }
    };

    checkConnection();
    
    return () => {
      isMounted = false;
    };
  }, [toast]);

  // Ce composant ne rend rien visuellement, il effectue simplement la vérification
  return null;
}

