"use client"

import { Heart, ShoppingCart, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useSimpleFavorites } from "@/hooks/use-simple-favorites"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import Link from "next/link"
import Image from "next/image"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function FavoritesMenu() {
  const { favorites, removeFromFavorites } = useSimpleFavorites()
  const { addToCart } = useSimpleCart()
  
  // Formater le prix
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF'
    }).format(price)
  }
  
  // Ajouter un favori au panier
  const handleAddToCart = (favorite: any) => {
    addToCart({
      id: favorite.id,
      name: favorite.name,
      price: favorite.price,
      image_url: favorite.image_url,
      customized: false
    }, 1)
  }
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Heart className="h-5 w-5" />
          {favorites.length > 0 && (
            <Badge className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 p-0 text-xs">
              {favorites.length}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <div className="p-4">
          <h3 className="font-medium">Mes favoris</h3>
          <p className="text-sm text-gray-500">{favorites.length} article(s)</p>
        </div>
        
        <DropdownMenuSeparator />
        
        {favorites.length === 0 ? (
          <div className="p-4 text-center">
            <p className="mb-2 text-gray-500">Vous n'avez pas encore de favoris</p>
            <Link href="/products">
              <Button variant="outline" size="sm" className="mt-2">
                Voir les produits
              </Button>
            </Link>
          </div>
        ) : (
          <>
            <div className="max-h-60 overflow-y-auto">
              {favorites.map((favorite) => (
                <div key={favorite.id} className="flex items-center p-3 hover:bg-gray-50">
                  <div className="relative h-12 w-12 flex-shrink-0 overflow-hidden rounded-md">
                    <Image
                      src={favorite.image_url || "/images/products/placeholder.png"}
                      alt={favorite.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="ml-3 flex-1">
                    <p className="text-sm font-medium line-clamp-1">{favorite.name}</p>
                    <div className="flex items-center justify-between">
                      <p className="text-xs font-medium text-purple-600">
                        {formatPrice(favorite.price)}
                      </p>
                      <div className="flex">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => handleAddToCart(favorite)}
                          title="Ajouter au panier"
                        >
                          <ShoppingCart className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => removeFromFavorites(favorite.id)}
                          title="Retirer des favoris"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <DropdownMenuSeparator />
            
            <div className="p-4">
              <Link href="/favorites">
                <Button className="w-full">
                  Voir tous mes favoris
                </Button>
              </Link>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
