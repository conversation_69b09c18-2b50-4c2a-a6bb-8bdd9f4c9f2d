const fetch = require('node-fetch');

// Remplacez par votre token de bot
const token = '7164687738:AAE8skE__H4GsGdZqElYJa0EByg7o3-0TBA';

// Fonction pour obtenir les mises à jour du bot
async function getUpdates() {
  try {
    const response = await fetch(`https://api.telegram.org/bot${token}/getUpdates`);
    const data = await response.json();
    
    if (data.ok) {
      console.log('Mises à jour reçues:');
      
      if (data.result.length === 0) {
        console.log('Aucune mise à jour. Veuillez envoyer un message à votre bot et réessayer.');
      } else {
        // Afficher toutes les mises à jour avec les chat_id
        data.result.forEach(update => {
          if (update.message) {
            console.log(`Message de: ${update.message.from.first_name} (${update.message.from.username || 'No username'})`);
            console.log(`Chat ID: ${update.message.chat.id}`);
            console.log(`Texte: ${update.message.text}`);
            console.log('---');
          }
        });
      }
    } else {
      console.error('Erreur lors de la récupération des mises à jour:', data.description);
    }
  } catch (error) {
    console.error('Erreur:', error);
  }
}

// Exécuter la fonction
getUpdates();
