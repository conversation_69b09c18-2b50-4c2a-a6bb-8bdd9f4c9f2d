"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import "@/styles/color-swatches.css"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Grid, List, SlidersHorizontal, Search } from "lucide-react"
import ProductCard from "@/components/product-card"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

// Sample product data
const products = [
  {
    id: 1,
    name: "Coque Rigide Transparente",
    price: 19.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.5,
    reviews: 128,
    isNew: true,
    isBestseller: true,
    compatibleModels: ["iPhone 15", "iPhone 14", "iPhone 13"],
    type: "Rigide",
    colors: ["Transparent"],
    collections: ["Basique"],
  },
  {
    id: 2,
    name: "Coque Silicone Premium",
    price: 24.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.8,
    reviews: 256,
    isNew: false,
    isBestseller: true,
    compatibleModels: ["iPhone 15", "iPhone 14", "iPhone 13", "iPhone 12"],
    type: "Silicone",
    colors: ["Noir", "Blanc", "Bleu"],
    collections: ["Premium"],
  },
  {
    id: 3,
    name: "Coque Antichoc Renforcée",
    price: 29.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.7,
    reviews: 189,
    isNew: false,
    isBestseller: false,
    compatibleModels: ["iPhone 15", "iPhone 14", "Samsung S23", "Samsung S22"],
    type: "Antichoc",
    colors: ["Noir", "Rouge"],
    collections: ["Protection"],
  },
  {
    id: 4,
    name: "Coque Biodégradable Écologique",
    price: 27.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.6,
    reviews: 94,
    isNew: true,
    isBestseller: false,
    compatibleModels: ["iPhone 15", "iPhone 14", "iPhone 13", "Google Pixel 7"],
    type: "Écologique",
    colors: ["Beige", "Vert"],
    collections: ["Éco-responsable"],
  },
  {
    id: 5,
    name: "Coque Cuir Véritable",
    price: 39.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.9,
    reviews: 76,
    isNew: false,
    isBestseller: false,
    compatibleModels: ["iPhone 15", "iPhone 14", "Samsung S23"],
    type: "Cuir",
    colors: ["Marron", "Noir"],
    collections: ["Luxe"],
  },
  {
    id: 6,
    name: "Coque Magnétique MagSafe",
    price: 34.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.7,
    reviews: 112,
    isNew: true,
    isBestseller: false,
    compatibleModels: ["iPhone 15", "iPhone 14", "iPhone 13", "iPhone 12"],
    type: "Magnétique",
    colors: ["Noir", "Blanc", "Bleu"],
    collections: ["Technologie"],
  },
  {
    id: 7,
    name: "Coque Paillettes Brillante",
    price: 22.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.4,
    reviews: 203,
    isNew: false,
    isBestseller: true,
    compatibleModels: ["iPhone 15", "iPhone 14", "iPhone 13", "Samsung S23"],
    type: "Décorative",
    colors: ["Rose", "Argent", "Or"],
    collections: ["Tendance"],
  },
  {
    id: 8,
    name: "Coque Batterie Intégrée",
    price: 49.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.6,
    reviews: 87,
    isNew: true,
    isBestseller: false,
    compatibleModels: ["iPhone 14", "iPhone 13", "Samsung S22"],
    type: "Batterie",
    colors: ["Noir"],
    collections: ["Technologie"],
  },
  {
    id: 9,
    name: "Coque Porte-Cartes",
    price: 26.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.5,
    reviews: 156,
    isNew: false,
    isBestseller: false,
    compatibleModels: ["iPhone 15", "iPhone 14", "Samsung S23", "Google Pixel 7"],
    type: "Fonctionnelle",
    colors: ["Noir", "Marron"],
    collections: ["Pratique"],
  },
  {
    id: 10,
    name: "Coque Personnalisable Basique",
    price: 17.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.3,
    reviews: 278,
    isNew: false,
    isBestseller: true,
    compatibleModels: [
      "iPhone 15",
      "iPhone 14",
      "iPhone 13",
      "iPhone 12",
      "Samsung S23",
      "Samsung S22",
      "Google Pixel 7",
    ],
    type: "Personnalisable",
    colors: ["Transparent", "Blanc", "Noir"],
    collections: ["Basique"],
  },
  {
    id: 11,
    name: "Coque Étanche IP68",
    price: 44.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.8,
    reviews: 64,
    isNew: true,
    isBestseller: false,
    compatibleModels: ["iPhone 15", "iPhone 14", "Samsung S23"],
    type: "Étanche",
    colors: ["Noir", "Bleu"],
    collections: ["Protection"],
  },
  {
    id: 12,
    name: "Coque Artiste Édition Limitée",
    price: 32.99,
    image: "/images/placeholder.svg?height=400&width=200",
    rating: 4.7,
    reviews: 92,
    isNew: true,
    isBestseller: false,
    compatibleModels: ["iPhone 15", "iPhone 14", "iPhone 13"],
    type: "Édition Limitée",
    colors: ["Multicolore"],
    collections: ["Art"],
  },
]

// Phone models for filtering
const phoneModels = ["iPhone 15", "iPhone 14", "iPhone 13", "iPhone 12", "Samsung S23", "Samsung S22", "Google Pixel 7"]

// Case types for filtering
const caseTypes = [
  "Rigide",
  "Silicone",
  "Antichoc",
  "Écologique",
  "Cuir",
  "Magnétique",
  "Décorative",
  "Batterie",
  "Fonctionnelle",
  "Personnalisable",
  "Étanche",
  "Édition Limitée",
]

// Colors for filtering
const colors = [
  "Transparent",
  "Noir",
  "Blanc",
  "Bleu",
  "Rouge",
  "Vert",
  "Rose",
  "Marron",
  "Argent",
  "Or",
  "Beige",
  "Multicolore",
]

// Collections for filtering
const collections = [
  "Basique",
  "Premium",
  "Protection",
  "Éco-responsable",
  "Luxe",
  "Technologie",
  "Tendance",
  "Pratique",
  "Art",
]

export default function ShopPage() {
  // State for filters
  const [selectedModel, setSelectedModel] = useState<string>("")
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedColors, setSelectedColors] = useState<string[]>([])
  const [selectedCollections, setSelectedCollections] = useState<string[]>([])
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 50])
  const [sortBy, setSortBy] = useState<string>("popularity")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState<string>("")

  // Toggle selection in array
  const toggleSelection = (array: string[], item: string) => {
    return array.includes(item) ? array.filter((i) => i !== item) : [...array, item]
  }

  // Filter products based on selected filters
  const filteredProducts = products.filter((product) => {
    // Filter by phone model
    if (selectedModel && !product.compatibleModels.includes(selectedModel)) {
      return false
    }

    // Filter by case type
    if (selectedTypes.length > 0 && !selectedTypes.includes(product.type)) {
      return false
    }

    // Filter by color
    if (selectedColors.length > 0 && !product.colors.some((color) => selectedColors.includes(color))) {
      return false
    }

    // Filter by collection
    if (
      selectedCollections.length > 0 &&
      !product.collections.some((collection) => selectedCollections.includes(collection))
    ) {
      return false
    }

    // Filter by price range
    if (product.price < priceRange[0] || product.price > priceRange[1]) {
      return false
    }

    // Filter by search query
    if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }

    return true
  })

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "price-asc":
        return a.price - b.price
      case "price-desc":
        return b.price - a.price
      case "rating":
        return b.rating - a.rating
      case "newest":
        return a.isNew ? -1 : b.isNew ? 1 : 0
      case "popularity":
      default:
        return b.reviews - a.reviews
    }
  })

  // Reset all filters
  const resetFilters = () => {
    setSelectedModel("")
    setSelectedTypes([])
    setSelectedColors([])
    setSelectedCollections([])
    setPriceRange([0, 50])
    setSearchQuery("")
  }

  // Render filters section
  const FiltersSection = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-lg">Filtres</h3>
        <Button variant="ghost" size="sm" onClick={resetFilters}>
          Réinitialiser
        </Button>
      </div>

      <Accordion type="multiple" defaultValue={["model", "type", "price"]}>
        {/* Phone Model Filter */}
        <AccordionItem value="model">
          <AccordionTrigger>Modèle de téléphone</AccordionTrigger>
          <AccordionContent>
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger>
                <SelectValue placeholder="Tous les modèles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les modèles</SelectItem>
                {phoneModels.map((model) => (
                  <SelectItem key={model} value={model}>
                    {model}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </AccordionContent>
        </AccordionItem>

        {/* Case Type Filter */}
        <AccordionItem value="type">
          <AccordionTrigger>Type de coque</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              {caseTypes.map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox
                    id={`type-${type}`}
                    checked={selectedTypes.includes(type)}
                    onCheckedChange={() => setSelectedTypes(toggleSelection(selectedTypes, type))}
                  />
                  <label htmlFor={`type-${type}`} className="text-sm cursor-pointer">
                    {type}
                  </label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Price Range Filter */}
        <AccordionItem value="price">
          <AccordionTrigger>Prix</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4">
              <Slider
                value={priceRange}
                min={0}
                max={50}
                step={1}
                onValueChange={(value) => setPriceRange(value as [number, number])}
              />
              <div className="flex items-center justify-between">
                <span>{priceRange[0]}€</span>
                <span>{priceRange[1]}€</span>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Color Filter */}
        <AccordionItem value="color">
          <AccordionTrigger>Couleur</AccordionTrigger>
          <AccordionContent>
            <div className="grid grid-cols-3 gap-2">
              {colors.map((color) => (
                <div
                  key={color}
                  className={`
                    flex flex-col items-center p-2 rounded-md cursor-pointer border transition-all
                    ${selectedColors.includes(color) ? "border-purple-600 bg-purple-50" : "border-gray-200 hover:border-gray-300"}
                  `}
                  onClick={() => setSelectedColors(toggleSelection(selectedColors, color))}
                >
                  <div
                    className={`color-swatch ${
                      color === "Transparent" 
                        ? "color-swatch-transparent" 
                        : color === "Multicolore" 
                          ? "color-swatch-multicolor" 
                          : `color-swatch-${color.toLowerCase()}`
                    }`}
                  />
                  <span className="text-xs">{color}</span>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Collection Filter */}
        <AccordionItem value="collection">
          <AccordionTrigger>Collection</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              {collections.map((collection) => (
                <div key={collection} className="flex items-center space-x-2">
                  <Checkbox
                    id={`collection-${collection}`}
                    checked={selectedCollections.includes(collection)}
                    onCheckedChange={() => setSelectedCollections(toggleSelection(selectedCollections, collection))}
                  />
                  <label htmlFor={`collection-${collection}`} className="text-sm cursor-pointer">
                    {collection}
                  </label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  )

  // Limiter à 12 produits maximum
  const limitedProducts = sortedProducts.slice(0, 12)

  return (
    <main className="container mx-auto px-4 py-8">
      {/* Shop Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-lg p-8 mb-8">
        {/* Mettre à jour le titre de la page boutique */}
        <h1 className="text-3xl font-bold mb-2">Boutique</h1>
        <p className="text-lg opacity-90">Découvrez notre sélection de coques pour tous les modèles de téléphone</p>
      </div>

      {/* Barre de catégories de produits */}
      <div className="flex overflow-x-auto gap-2 p-2 mb-6 border rounded-lg bg-gray-50">
        <Button variant="ghost" className="flex items-center gap-2 whitespace-nowrap">
          <SlidersHorizontal className="h-4 w-4" />
          Tous les produits
        </Button>
        <Separator orientation="vertical" />
        <Button variant="ghost" className="flex items-center gap-2 whitespace-nowrap">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M17 8h1a4 4 0 1 1 0 8h-1"></path><path d="M3 8h14v9a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4Z"></path><line x1="6" x2="6" y1="2" y2="4"></line><line x1="10" x2="10" y1="2" y2="4"></line><line x1="14" x2="14" y1="2" y2="4"></line></svg>
          Tasses
        </Button>
        <Button variant="ghost" className="flex items-center gap-2 whitespace-nowrap">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z"></path></svg>
          T-shirts
        </Button>
        <Button variant="ghost" className="flex items-center gap-2 whitespace-nowrap">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><rect width="18" height="12" x="3" y="6" rx="2"></rect><path d="M14 2H10"></path><path d="M3 10h18"></path><path d="M4 14h16"></path><path d="M4 18h16"></path></svg>
          Tapis de souris
        </Button>
        <Button variant="ghost" className="flex items-center gap-2 whitespace-nowrap">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M21 7v6h-6"></path><path d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7"></path></svg>
          Coussins
        </Button>
        <Button variant="ghost" className="flex items-center gap-2 whitespace-nowrap">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M15 11h.01"></path><path d="M11 15h.01"></path><path d="M16 16h.01"></path><path d="m2 16 20 6-6-20A20 20 0 0 0 2 16"></path><path d="M5.71 17.11a17.04 17.04 0 0 1 11.4-11.4"></path></svg>
          Porte-clés
        </Button>
      </div>

      {/* Shop Content */}
      <div className="flex flex-col gap-8">
        {/* Products */}
        <div className="flex-1">
          {/* Search and Sort Bar */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Input
                placeholder="Rechercher..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            </div>

            <div className="flex items-center gap-2">

              {/* Sort Dropdown */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Trier par" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popularity">Popularité</SelectItem>
                  <SelectItem value="price-asc">Prix croissant</SelectItem>
                  <SelectItem value="price-desc">Prix décroissant</SelectItem>
                  <SelectItem value="rating">Meilleures notes</SelectItem>
                  <SelectItem value="newest">Nouveautés</SelectItem>
                </SelectContent>
              </Select>

              {/* View Mode Buttons */}
              <div className="flex border rounded-md">
                <Button
                  variant="ghost"
                  size="icon"
                  className={`rounded-r-none ${viewMode === "grid" ? "bg-gray-100" : ""}`}
                  onClick={() => setViewMode("grid")}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Separator orientation="vertical" />
                <Button
                  variant="ghost"
                  size="icon"
                  className={`rounded-l-none ${viewMode === "list" ? "bg-gray-100" : ""}`}
                  onClick={() => setViewMode("list")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Search Query Badge */}
          {searchQuery && (
            <div className="flex flex-wrap gap-2 mb-6">
              <Badge variant="secondary" className="flex items-center gap-1">
                "{searchQuery}"
                <button onClick={() => setSearchQuery("")} className="ml-1">
                  ×
                </button>
              </Badge>
            </div>
          )}

          {/* Results Count */}
          <div className="mb-6">
            <p className="text-gray-500">
              {limitedProducts.length} produit{limitedProducts.length !== 1 ? "s" : ""} trouvé
              {limitedProducts.length !== 1 ? "s" : ""}
            </p>
          </div>

          {/* Products Grid */}
          {limitedProducts.length > 0 ? (
            <div className={viewMode === "grid" ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
              {limitedProducts.map((product) => (
                <ProductCard key={product.id} product={product} viewMode={viewMode} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-medium mb-2">Aucun produit trouvé</h3>
              <p className="text-gray-500 mb-6">Essayez de modifier votre recherche pour voir des résultats.</p>
              <Button onClick={() => setSearchQuery("")}>Réinitialiser la recherche</Button>
            </div>
          )}

          {/* Résultats */}
          <div className="flex justify-center mt-12">
            <p className="text-gray-500">Affichage de {limitedProducts.length} produits</p>
          </div>
        </div>
      </div>
    </main>
  )
}
