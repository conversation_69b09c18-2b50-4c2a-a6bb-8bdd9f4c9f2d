const fs = require('fs');
const path = require('path');

// Fonction pour lire un fichier
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier ${filePath}:`, error);
    return null;
  }
}

// Fonction pour écrire dans un fichier
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'écriture dans le fichier ${filePath}:`, error);
    return false;
  }
}

// Fonction pour vérifier si un fichier existe
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Fonction pour corriger les références d'images dans un fichier
function fixImageReferencesInFile(filePath) {
  console.log(`Vérification des références d'images dans ${filePath}...`);
  
  const content = readFile(filePath);
  if (!content) return false;
  
  // Recherche des références d'images
  const imageRegex = /(?:src|image|imageUrl|thumbnail):\s*["']([^"']*\.(?:png|jpg|jpeg|svg|gif|webp))["']/g;
  let match;
  let modified = false;
  let newContent = content;
  
  const replacements = [];
  
  while ((match = imageRegex.exec(content)) !== null) {
    const fullMatch = match[0];
    const imagePath = match[1];
    
    // Ignorer les URLs externes
    if (imagePath.startsWith('http')) continue;
    
    // Ignorer les placeholders
    if (imagePath.includes('placeholder')) continue;
    
    // Vérifier si l'image existe
    const absoluteImagePath = path.join(process.cwd(), 'public', imagePath.startsWith('/') ? imagePath.substring(1) : imagePath);
    
    if (!fileExists(absoluteImagePath)) {
      console.log(`Image manquante: ${imagePath}`);
      
      // Déterminer le type d'image et proposer une alternative
      let alternativePath = '';
      
      if (imagePath.includes('/promos/')) {
        // Images de promotion
        if (imagePath.includes('/seasonal/')) {
          alternativePath = '/images/promos/seasonal/seasonal.png';
        } else if (imagePath.includes('/bundle/')) {
          alternativePath = '/images/promos/bundle/bundle.png';
        } else if (imagePath.includes('/new/')) {
          alternativePath = '/images/promos/new/new.png';
        } else {
          alternativePath = '/images/promos/banner1.png';
        }
      } else if (imagePath.includes('/gallery/')) {
        // Images de galerie
        alternativePath = '/images/gallery/gallery-placeholder.svg';
      } else if (imagePath.includes('/banners/')) {
        // Bannières
        alternativePath = '/images/banners/banner-placeholder.svg';
      } else {
        // Autres images
        alternativePath = '/placeholder.svg';
      }
      
      // Remplacer la référence
      const newReference = fullMatch.replace(imagePath, alternativePath);
      replacements.push({ from: fullMatch, to: newReference });
      
      modified = true;
    }
  }
  
  // Appliquer les remplacements
  for (const replacement of replacements) {
    newContent = newContent.replace(replacement.from, replacement.to);
  }
  
  // Enregistrer les modifications
  if (modified) {
    console.log(`Correction de ${replacements.length} références d'images dans ${filePath}`);
    return writeFile(filePath, newContent);
  }
  
  return false;
}

// Fonction pour parcourir récursivement un répertoire
function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    isDirectory ? walkDir(dirPath, callback) : callback(path.join(dir, f));
  });
}

// Fonction principale
function main() {
  console.log("Début de la vérification et correction des références d'images...");
  
  let totalFixed = 0;
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire app
  walkDir('app', (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const fixed = fixImageReferencesInFile(filePath);
      if (fixed) totalFixed++;
    }
  });
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire components
  walkDir('components', (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const fixed = fixImageReferencesInFile(filePath);
      if (fixed) totalFixed++;
    }
  });
  
  console.log(`Vérification et correction des références d'images terminées. ${totalFixed} fichiers modifiés.`);
}

// Exécuter la fonction principale
main();
