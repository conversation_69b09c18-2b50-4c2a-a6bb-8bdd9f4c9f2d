"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Phone, LogIn, AlertCircle, User } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { supabase } from "@/lib/supabase"

interface SimpleLoginProps {
  onSuccess: () => void
  onCancel: () => void
}

export default function SimpleLogin({ onSuccess, onCancel }: SimpleLoginProps) {
  const [formData, setFormData] = useState({
    fullName: "",
    whatsapp: ""
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setIsLoading(true)

    try {
      // Validation de base
      if (!formData.fullName.trim() || !formData.whatsapp.trim()) {
        setError("Veuillez remplir tous les champs")
        return
      }

      // Validation du numéro WhatsApp
      const whatsappRegex = /^\+?[0-9]{8,15}$/
      if (!whatsappRegex.test(formData.whatsapp.replace(/\s/g, ''))) {
        setError("Veuillez entrer un numéro WhatsApp valide")
        return
      }

      // Vérifier si l'utilisateur existe déjà
      const { data: existingUsers, error: searchError } = await supabase
        .from('customers')
        .select('id')
        .eq('whatsapp', formData.whatsapp.replace(/\s/g, ''))
        .limit(1)

      if (searchError) {
        console.error("Erreur lors de la recherche de l'utilisateur:", searchError)
        throw new Error("Erreur lors de la vérification du numéro WhatsApp")
      }

      let userId

      if (existingUsers && existingUsers.length > 0) {
        // Utilisateur existant - mettre à jour le nom si nécessaire
        userId = existingUsers[0].id
        
        const { error: updateError } = await supabase
          .from('customers')
          .update({ full_name: formData.fullName })
          .eq('id', userId)

        if (updateError) {
          console.error("Erreur lors de la mise à jour du nom:", updateError)
          throw new Error("Erreur lors de la mise à jour de vos informations")
        }
      } else {
        // Nouvel utilisateur - créer un enregistrement
        const { data: newUser, error: insertError } = await supabase
          .from('customers')
          .insert([
            { 
              full_name: formData.fullName, 
              whatsapp: formData.whatsapp.replace(/\s/g, '')
            }
          ])
          .select('id')
          .single()

        if (insertError) {
          console.error("Erreur lors de la création de l'utilisateur:", insertError)
          throw new Error("Erreur lors de l'enregistrement de vos informations")
        }

        userId = newUser.id
      }

      // Stocker l'ID utilisateur dans le localStorage
      localStorage.setItem('customer_id', userId)
      localStorage.setItem('customer_name', formData.fullName)
      localStorage.setItem('customer_whatsapp', formData.whatsapp.replace(/\s/g, ''))

      toast({
        title: "Connexion réussie",
        description: "Vous pouvez maintenant procéder au paiement",
      })

      // Appeler le callback de succès
      onSuccess()
    } catch (err: any) {
      console.error("Erreur lors de la connexion:", err)
      setError(err.message || "Une erreur s'est produite lors de la connexion")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">Vos coordonnées</CardTitle>
        <CardDescription className="text-center">
          Entrez votre nom et votre numéro WhatsApp pour continuer
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="fullName">Nom complet</Label>
            <div className="relative">
              <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="fullName"
                placeholder="Votre nom complet"
                className="pl-10"
                value={formData.fullName}
                onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                required
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="whatsapp">Numéro WhatsApp</Label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="whatsapp"
                placeholder="+225 XX XX XX XX XX"
                className="pl-10"
                value={formData.whatsapp}
                onChange={(e) => setFormData({ ...formData, whatsapp: e.target.value })}
                required
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="flex gap-2 pt-2">
            <Button
              type="button"
              variant="outline"
              className="flex-1"
              onClick={onCancel}
              disabled={isLoading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-purple-600 hover:bg-purple-700"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Connexion...
                </span>
              ) : (
                <span className="flex items-center">
                  <LogIn className="mr-2 h-4 w-4" /> Continuer
                </span>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
