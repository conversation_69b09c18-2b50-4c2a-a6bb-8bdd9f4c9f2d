import { Metadata } from "next";
import { AdminLayout } from "@/components/admin/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, FileText, Truck, X, Eye, Share2 } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Gestion des commandes | Admin HCP-DESIGN CI",
  description: "Gérez les commandes des clients",
};

export default function OrdersPage() {
  // Données fictives pour la démo
  const orders = [
    { 
      id: "ORD-2345", 
      customer: "<PERSON>", 
      date: "15/05/2023", 
      products: "Coque iPhone 15 Pro - Design Floral", 
      total: "49,90 €", 
      status: "En attente",
      statusColor: "bg-yellow-500"
    },
    { 
      id: "ORD-2344", 
      customer: "Marie Martin", 
      date: "14/05/2023", 
      products: "Coque Samsung S23 - Logo Personnalisé", 
      total: "29,95 €", 
      status: "Payée",
      statusColor: "bg-blue-500"
    },
    { 
      id: "ORD-2343", 
      customer: "Paul Durand", 
      date: "13/05/2023", 
      products: "Coque iPhone 14 - Photo Famille, Coque Pixel 7 - Design Abstrait", 
      total: "79,85 €", 
      status: "Expédiée",
      statusColor: "bg-purple-500"
    },
    { 
      id: "ORD-2342", 
      customer: "Sophie Petit", 
      date: "12/05/2023", 
      products: "Coque iPhone 13 - Design Personnalisé", 
      total: "39,90 €", 
      status: "Livrée",
      statusColor: "bg-green-500"
    },
    { 
      id: "ORD-2341", 
      customer: "Thomas Leroy", 
      date: "11/05/2023", 
      products: "Coque Samsung S22 - Photo Paysage", 
      total: "34,95 €", 
      status: "Annulée",
      statusColor: "bg-red-500"
    }
  ];

  return (
    <AdminLayout>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Gestion des commandes</h1>
        <Link href="/admin/orders/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Nouvelle commande
          </Button>
        </Link>
      </div>
      
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="grid gap-2 flex-1">
              <Input 
                placeholder="Rechercher une commande..." 
                className="w-full"
                icon={<Search className="h-4 w-4" />}
              />
            </div>
            <div className="grid gap-2 w-full md:w-[180px]">
              <Select defaultValue="all">
                <SelectTrigger>
                  <SelectValue placeholder="Statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="pending">En attente</SelectItem>
                  <SelectItem value="paid">Payée</SelectItem>
                  <SelectItem value="shipped">Expédiée</SelectItem>
                  <SelectItem value="delivered">Livrée</SelectItem>
                  <SelectItem value="cancelled">Annulée</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2 w-full md:w-[180px]">
              <Select defaultValue="recent">
                <SelectTrigger>
                  <SelectValue placeholder="Trier par" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Plus récentes</SelectItem>
                  <SelectItem value="oldest">Plus anciennes</SelectItem>
                  <SelectItem value="highest">Montant (décroissant)</SelectItem>
                  <SelectItem value="lowest">Montant (croissant)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Liste des commandes</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Produit(s)</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.id}</TableCell>
                  <TableCell>{order.customer}</TableCell>
                  <TableCell>{order.date}</TableCell>
                  <TableCell className="max-w-[200px] truncate">{order.products}</TableCell>
                  <TableCell>{order.total}</TableCell>
                  <TableCell>
                    <Badge className={order.statusColor}>{order.status}</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon" title="Voir les détails">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" title="Marquer comme livré">
                        <Truck className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" title="Imprimer facture">
                        <FileText className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" title="Partager via WhatsApp">
                        <Share2 className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" title="Annuler" className="text-destructive">
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </AdminLayout>
  );
}

