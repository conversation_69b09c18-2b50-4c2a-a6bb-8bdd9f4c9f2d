# Tables manquantes dans Supabase

Ce document explique comment ajouter les tables manquantes à votre projet Supabase pour HCP-DESIGN CI.

## Tables manquantes identifiées

Nous avons identifié les tables suivantes comme manquantes dans votre projet Supabase :

1. `site_settings` - Paramètres du site (navigation, couleurs, etc.)
2. `verification_codes` - Codes de vérification pour l'authentification
3. `gallery_variants` - Variantes de designs pour la galerie
4. `payment_methods` - Méthodes de paiement (Wave, Orange Money, etc.)

## Comment ajouter les tables manquantes

### Option 1 : Via l'interface Supabase (recommandée)

1. Connectez-vous à votre projet Supabase à l'adresse [https://app.supabase.com/](https://app.supabase.com/)
2. Allez dans l'onglet "SQL Editor"
3. Cliquez sur "New Query"
4. Copiez-collez le contenu du fichier `scripts/add-missing-tables.sql` dans l'éditeur
5. Cliquez sur "Run" pour exécuter le script

### Option 2 : Via l'API Supabase (nécessite une clé de service)

Si vous avez une clé de service Supabase, vous pouvez exécuter le script via l'API :

1. Créez un fichier `.env.local` à la racine du projet avec les variables suivantes :
   ```
   NEXT_PUBLIC_SUPABASE_URL=votre-url-supabase
   NEXT_PUBLIC_SUPABASE_ANON_KEY=votre-clé-anon-supabase
   SUPABASE_SERVICE_ROLE_KEY=votre-clé-de-service-supabase
   ```

2. Exécutez le script suivant :
   ```bash
   node scripts/apply-migrations.js
   ```

## Contenu des tables

### Table `site_settings`

Cette table stocke les paramètres du site, tels que :
- Informations générales du site (nom, description, couleurs)
- Informations de contact
- Liens des réseaux sociaux
- Configuration de la navigation
- Configuration de la bannière d'accueil

### Table `verification_codes`

Cette table stocke les codes de vérification pour l'authentification à deux facteurs :
- Email de l'utilisateur
- Code de vérification
- Date d'expiration
- Statut d'utilisation

### Table `gallery_variants`

Cette table stocke les variantes de designs pour la galerie :
- Vagues Abstraites
- Fleurs Tropicales
- Galaxie Cosmique
- Marbre Élégant
- Rétro Synthwave
- Montagnes Minimalistes
- Motif Géométrique
- Néon Urbain
- Mandala Zen
- Animaux Polygonaux
- Typographie Créative

### Table `payment_methods`

Cette table stocke les méthodes de paiement disponibles :
- Wave
- Orange Money

## Vérification

Après avoir exécuté le script, vous pouvez vérifier que les tables ont été créées en exécutant le script suivant :

```bash
node scripts/check-supabase-tables.js
```

Ce script vérifiera si toutes les tables nécessaires existent dans votre projet Supabase.
