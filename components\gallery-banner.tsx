"use client"

import React from "react"
import { Button } from "@/components/ui/button"
import { ArrowRight, Sparkles } from "lucide-react"

interface GalleryBannerProps {
  title?: string
  subtitle?: string
  description?: string
  showCTA?: boolean
  ctaText?: string
  onCTAClick?: () => void
  backgroundImage?: string
  backgroundVideo?: string
  className?: string
}

export default function GalleryBanner({
  title = "Galerie de Designs",
  subtitle = "Découvrez notre collection complète de designs pour coques de téléphone.",
  description = "Explorez les produits Gospel et les designs généraux pour trouver celui qui vous correspond.",
  showCTA = true,
  ctaText = "Explorer maintenant",
  onCTAClick,
  backgroundImage = "/images/banners/gallery/gallery-banner.png",
  backgroundVideo,
  className = ""
}: GalleryBannerProps) {
  const handleCTAClick = () => {
    if (onCTAClick) {
      onCTAClick()
    } else {
      // Scroll to gallery content
      const galleryContent = document.getElementById('gallery-content')
      if (galleryContent) {
        galleryContent.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }

  return (
    <div className={`relative w-full h-[400px] overflow-hidden rounded-lg mb-8 ${className}`}>
      {/* Background Media */}
      {backgroundVideo ? (
        <video
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
        >
          <source src={backgroundVideo} type="video/mp4" />
          {/* Fallback to image if video fails */}
          <img
            src={backgroundImage}
            alt="Gallery Banner"
            className="w-full h-full object-cover"
          />
        </video>
      ) : (
        <img
          src={backgroundImage}
          alt="Gallery Banner"
          className="absolute inset-0 w-full h-full object-cover"
        />
      )}
      
      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-black/20" />
      
      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl">
            {/* Title */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 drop-shadow-lg">
              {title}
            </h1>
            
            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-white/90 mb-4 drop-shadow-md">
              {subtitle}
            </p>
            
            {/* Description */}
            <p className="text-lg md:text-xl text-white/80 mb-8 max-w-2xl drop-shadow-md">
              {description}
            </p>
            
            {/* Call to Action */}
            {showCTA && (
              <Button
                onClick={handleCTAClick}
                size="lg"
                className="bg-white/20 hover:bg-white/30 text-white border-2 border-white/50 hover:border-white/70 backdrop-blur-sm transition-all duration-300 group"
              >
                <Sparkles className="mr-2 h-5 w-5" />
                {ctaText}
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            )}
          </div>
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="absolute top-4 right-4 text-white/30">
        <Sparkles className="h-8 w-8" />
      </div>
      <div className="absolute bottom-4 left-4 text-white/20">
        <Sparkles className="h-6 w-6" />
      </div>
    </div>
  )
}