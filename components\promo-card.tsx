"use client"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, Tag } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

interface PromoCardProps {
  id: string
  title: string
  description: string
  imageUrl: string
  discount: number
  startDate: string
  endDate: string
  code: string
  category: string
  transparent?: boolean
}

export default function PromoCard({
  id,
  title,
  description,
  imageUrl,
  discount,
  startDate,
  endDate,
  code,
  category,
  transparent = false
}: PromoCardProps) {
  // Formater les dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR')
  }

  return (
    <div className={`relative overflow-hidden rounded-xl hover:shadow-lg transition-all duration-300 ${transparent ? 'bg-transparent hover:scale-[1.02]' : 'bg-white'}`}>
      <div className={`relative h-48 ${transparent ? 'border border-purple-300 border-b-0 rounded-t-xl' : ''}`}>
        <Image
          src={imageUrl}
          alt={title}
          fill
          style={{ objectFit: "cover" }}
          className="rounded-t-xl"
        />
        <div className="absolute top-4 right-4">
          <Badge className="bg-purple-600 text-lg px-3 py-1">-{discount}%</Badge>
        </div>
      </div>
      <div className={`p-6 ${transparent ? 'border border-purple-300 border-t-0 rounded-b-xl backdrop-blur-lg bg-white/5' : ''}`}>
        <h3 className="text-xl font-bold mb-2 text-purple-900">{title}</h3>
        <p className={`mb-4 ${transparent ? 'text-purple-800' : 'text-gray-600'}`}>{description}</p>
        <div className={`flex items-center text-sm mb-2 ${transparent ? 'text-purple-800' : 'text-gray-500'}`}>
          <Calendar className="h-4 w-4 mr-2" />
          <span>Valable du {formatDate(startDate)} au {formatDate(endDate)}</span>
        </div>
        <div className={`flex items-center text-sm mb-4 ${transparent ? 'text-purple-800' : 'text-gray-500'}`}>
          <Tag className="h-4 w-4 mr-2" />
          <span>Code: <strong>{code}</strong></span>
        </div>
        <Button className="w-full bg-purple-600 hover:bg-purple-700">
          Ajouter au panier
        </Button>
      </div>
    </div>
  )
}
