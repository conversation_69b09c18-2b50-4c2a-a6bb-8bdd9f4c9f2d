# Guide de gestion des images pour HCP-DESIGN CI

Ce document explique comment gérer les images sur le site HCP-DESIGN CI, notamment comment mettre à jour les images et s'assurer qu'elles s'affichent correctement après un déploiement.

## Structure des dossiers d'images

Toutes les images du site sont organisées dans le dossier `public/images` selon la structure suivante :

```
public/images/
├── account/
│   ├── profile/
│   ├── orders/
│   ├── designs/
│   └── favorites/
├── banners/
│   ├── accueil/
│   ├── promotions/
│   ├── categories/
│   └── ...
├── designs/
│   ├── abstraits/
│   ├── animaux/
│   └── ...
├── phone-cases/
│   ├── iphone/
│   ├── samsung/
│   ├── google/
│   └── types/
├── products/
│   ├── coques/
│   ├── accessoires/
│   └── ...
├── promos/
│   ├── seasonal/
│   ├── bundle/
│   └── new/
└── ...
```

## Convention de nommage des images

Chaque image doit porter le nom du dossier dans lequel elle se trouve. Par exemple :
- `/images/designs/abstraits/abstraits.png`
- `/images/banners/accueil/accueil.png`
- `/images/phone-cases/iphone/iphone-15-pro-max/iphone-15-pro-max.png`

## Mise à jour des images

### Méthode 1 : Via GitHub Desktop

1. Clonez le dépôt sur votre ordinateur si ce n'est pas déjà fait
2. Remplacez l'image dans le dossier approprié
3. Committez vos changements avec un message descriptif (ex: "Mise à jour de l'image de la bannière d'accueil")
4. Poussez vos changements vers GitHub

### Méthode 2 : Via l'interface GitHub

1. Accédez au dépôt sur GitHub
2. Naviguez jusqu'au dossier contenant l'image à remplacer
3. Cliquez sur "Add file" > "Upload files"
4. Sélectionnez l'image à télécharger (assurez-vous qu'elle a le même nom que l'image existante)
5. Committez vos changements

## Problèmes courants et solutions

### Les images ne s'affichent pas après un déploiement

Si vous avez remplacé une image mais qu'elle ne s'affiche pas sur le site après un déploiement, cela peut être dû à plusieurs raisons :

1. **Cache du navigateur** : Forcez le rafraîchissement de la page en utilisant Ctrl+F5 (ou Cmd+Shift+R sur Mac)

2. **Cache du CDN** : Le site utilise un système de versionnement des images pour contourner le cache du CDN. Chaque déploiement génère un nouveau paramètre de version pour toutes les images.

3. **Problème de déploiement** : Vérifiez que le déploiement s'est bien terminé sans erreur dans l'interface Vercel.

### Vider manuellement le cache des images

Si les images ne s'affichent toujours pas après un déploiement, vous pouvez vider manuellement le cache des images :

1. Installez les dépendances nécessaires :
   ```
   npm install node-fetch dotenv
   ```

2. Créez un fichier `.env` à la racine du projet avec les informations suivantes :
   ```
   VERCEL_TOKEN=votre_token_vercel
   VERCEL_TEAM_ID=votre_team_id
   VERCEL_PROJECT_ID=votre_project_id
   ```

3. Exécutez le script de purge du cache :
   ```
   node scripts/purge-image-cache.js
   ```

## Bonnes pratiques

1. **Optimisez vos images** avant de les télécharger pour réduire leur taille et améliorer les performances du site

2. **Respectez les dimensions recommandées** pour chaque type d'image :
   - Bannières : 1200 x 400 pixels
   - Coques de téléphone : 300 x 600 pixels
   - Produits : 500 x 500 pixels
   - Témoignages : 200 x 200 pixels (format carré)
   - Icônes : 64 x 64 pixels

3. **Utilisez le format PNG** pour toutes les images du site pour une meilleure compatibilité avec le back office

4. **Respectez la convention de nommage** où chaque image porte le nom du dossier dans lequel elle se trouve

## Utilisation du composant VersionedImage

Pour afficher des images dans vos composants React, utilisez le composant `VersionedImage` qui ajoute automatiquement un paramètre de version à l'URL de l'image :

```jsx
import VersionedImage from "@/components/versioned-image";

export default function MyComponent() {
  return (
    <VersionedImage
      src="/images/banners/accueil/accueil.png"
      alt="Bannière d'accueil"
      width={1200}
      height={400}
    />
  );
}
```

## Utilisation des fonctions utilitaires

Pour obtenir le chemin d'une image avec un paramètre de version, utilisez les fonctions utilitaires du fichier `lib/image-paths.ts` :

```jsx
import { getBannerImagePath } from "@/lib/image-paths";
import Image from "next/image";

export default function MyComponent() {
  // Le chemin retourné inclut déjà un paramètre de version
  const bannerPath = getBannerImagePath("accueil");
  
  return (
    <Image
      src={bannerPath}
      alt="Bannière d'accueil"
      width={1200}
      height={400}
    />
  );
}
```
