import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    // Tester la connexion à Supabase
    const { data, error } = await supabase.from('products').select('count').single();
    
    if (error) {
      return NextResponse.json({ 
        success: false, 
        message: 'Erreur de connexion à Supabase', 
        error: error.message 
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Connexion à Supabase réussie',
      data
    });
  } catch (error: any) {
    return NextResponse.json({ 
      success: false, 
      message: 'Erreur inattendue', 
      error: error.message 
    }, { status: 500 });
  }
}
