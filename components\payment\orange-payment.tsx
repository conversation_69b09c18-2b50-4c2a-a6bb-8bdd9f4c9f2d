"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Upload, Phone } from "lucide-react"

interface OrangePaymentProps {
  amount: number
  onPaymentComplete: (message: string, screenshot: File | null) => void
}

export default function OrangePayment({ amount, onPaymentComplete }: OrangePaymentProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [message, setMessage] = useState("")
  const [screenshot, setScreenshot] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  
  // Formater le montant pour l'affichage
  const formattedAmount = new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'XOF'
  }).format(amount)
  
  // Numéro Orange Money
  const orangeMoneyNumber = "07 09 49 58 48"
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    setScreenshot(file)
    
    if (file) {
      const reader = new FileReader()
      reader.onload = () => {
        setPreviewUrl(reader.result as string)
      }
      reader.readAsDataURL(file)
    } else {
      setPreviewUrl(null)
    }
  }
  
  const handleSubmit = () => {
    onPaymentComplete(message, screenshot)
  }
  
  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Image 
            src="/images/payments/orange/logo/orange-money-logo.png" 
            alt="Orange Money" 
            width={24} 
            height={24} 
          />
          Payer avec Orange Money
        </CardTitle>
        <CardDescription>
          Scannez le QR code ou utilisez le numéro de téléphone
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center">
          <div className="bg-white p-4 rounded-lg mb-4">
            <Image 
              src="/images/payments/orange/qr/orange-qr.png" 
              alt="QR Code Orange Money" 
              width={200} 
              height={200} 
              className="mx-auto"
            />
          </div>
          <p className="text-center mb-4">
            Montant à payer: <strong>{formattedAmount}</strong>
          </p>
          <div className="flex items-center gap-2 bg-gray-100 p-2 rounded-md w-full justify-center mb-4">
            <Phone size={16} />
            <span className="font-medium">{orangeMoneyNumber}</span>
          </div>
          
          <div className="w-full space-y-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="payment-message">Message (référence de paiement)</Label>
              <Textarea 
                id="payment-message" 
                placeholder="Entrez un message ou une référence pour votre paiement"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="payment-screenshot">Capture d'écran du paiement</Label>
              <div className="flex items-center gap-2">
                <Button 
                  variant="outline" 
                  onClick={() => document.getElementById("payment-screenshot")?.click()}
                  className="flex items-center gap-2"
                >
                  <Upload size={16} /> Télécharger une capture
                </Button>
                <Input 
                  id="payment-screenshot" 
                  type="file" 
                  accept="image/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
              </div>
              
              {previewUrl && (
                <div className="mt-2 border rounded-md p-2">
                  <Image 
                    src={previewUrl} 
                    alt="Aperçu de la capture d'écran" 
                    width={200} 
                    height={200} 
                    className="mx-auto object-contain"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col items-start pt-0">
        <div className="w-full flex flex-col gap-4">
          <Button 
            variant="link" 
            className="px-0 text-sm self-start"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? "Masquer les instructions" : "Afficher les instructions"}
          </Button>
          
          {isExpanded && (
            <div className="text-sm space-y-2">
              <p><strong>Comment payer avec Orange Money:</strong></p>
              <ol className="list-decimal pl-5 space-y-1">
                <li>Scannez le QR code avec l'application Orange Money</li>
                <li>Ou composez *144# et suivez les instructions</li>
                <li>Envoyez {formattedAmount} au numéro {orangeMoneyNumber}</li>
                <li>Prenez une capture d'écran de la confirmation</li>
                <li>Téléchargez la capture et ajoutez un message</li>
              </ol>
            </div>
          )}
          
          <Button 
            className="w-full mt-4"
            onClick={handleSubmit}
            disabled={!message || !screenshot}
          >
            J'ai payé avec Orange Money
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
