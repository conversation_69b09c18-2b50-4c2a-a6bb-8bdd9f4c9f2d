"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { supabaseApi, GalleryVariant } from "@/lib/supabase";
import { useToast } from "@/components/ui/use-toast";

// Valeurs par défaut pour les variantes de la galerie
const defaultVariants: GalleryVariant[] = [
  {
    id: "1",
    name: "Vagues Abstraites",
    slug: "vagues-abstraites",
    description: "Designs avec motifs de vagues abstraites",
    image_url: "/images/gallery/variants/vagues-abstraites/vagues-abstraites.png",
    is_active: true,
    display_order: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "2",
    name: "Fleurs Tropicales",
    slug: "fleurs-tropicales",
    description: "Designs avec motifs de fleurs tropicales",
    image_url: "/images/gallery/variants/fleurs-tropicales/fleurs-tropicales.png",
    is_active: true,
    display_order: 2,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "3",
    name: "Galaxie Cosmique",
    slug: "galaxie-cosmique",
    description: "Designs avec motifs de galaxie et d'espace",
    image_url: "/images/gallery/variants/galaxie-cosmique/galaxie-cosmique.png",
    is_active: true,
    display_order: 3,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

// Type pour le contexte
type GalleryVariantsContextType = {
  variants: GalleryVariant[];
  activeVariants: GalleryVariant[];
  isLoading: boolean;
  error: Error | null;
  refreshVariants: () => Promise<void>;
  getVariantBySlug: (slug: string) => GalleryVariant | undefined;
};

// Créer le contexte
const GalleryVariantsContext = createContext<GalleryVariantsContextType | undefined>(undefined);

// Hook pour utiliser le contexte
export function useGalleryVariants() {
  const context = useContext(GalleryVariantsContext);
  if (context === undefined) {
    throw new Error("useGalleryVariants must be used within a GalleryVariantsProvider");
  }
  return context;
}

// Provider pour le contexte
export function GalleryVariantsProvider({ children }: { children: React.ReactNode }) {
  const [variants, setVariants] = useState<GalleryVariant[]>([]);
  const [activeVariants, setActiveVariants] = useState<GalleryVariant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  // Fonction pour charger les variantes de la galerie
  const loadVariants = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await supabaseApi.galleryVariants.getAll();

      if (error) {
        throw new Error(`Erreur lors du chargement des variantes: ${error.message}`);
      }

      if (!data || data.length === 0) {
        console.warn("Aucune variante trouvée, utilisation des valeurs par défaut");
        setVariants(defaultVariants);
        setActiveVariants(defaultVariants.filter(v => v.is_active));
        return;
      }

      setVariants(data);
      setActiveVariants(data.filter(v => v.is_active));
    } catch (err) {
      console.error("Erreur lors du chargement des variantes:", err);
      setError(err instanceof Error ? err : new Error(String(err)));
      toast({
        title: "Erreur",
        description: "Impossible de charger les variantes de la galerie",
        variant: "destructive",
      });
      
      // Utiliser les valeurs par défaut en cas d'erreur
      setVariants(defaultVariants);
      setActiveVariants(defaultVariants.filter(v => v.is_active));
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour obtenir une variante par son slug
  const getVariantBySlug = (slug: string): GalleryVariant | undefined => {
    return variants.find(v => v.slug === slug);
  };

  // Charger les variantes au montage du composant
  useEffect(() => {
    loadVariants();
  }, []);

  return (
    <GalleryVariantsContext.Provider
      value={{
        variants,
        activeVariants,
        isLoading,
        error,
        refreshVariants: loadVariants,
        getVariantBySlug,
      }}
    >
      {children}
    </GalleryVariantsContext.Provider>
  );
}
