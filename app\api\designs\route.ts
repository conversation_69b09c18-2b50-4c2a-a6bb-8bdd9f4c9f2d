import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET - Récupérer tous les designs
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const limit = searchParams.get('limit');

    let query = supabase
      .from('designs')
      .select('*')
      .order('utilisations', { ascending: false });

    if (category && category !== 'all') {
      query = query.eq('theme', category);
    }

    if (search) {
      query = query.or(`titre.ilike.%${search}%,tags.ilike.%${search}%`);
    }

    if (limit) {
      query = query.limit(parseInt(limit));
    }

    const { data, error } = await query;

    if (error) {
      console.error('Erreur lors de la récupération des designs:', error);
      return NextResponse.json(
        { error: 'Erreur lors de la récupération des designs' },
        { status: 500 }
      );
    }

    // Transformer les données pour correspondre au format attendu par la galerie
    const transformedData = data?.map((design) => ({
      id: design.id,
      title: design.titre,
      image: design.fichier,
      thumbnail: design.fichier,
      category: design.theme,
      tags: design.tags ? design.tags.split(',').map((tag: string) => tag.trim()) : [],
      likes: Math.floor(Math.random() * 500) + 50, // Valeur temporaire
      views: design.utilisations || 0,
      comments: Math.floor(Math.random() * 50), // Valeur temporaire
      featured: Math.random() > 0.7, // Valeur temporaire
      trending: Math.random() > 0.8, // Valeur temporaire
      creator: {
        name: 'HCP Design',
        avatar: '/placeholder.svg?height=100&width=100',
        isVerified: true,
      },
      description: `Design ${design.titre} - ${design.theme}`,
      phoneModel: 'Compatible tous modèles',
      createdAt: new Date().toISOString().split('T')[0],
    })) || [];

    return NextResponse.json(transformedData);
  } catch (error) {
    console.error('Erreur serveur:', error);
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    );
  }
}

// POST - Créer un nouveau design
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { titre, theme, fichier, tags } = body;

    if (!titre || !theme || !fichier) {
      return NextResponse.json(
        { error: 'Titre, thème et fichier sont requis' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('designs')
      .insert({
        titre,
        theme,
        fichier,
        tags: Array.isArray(tags) ? tags.join(', ') : tags,
        utilisations: 0
      })
      .select()
      .single();

    if (error) {
      console.error('Erreur lors de la création du design:', error);
      return NextResponse.json(
        { error: 'Erreur lors de la création du design' },
        { status: 500 }
      );
    }

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Erreur serveur:', error);
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    );
  }
}

// PUT - Mettre à jour un design
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, titre, theme, fichier, tags } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID du design requis' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (titre) updateData.titre = titre;
    if (theme) updateData.theme = theme;
    if (fichier) updateData.fichier = fichier;
    if (tags) updateData.tags = Array.isArray(tags) ? tags.join(', ') : tags;

    const { data, error } = await supabase
      .from('designs')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Erreur lors de la mise à jour du design:', error);
      return NextResponse.json(
        { error: 'Erreur lors de la mise à jour du design' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Erreur serveur:', error);
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    );
  }
}

// DELETE - Supprimer un design
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'ID du design requis' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('designs')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Erreur lors de la suppression du design:', error);
      return NextResponse.json(
        { error: 'Erreur lors de la suppression du design' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: 'Design supprimé avec succès' });
  } catch (error) {
    console.error('Erreur serveur:', error);
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    );
  }
}