"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import PromotionManager from "@/components/admin/PromotionManager"

export default function AdminPromotionsPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Link href="/admin">
            <Button variant="ghost" className="flex items-center gap-2 mb-2">
              <ArrowLeft className="h-4 w-4" /> Retour au tableau de bord
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">Gestion des Promotions</h1>
          <p className="text-gray-500">Créez et gérez les promotions affichées sur le site</p>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="all">Toutes les promotions</TabsTrigger>
          <TabsTrigger value="active">Promotions actives</TabsTrigger>
          <TabsTrigger value="upcoming">À venir</TabsTrigger>
          <TabsTrigger value="expired">Expirées</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all">
          <PromotionManager />
        </TabsContent>
        
        <TabsContent value="active">
          <div className="text-center py-12 text-gray-500">
            Filtrage par promotions actives à implémenter
          </div>
        </TabsContent>
        
        <TabsContent value="upcoming">
          <div className="text-center py-12 text-gray-500">
            Filtrage par promotions à venir à implémenter
          </div>
        </TabsContent>
        
        <TabsContent value="expired">
          <div className="text-center py-12 text-gray-500">
            Filtrage par promotions expirées à implémenter
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}