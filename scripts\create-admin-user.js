// Script pour créer un utilisateur administrateur dans Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Informations de connexion à Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Vérifier que les variables d'environnement sont définies
if (!supabaseUrl || !supabaseKey) {
  console.error('Les variables d\'environnement NEXT_PUBLIC_SUPABASE_URL et NEXT_PUBLIC_SUPABASE_ANON_KEY doivent être définies.');
  process.exit(1);
}

// Créer un client Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

// Informations de l'utilisateur administrateur
const adminUser = {
  email: '<EMAIL>',
  password: 'Fr<PERSON>emelody-02032245',
  userData: {
    first_name: 'Admin',
    last_name: '<PERSON><PERSON>',
    whatsapp: '+225123456789',
  }
};

// Fonction pour créer l'utilisateur administrateur
async function createAdminUser() {
  try {
    console.log('Création de l\'utilisateur administrateur...');
    // On tente directement la création, Supabase gère l'existence
    const { data, error } = await supabase.auth.signUp({
      email: adminUser.email,
      password: adminUser.password,
      options: {
        data: adminUser.userData,
      },
    });
    if (error) {
      if (error.message && error.message.includes('already registered')) {
        console.log('Un utilisateur avec cet email existe déjà.');
      } else {
        console.error('Erreur lors de la création de l\'utilisateur:', error);
      }
      return;
    }
    console.log('Utilisateur créé avec succès:', data);
    console.log('Note: Vous devrez peut-être mettre à jour manuellement le rôle de l\'utilisateur dans la console Supabase.');
  } catch (error) {
    console.error('Erreur inattendue:', error);
  }
}

// Exécuter la fonction
createAdminUser();
