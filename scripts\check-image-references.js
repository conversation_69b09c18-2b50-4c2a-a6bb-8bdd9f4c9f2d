/**
 * Script pour vérifier les références aux images dans le code
 * Ce script parcourt tous les fichiers .tsx, .jsx, .ts et .js du projet
 * et vérifie si les images référencées existent dans le répertoire public
 */

const fs = require('fs');
const path = require('path');

// Fonction pour parcourir récursivement un répertoire
function walkDir(dir, callback) {
  if (!fs.existsSync(dir)) {
    console.log(`Le répertoire ${dir} n'existe pas.`);
    return;
  }
  
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    isDirectory ? walkDir(dirPath, callback) : callback(path.join(dir, f));
  });
}

// Fonction pour extraire les chemins d'images d'un fichier
function extractImagePaths(filePath) {
  // Ignorer les fichiers node_modules, .git, etc.
  if (filePath.includes('node_modules') || filePath.includes('.git') || filePath.includes('.next')) {
    return [];
  }
  
  // Vérifier l'extension du fichier
  const ext = path.extname(filePath).toLowerCase();
  if (!['.tsx', '.jsx', '.ts', '.js', '.md'].includes(ext)) {
    return [];
  }
  
  try {
    // Lire le contenu du fichier
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Rechercher les chemins d'images
    const regex = /(?:src|image|imageUrl|thumbnail|image_url):\s*["']([^"']*\.(?:png|jpg|jpeg|gif|webp))["']/g;
    const matches = [];
    let match;
    
    while ((match = regex.exec(content)) !== null) {
      const imagePath = match[1];
      
      // Ignorer les URLs externes
      if (imagePath.startsWith('http')) {
        continue;
      }
      
      // Ignorer les placeholders
      if (imagePath.includes('placeholder')) {
        continue;
      }
      
      matches.push({
        path: imagePath,
        source: filePath
      });
    }
    
    return matches;
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier ${filePath}:`, error);
    return [];
  }
}

// Fonction pour vérifier si un fichier existe
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    console.error(`Erreur lors de la vérification du fichier ${filePath}:`, error);
    return false;
  }
}

// Fonction principale
function main() {
  console.log('Vérification des références aux images...');
  
  const imagePaths = [];
  
  // Parcourir les répertoires app et components
  walkDir('app', (filePath) => {
    const paths = extractImagePaths(filePath);
    imagePaths.push(...paths);
  });
  
  walkDir('components', (filePath) => {
    const paths = extractImagePaths(filePath);
    imagePaths.push(...paths);
  });
  
  walkDir('lib', (filePath) => {
    const paths = extractImagePaths(filePath);
    imagePaths.push(...paths);
  });
  
  walkDir('hooks', (filePath) => {
    const paths = extractImagePaths(filePath);
    imagePaths.push(...paths);
  });
  
  walkDir('services', (filePath) => {
    const paths = extractImagePaths(filePath);
    imagePaths.push(...paths);
  });
  
  console.log(`${imagePaths.length} références aux images trouvées.`);
  
  // Vérifier si les images existent
  const missingImages = [];
  
  imagePaths.forEach(item => {
    const { path: imagePath, source } = item;
    
    // Normaliser le chemin pour qu'il commence par public si ce n'est pas le cas
    let normalizedPath = imagePath;
    if (imagePath.startsWith('/')) {
      normalizedPath = `public${imagePath}`;
    }
    
    if (!fileExists(normalizedPath)) {
      missingImages.push({
        path: imagePath,
        normalizedPath,
        source
      });
    }
  });
  
  if (missingImages.length > 0) {
    console.log(`${missingImages.length} images manquantes:`);
    
    // Regrouper les images manquantes par source
    const groupedBySource = {};
    
    missingImages.forEach(item => {
      if (!groupedBySource[item.source]) {
        groupedBySource[item.source] = [];
      }
      
      groupedBySource[item.source].push(item.path);
    });
    
    // Afficher les images manquantes par source
    Object.keys(groupedBySource).forEach(source => {
      console.log(`\nDans ${source}:`);
      
      groupedBySource[source].forEach(path => {
        console.log(`  - ${path}`);
      });
    });
    
    // Créer les dossiers manquants
    console.log('\nCréation des dossiers manquants...');
    
    missingImages.forEach(item => {
      const dir = path.dirname(item.normalizedPath);
      
      if (!fs.existsSync(dir)) {
        try {
          fs.mkdirSync(dir, { recursive: true });
          console.log(`Dossier créé: ${dir}`);
        } catch (error) {
          console.error(`Erreur lors de la création du dossier ${dir}:`, error);
        }
      }
    });
  } else {
    console.log('Toutes les images référencées existent!');
  }
  
  console.log('Vérification terminée!');
}

// Exécuter la fonction principale
main();
