"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { 
  ArrowLeft, 
  CreditCard, 
  Truck, 
  Phone, 
  MapPin, 
  User, 
  Mail, 
  CheckCircle2,
  QrCode,
  Smartphone,
  Monitor,
  Copy
} from "lucide-react"
import Image from 'next/image'
import { useSimpleCart } from "@/hooks/use-simple-cart"

export default function CheckoutPage() {
  const { items, subtotal, shippingCost, total } = useSimpleCart()
  const [isMobile, setIsMobile] = useState(false)
  const [selectedPhoneModel, setSelectedPhoneModel] = useState("")
  
  // État pour les informations de livraison
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    notes: "",
    paymentMethod: "wave" // Default to Wave
  })

  // Détecter si l'utilisateur est sur mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Extraire le modèle de téléphone des articles du panier
  useEffect(() => {
    if (items.length > 0) {
      const phoneModel = items[0]?.phoneModel || items[0]?.customization?.phoneModel || ""
      setSelectedPhoneModel(phoneModel)
    }
  }, [items])

  // Mettre à jour les champs du formulaire
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Gérer le changement de méthode de paiement
  const handlePaymentChange = (value: string) => {
    setFormData(prev => ({ ...prev, paymentMethod: value }))
  }

  // Soumettre la commande
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Ici, vous enverriez les données au serveur
    alert("Commande passée avec succès!")
    // Rediriger vers une page de confirmation
  }

  // Formater le prix en FCFA
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + " FCFA"
  }

  // Copier le numéro Orange Money
  const copyOrangeNumber = () => {
    navigator.clipboard.writeText("070949584")
    alert("Numéro Orange Money copié !")
  }

  // Générer le message WhatsApp avec les informations de commande
  const generateWhatsAppMessage = () => {
    const itemsList = items.map(item => 
      `- ${item.name} ${item.customization?.phoneModel ? `(${item.customization.phoneModel})` : ''} x${item.quantity}`
    ).join('\n')
    
    return `🛒 *NOUVELLE COMMANDE HCP DESIGN*\n\n` +
           `👤 *Informations client:*\n` +
           `Nom: ${formData.firstName} ${formData.lastName}\n` +
           `Téléphone: ${formData.phone}\n` +
           `Email: ${formData.email}\n\n` +
           `📱 *Série du téléphone:* ${selectedPhoneModel || '[À préciser]'}\n\n` +
           `🛍️ *Articles commandés:*\n${itemsList}\n\n` +
           `📍 *Adresse de livraison:*\n${formData.address}, ${formData.city}\n\n` +
           `💰 *Montant total:* ${formatPrice(total)}\n` +
           `💳 *Méthode de paiement:* ${formData.paymentMethod === 'wave' ? 'Wave' : formData.paymentMethod === 'orange' ? 'Orange Money' : 'Paiement à la livraison'}\n\n` +
           `📝 *Instructions:* ${formData.notes || 'Aucune'}\n\n` +
           `⚠️ *Note:* Si vous avez une image de personnalisation, veuillez l'envoyer après ce message.`
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Finaliser la commande</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Formulaire de commande */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit}>
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" /> Informations personnelles
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">Prénom</Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Nom</Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="flex items-center">
                      <Mail className="mr-1 h-4 w-4" /> Email
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="flex items-center">
                      <Phone className="mr-1 h-4 w-4" /> Téléphone
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="mr-2 h-5 w-5" /> Adresse de livraison
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="address">Adresse complète</Label>
                  <Input
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="city">Ville / Commune</Label>
                  <Input
                    id="city"
                    name="city"
                    value={formData.city}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Instructions de livraison (optionnel)</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    placeholder="Informations supplémentaires pour la livraison..."
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" /> Méthode de paiement
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup 
                  value={formData.paymentMethod} 
                  onValueChange={handlePaymentChange}
                  className="space-y-4"
                >
                  {/* Wave Payment Option */}
                  <div className="flex flex-col space-y-2 border p-4 rounded-md">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="wave" id="wave" />
                      <Label htmlFor="wave" className="flex items-center cursor-pointer">
                        <Image src="/images/payments/wave/logo/wave-logo.png" alt="Wave" width={24} height={24} className="mr-2" /> 
                        <span className="font-semibold">Wave</span>
                      </Label>
                    </div>
                    {formData.paymentMethod === "wave" && (
                      <div className="mt-4 pl-6">
                        {isMobile ? (
                          // Version Mobile - Lien direct
                          <div className="text-center">
                            <div className="flex items-center justify-center mb-3">
                              <Smartphone className="mr-2 h-5 w-5 text-blue-500" />
                              <span className="text-sm font-medium">Paiement mobile détecté</span>
                            </div>
                            <Link href="https://pay.wave.com/m/M_oLBggfv-S8aE/c/ci/" passHref legacyBehavior>
                              <a target="_blank" rel="noopener noreferrer">
                                <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3">
                                  <Image src="/images/payments/wave/logo/wave-logo.png" alt="Wave" width={20} height={20} className="mr-2" />
                                  Payer avec Wave
                                </Button>
                              </a>
                            </Link>
                            <p className="text-xs text-gray-600 mt-2">Cliquez pour ouvrir l'application Wave</p>
                          </div>
                        ) : (
                          // Version Desktop - QR Code
                          <div className="text-center">
                            <div className="flex items-center justify-center mb-3">
                              <Monitor className="mr-2 h-5 w-5 text-blue-500" />
                              <span className="text-sm font-medium">Scannez le QR Code avec votre téléphone</span>
                            </div>
                            <div className="bg-white p-4 rounded-lg border-2 border-blue-200 inline-block">
                              <Image 
                                src="/images/payments/wave/qr/wave-qr.png" 
                                alt="QR Code Wave" 
                                width={200} 
                                height={200} 
                                className="mx-auto"
                              />
                            </div>
                            <p className="text-xs text-gray-600 mt-2">Ouvrez votre app Wave et scannez ce code</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Orange Money Payment Option */}
                  <div className="flex flex-col space-y-2 border p-4 rounded-md">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="orange" id="orange" />
                      <Label htmlFor="orange" className="flex items-center cursor-pointer">
                        <Image src="/images/payments/orange/logo/orange-money-logo.png" alt="Orange Money" width={24} height={24} className="mr-2" /> 
                        <span className="font-semibold">Orange Money</span>
                      </Label>
                    </div>
                    {formData.paymentMethod === "orange" && (
                      <div className="mt-4 pl-6">
                        <div className="space-y-4">
                          {/* QR Code - Affiché sur desktop et mobile */}
                          <div className="text-center">
                            <div className="flex items-center justify-center mb-3">
                              <QrCode className="mr-2 h-5 w-5 text-orange-500" />
                              <span className="text-sm font-medium">Scannez avec Orange Money</span>
                            </div>
                            <div className="bg-white p-4 rounded-lg border-2 border-orange-200 inline-block">
                              <Image 
                                src="/images/payments/orange/qr/orange-qr.png" 
                                alt="QR Code Orange Money" 
                                width={180} 
                                height={180} 
                                className="mx-auto"
                              />
                            </div>
                          </div>
                          
                          {/* Numéro Orange Money */}
                          <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                            <p className="text-sm font-medium text-gray-700 mb-2">Ou payez directement au numéro :</p>
                            <div className="flex items-center justify-between bg-white p-3 rounded border">
                              <div>
                                <p className="text-xs text-gray-500">Numéro Orange Money</p>
                                <p className="text-lg font-bold text-orange-600">07 09 49 58 48</p>
                              </div>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={copyOrangeNumber}
                                className="border-orange-300 text-orange-600 hover:bg-orange-50"
                              >
                                <Copy className="h-4 w-4 mr-1" />
                                Copier
                              </Button>
                            </div>
                            <p className="text-xs text-gray-600 mt-2">💡 Copiez/collez ce numéro dans votre app Orange Money</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Paiement à la livraison Option - Conserved */}
                  <div className="flex items-center space-x-2 border p-4 rounded-md">
                    <RadioGroupItem value="delivery" id="delivery" />
                    <Label htmlFor="delivery" className="flex items-center cursor-pointer">
                      <Truck className="mr-2 h-4 w-4" /> Paiement à la livraison
                    </Label>
                  </div>
                </RadioGroup>

                {/* WhatsApp Instructions */}
                {(formData.paymentMethod === "wave" || formData.paymentMethod === "orange") && (
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
                      <p className="text-sm text-gray-700 mb-2">
                        <span className="font-bold text-green-700">🔔 Après avoir effectué le paiement</span>, cliquez sur le bouton ci-dessous pour nous envoyer les informations de votre commande via WhatsApp.
                      </p>
                      <p className="text-xs text-gray-600">
                        📸 Si vous avez une image de personnalisation, vous devrez l'envoyer manuellement après ce message.
                      </p>
                    </div>
                    <Button 
                      className="w-full bg-green-500 hover:bg-green-600 text-white py-3 text-base font-medium"
                      onClick={() => {
                        const message = generateWhatsAppMessage()
                        const whatsappUrl = `https://wa.me/2250709495848?text=${encodeURIComponent(message)}`
                        window.open(whatsappUrl, '_blank')
                      }}
                      disabled={!formData.firstName || !formData.lastName || !formData.phone}
                    >
                      <Image src="/images/icons/whatsapp.png" alt="WhatsApp" width={24} height={24} className="mr-2" /> 
                      📲 Envoyer sur WhatsApp
                    </Button>
                    {(!formData.firstName || !formData.lastName || !formData.phone) && (
                      <p className="text-xs text-red-500 mt-2 text-center">
                        Veuillez remplir vos informations personnelles pour activer WhatsApp
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Link href="/cart">
                <Button variant="outline" type="button">
                  <ArrowLeft className="mr-2 h-4 w-4" /> Retour au panier
                </Button>
              </Link>
              <Button type="submit" className="bg-purple-600 hover:bg-purple-700">
                <CheckCircle2 className="mr-2 h-4 w-4" /> Confirmer la commande
              </Button>
            </div>
          </form>
        </div>

        {/* Résumé de la commande */}
        <div>
          <Card className="sticky top-24">
            <CardHeader>
              <CardTitle>Résumé de la commande</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                {items.length > 0 ? (
                  items.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="flex-1">
                        {item.name}
                        {item.customization?.phoneModel && (
                          <span className="text-gray-500"> ({item.customization.phoneModel})</span>
                        )}
                        {item.quantity > 1 && (
                          <span className="text-gray-500"> x{item.quantity}</span>
                        )}
                      </span>
                      <span className="font-medium">{formatPrice(item.price * item.quantity)}</span>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-gray-500 py-4">
                    <p>Votre panier est vide</p>
                    <Link href="/products" className="text-purple-600 hover:underline text-sm">
                      Continuer vos achats
                    </Link>
                  </div>
                )}
              </div>
              
              {items.length > 0 && (
                <>
                  <Separator />
                  
                  <div className="flex justify-between">
                    <span>Sous-total</span>
                    <span>{formatPrice(subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="flex items-center">
                      <Truck className="mr-2 h-4 w-4" /> Livraison
                    </span>
                    <span>{formatPrice(shippingCost)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span>{formatPrice(total)}</span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  )
}
