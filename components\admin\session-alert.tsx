"use client";

import { useEffect, useState } from "react";
import { useAdminSession } from "./session-provider";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Clock } from "lucide-react";

// Durée avant expiration de la session (en millisecondes)
const SESSION_WARNING_TIME = 25 * 60 * 1000; // 25 minutes (5 minutes avant expiration)

/**
 * Alerte de session inactive
 * Affiche une alerte lorsque la session est sur le point d'expirer
 */
export function SessionAlert() {
  const { isActive, resetTimer } = useAdminSession();
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);

  // Vérifier si la session est sur le point d'expirer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isActive) {
      interval = setInterval(() => {
        // Calculer le temps restant avant expiration
        const now = Date.now();
        const lastActivity = now - SESSION_WARNING_TIME;
        const timeRemaining = 30 * 60 * 1000 - (now - lastActivity);
        
        if (timeRemaining <= 5 * 60 * 1000) { // Moins de 5 minutes restantes
          setShowWarning(true);
          setTimeLeft(Math.max(0, timeRemaining));
        } else {
          setShowWarning(false);
          setTimeLeft(null);
        }
      }, 10000); // Vérifier toutes les 10 secondes
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive]);

  // Formater le temps restant
  const formatTimeLeft = () => {
    if (timeLeft === null) return "";
    
    const minutes = Math.floor(timeLeft / 60000);
    const seconds = Math.floor((timeLeft % 60000) / 1000);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Si pas d'avertissement, ne rien afficher
  if (!showWarning) return null;

  return (
    <Alert className="fixed bottom-4 right-4 w-96 z-50 bg-yellow-50 border-yellow-400">
      <Clock className="h-4 w-4 text-yellow-600" />
      <AlertTitle className="text-yellow-800">Session sur le point d'expirer</AlertTitle>
      <AlertDescription className="text-yellow-700">
        <p className="mb-2">
          Votre session expirera dans {formatTimeLeft()} en raison d'inactivité.
        </p>
        <Button 
          variant="outline" 
          className="bg-yellow-100 border-yellow-400 text-yellow-800 hover:bg-yellow-200"
          onClick={resetTimer}
        >
          Rester connecté
        </Button>
      </AlertDescription>
    </Alert>
  );
}
