# Script pour créer des images placeholder PNG
# Ce script crée des fichiers PNG vides dans les dossiers spécifiés

# Fonction pour créer un fichier PNG vide
function Create-Empty-PNG {
    param (
        [string]$path,
        [string]$name
    )
    
    $pngPath = Join-Path -Path $path -ChildPath "$name.png"
    if (-not (Test-Path -Path $pngPath)) {
        # Créer un fichier PNG vide (1x1 pixel transparent)
        # Nous utilisons un fichier PNG de base64 encodé qui représente un pixel transparent
        $base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
        $bytes = [Convert]::FromBase64String($base64)
        
        # Écrire le fichier PNG
        [System.IO.File]::WriteAllBytes($pngPath, $bytes)
        
        Write-Host "Fichier PNG créé: $pngPath" -ForegroundColor Green
    } else {
        Write-Host "Le fichier PNG existe déjà: $pngPath" -ForegroundColor Yellow
    }
}

# Dossiers et noms de fichiers
$designs = @(
    @{path = "public\images\gallery\floral-dream"; name = "floral-dream"},
    @{path = "public\images\gallery\abstract-waves"; name = "abstract-waves"},
    @{path = "public\images\gallery\mountain-sunset"; name = "mountain-sunset"},
    @{path = "public\images\gallery\cosmic-galaxy"; name = "cosmic-galaxy"}
)

# Créer les fichiers PNG
foreach ($design in $designs) {
    Create-Empty-PNG -path $design.path -name $design.name
}

Write-Host "Création des images placeholder terminée!" -ForegroundColor Green
