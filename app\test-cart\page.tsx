"use client"

import { useState } from "react"
import SimpleProductCard from "@/components/simple-product-card"
import { Button } from "@/components/ui/button"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import { useSimpleFavorites } from "@/hooks/use-simple-favorites"

// Produits de test
const testProducts = [
  {
    id: "1",
    name: "Coque iPhone 15 Pro Max - Floral Dream",
    price: 8000,
    image: "/images/products/coque-iphone-floral.jpg",
    rating: 4.8,
    reviews: 124,
    isNew: true,
    isBestseller: false,
    category: "Coques",
    type: "Premium"
  },
  {
    id: "2",
    name: "Coque iPhone 16 Pro - Abstract Waves",
    price: 8000,
    image: "/images/products/coque-iphone-abstract.jpg",
    rating: 4.5,
    reviews: 89,
    isNew: false,
    isBestseller: true,
    category: "Coques",
    type: "Premium"
  },
  {
    id: "3",
    name: "Coque Samsung Galaxy S24 - Cosmic Galaxy",
    price: 8000,
    image: "/images/products/coque-samsung-galaxy.jpg",
    rating: 4.7,
    reviews: 56,
    isNew: true,
    isBestseller: false,
    category: "Coques",
    type: "Premium"
  }
]

export default function TestCartPage() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const { items, subtotal, shippingCost, total, clearCart } = useSimpleCart()
  const { favorites } = useSimpleFavorites()
  
  // Formater le prix
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF'
    }).format(price)
  }
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="mb-8 text-3xl font-bold">Test du panier et des favoris</h1>
      
      <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">État du panier</h2>
          <p className="mb-2">Nombre d'articles: <span className="font-bold">{items.length}</span></p>
          <p className="mb-2">Sous-total: <span className="font-bold">{formatPrice(subtotal)}</span></p>
          <p className="mb-2">Frais de livraison: <span className="font-bold">{formatPrice(shippingCost)}</span></p>
          <p className="mb-4">Total: <span className="font-bold text-purple-600">{formatPrice(total)}</span></p>
          
          {items.length > 0 && (
            <Button 
              variant="destructive" 
              onClick={() => clearCart()}
            >
              Vider le panier
            </Button>
          )}
        </div>
        
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">État des favoris</h2>
          <p className="mb-4">Nombre de favoris: <span className="font-bold">{favorites.length}</span></p>
          
          {favorites.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium">Produits favoris:</h3>
              <ul className="list-inside list-disc">
                {favorites.map(favorite => (
                  <li key={favorite.id}>{favorite.name} - {formatPrice(favorite.price)}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
      
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Produits de test</h2>
        
        <div className="flex gap-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            onClick={() => setViewMode("grid")}
          >
            Grille
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            onClick={() => setViewMode("list")}
          >
            Liste
          </Button>
        </div>
      </div>
      
      <div className={
        viewMode === "grid" 
          ? "grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3" 
          : "space-y-6"
      }>
        {testProducts.map(product => (
          <SimpleProductCard
            key={product.id}
            product={product}
            viewMode={viewMode}
          />
        ))}
      </div>
    </div>
  )
}
