"use client";

import { usePaymentMethods } from "@/hooks/use-payment-methods";
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { Skeleton } from "@/components/ui/skeleton";
import { getVersionedImagePath } from "@/lib/image-version";
import { useState } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

interface PaymentMethodsProps {
  onSelect: (methodId: string) => void;
  selectedMethodId?: string;
}

export default function PaymentMethods({ onSelect, selectedMethodId }: PaymentMethodsProps) {
  const { activePaymentMethods, isLoading, error } = usePaymentMethods();
  const [selectedMethod, setSelectedMethod] = useState<string | undefined>(selectedMethodId);

  const handleSelect = (methodId: string) => {
    setSelectedMethod(methodId);
    onSelect(methodId);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 2 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-6 w-3/4 mb-2" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <Skeleton className="h-16 w-16 rounded-md" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-4 w-32" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 text-red-500 rounded-md">
        <p>Erreur lors du chargement des méthodes de paiement.</p>
        <p>{error.message}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <RadioGroup value={selectedMethod} onValueChange={handleSelect}>
        {activePaymentMethods.map((method) => (
          <div key={method.id} className="flex items-start space-x-2">
            <RadioGroupItem value={method.id} id={`payment-${method.id}`} className="mt-1" />
            <div className="flex-1">
              <Label htmlFor={`payment-${method.id}`} className="flex items-center mb-2">
                <span className="text-lg font-medium">{method.name}</span>
              </Label>
              <Card className={`border-2 ${selectedMethod === method.id ? 'border-primary' : 'border-transparent'}`}>
                <CardContent className="p-4">
                  <div className="flex flex-col md:flex-row items-center gap-4">
                    <div className="relative h-16 w-32">
                      {method.logo_url && (
                        <Image
                          src={getVersionedImagePath(method.logo_url)}
                          alt={method.name}
                          fill
                          className="object-contain"
                        />
                      )}
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">{method.description}</p>
                      {method.payment_details?.phone && (
                        <p className="text-sm font-medium mt-1">
                          Numéro: {method.payment_details.phone}
                        </p>
                      )}
                    </div>
                  </div>
                  {method.qr_code_url && selectedMethod === method.id && (
                    <div className="mt-4 flex justify-center">
                      <div className="relative h-48 w-48">
                        <Image
                          src={getVersionedImagePath(method.qr_code_url)}
                          alt={`Code QR ${method.name}`}
                          fill
                          className="object-contain"
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}
