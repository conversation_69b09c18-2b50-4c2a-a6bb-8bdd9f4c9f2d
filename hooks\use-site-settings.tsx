"use client";

import { useState, useEffect, createContext, useContext } from "react";
import { supabase, supabaseApi } from "@/lib/supabase";
import { useToast } from "@/components/ui/use-toast";

// Types pour les paramètres du site
export type SiteInfo = {
  name: string;
  description: string;
  primary_color: string;
  secondary_color: string;
};

export type ContactInfo = {
  whatsapp: string;
  email: string;
  address: string;
};

export type SocialMedia = {
  facebook: string;
  instagram: string;
};

export type NavigationItem = {
  name: string;
  path: string;
};

export type HomeBanner = {
  enabled: boolean;
  images: string[];
  transition_time: number;
  transition_effect: string;
};

// Type pour tous les paramètres du site
export type SiteSettings = {
  site_name: string;
  site_description: string;
  primary_color: string;
  secondary_color: string;
  show_whatsapp_button: boolean;
  whatsapp_number: string;
  navigation: NavigationItem[];
  home_banner: HomeBanner;
  site_info?: SiteInfo;
  contact_info?: ContactInfo;
  social_media?: SocialMedia;
};

// Valeurs par défaut
const defaultSettings: SiteSettings = {
  site_name: "HCP Design CI",
  site_description: "Coques de téléphone personnalisées",
  primary_color: "#6d28d9",
  secondary_color: "#8b5cf6",
  show_whatsapp_button: true,
  whatsapp_number: "+2250709495849",
  navigation: [
    { name: "Accueil", path: "/" },
    { name: "Produits", path: "/produits" },
    { name: "Promo", path: "/promo" },
    { name: "Personnaliser", path: "/customize" },
    { name: "Contact", path: "/contact" },
  ],
  home_banner: {
    enabled: true,
    images: [
      "https://example.com/banner1.jpg",
      "https://example.com/banner2.jpg",
      "https://example.com/banner3.jpg",
    ],
    transition_time: 4000,
    transition_effect: "zoom",
  },
};

// Créer le contexte
type SiteSettingsContextType = {
  settings: SiteSettings;
  isLoading: boolean;
  error: string | null;
};

const SiteSettingsContext = createContext<SiteSettingsContextType>({
  settings: defaultSettings,
  isLoading: false,
  error: null,
});

// Provider pour les paramètres du site
export function SiteSettingsProvider({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<SiteSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { toast } = useToast();

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // Vérifier si supabaseApi.siteSettings existe
        if (supabaseApi.siteSettings) {
          try {
            // Essayer d'abord la nouvelle table site_settings
            const { data: newData, error: newError } = await supabaseApi.siteSettings.getAll();

            if (!newError && newData && newData.length > 0) {
              // Convertir les données en objet
              const settingsObj: Partial<SiteSettings> = {};

              newData.forEach((item) => {
                try {
                  // Essayer de parser la valeur si c'est une chaîne JSON
                  if (typeof item.value === 'string' && (item.value.startsWith('{') || item.value.startsWith('['))) {
                    try {
                      settingsObj[item.key as keyof SiteSettings] = JSON.parse(item.value);
                    } catch (e) {
                      settingsObj[item.key as keyof SiteSettings] = item.value;
                    }
                  } else {
                    settingsObj[item.key as keyof SiteSettings] = item.value;
                  }
                } catch (parseError) {
                  console.error(`Erreur lors du parsing de la valeur pour ${item.key}:`, parseError);
                  settingsObj[item.key as keyof SiteSettings] = item.value;
                }
              });

              // Compatibilité avec l'ancien format
              if (settingsObj.site_info) {
                settingsObj.site_name = settingsObj.site_info.name;
                settingsObj.site_description = settingsObj.site_info.description;
                settingsObj.primary_color = settingsObj.site_info.primary_color;
                settingsObj.secondary_color = settingsObj.site_info.secondary_color;
              }

              if (settingsObj.contact_info) {
                settingsObj.whatsapp_number = settingsObj.contact_info.whatsapp;
                settingsObj.show_whatsapp_button = true;
              }

              // Fusionner avec les valeurs par défaut
              setSettings({
                ...defaultSettings,
                ...settingsObj,
              });
              setIsLoading(false);
              return;
            }
          } catch (siteSettingsError) {
            console.error("Erreur lors de l'accès à la table site_settings:", siteSettingsError);
            // Continuer avec le fallback
          }
        } else {
          console.warn("supabaseApi.siteSettings n'est pas défini, utilisation du fallback");
        }

        // Fallback à l'ancienne table si la nouvelle n'a pas de données
        const { data, error } = await supabase
          .from("configurations")
          .select("config_data")
          .eq("id", "site_settings")
          .single();

        if (error) {
          console.error("Erreur lors du chargement des paramètres:", error);
          setError("Erreur lors du chargement des paramètres");
          toast({
            title: "Erreur",
            description: "Impossible de charger les paramètres du site",
            variant: "destructive",
          });
        } else if (data) {
          setSettings(data.config_data as SiteSettings);
        }
      } catch (error) {
        console.error("Exception lors du chargement des paramètres:", error);
        setError("Erreur lors du chargement des paramètres");
        toast({
          title: "Erreur",
          description: "Impossible de charger les paramètres du site",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  return (
    <SiteSettingsContext.Provider value={{ settings, isLoading, error }}>
      {children}
    </SiteSettingsContext.Provider>
  );
}

// Hook pour utiliser les paramètres du site
export function useSiteSettings() {
  const context = useContext(SiteSettingsContext);
  if (context === undefined) {
    throw new Error("useSiteSettings doit être utilisé à l'intérieur d'un SiteSettingsProvider");
  }
  return context;
}
