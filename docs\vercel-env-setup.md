# Configuration des variables d'environnement sur Vercel

Pour que le déploiement fonctionne correctement sur Vercel, vous devez configurer les variables d'environnement suivantes dans le tableau de bord Vercel.

## Variables d'environnement requises

Voici les variables d'environnement que vous devez configurer :

```
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://bekwlxorzlyoudsnajsn.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJla3dseG9yemx5b3Vkc25hanNuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NzUzOTgsImV4cCI6MjA2MDU1MTM5OH0.yRKV3bo0ww0GIW1LtBB0HJQQErVWHbbbpkWVcFScS34
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJla3dseG9yemx5b3Vkc25hanNuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDk3NTM5OCwiZXhwIjoyMDYwNTUxMzk4fQ.Rl-Yw9Hl_Ql_Hl_Ql_Hl_Ql_Hl_Ql_Hl_Ql_Hl_Ql_Hl

# Clé API MailerSend
MAILERSEND_API_KEY=mlsn.7ed7c5f6d3f971ad2fa83aab275b16c9b4bf07e03eaede75730a656db6f53786

# Configuration SMTP MailerSend
SMTP_HOST=smtp.mailersend.net
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=mssp.EW2HtPA.pq3enl6ded0g2vwr.PBtqf6i

# Email d'expéditeur (doit être vérifié dans MailerSend)
MAILERSEND_FROM_EMAIL=<EMAIL>
MAILERSEND_FROM_NAME=HCP Design

# reCAPTCHA
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=6LcZ3TgrAAAAAOnap0r_cX5yFApJZwCux0rvz-T4
RECAPTCHA_SECRET_KEY=6LcZ3TgrAAAAAAzLFx7QSAi7Ni5jsLsZ5TVyEA7o

# URL de base du site
NEXT_PUBLIC_BASE_URL=https://v0-site-hcp-designci.vercel.app
NEXT_PUBLIC_SITE_URL=https://v0-site-hcp-designci.vercel.app
```

## Comment configurer les variables d'environnement sur Vercel

1. Connectez-vous à votre compte Vercel : https://vercel.com/dashboard
2. Sélectionnez votre projet `v0-site-hcp-designci`
3. Cliquez sur l'onglet "Settings"
4. Dans le menu de gauche, cliquez sur "Environment Variables"
5. Ajoutez chaque variable d'environnement en spécifiant :
   - Le nom de la variable (ex: `NEXT_PUBLIC_SUPABASE_URL`)
   - La valeur de la variable
   - Les environnements où la variable doit être disponible (Production, Preview, Development)
6. Cliquez sur "Save" après avoir ajouté toutes les variables

## Redéployer après avoir configuré les variables d'environnement

Après avoir configuré les variables d'environnement, vous devez redéployer votre application :

1. Allez dans l'onglet "Deployments"
2. Trouvez le dernier déploiement
3. Cliquez sur les trois points (...) à droite du déploiement
4. Sélectionnez "Redeploy"

## Vérifier que les variables d'environnement sont correctement configurées

Pour vérifier que les variables d'environnement sont correctement configurées :

1. Attendez que le redéploiement soit terminé
2. Visitez votre site déployé
3. Vérifiez que les fonctionnalités qui dépendent des variables d'environnement fonctionnent correctement

Si vous rencontrez des problèmes, vérifiez les logs de déploiement pour voir s'il y a des erreurs liées aux variables d'environnement.
