"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Trash2, Plus, Image as ImageIcon, Save, Eye } from "lucide-react"
import { toast } from "sonner"
import AnimatedBanner from "@/components/animated-banner"

// Type pour les images de la bannière
interface BannerImage {
  id: string
  src: string
  alt: string
}

// Type pour la configuration de la bannière
interface BannerConfig {
  title: string
  subtitle: string
  badge: string
  code: string
  interval: number
  images: BannerImage[]
  isActive: boolean
}

export default function BannerConfig() {
  // État pour la configuration de la bannière
  const [config, setConfig] = useState<BannerConfig>({
    title: "Spécial Fête des Mères",
    subtitle: "Offrez un cadeau unique à votre mère avec une coque personnalisée et profitez de 20% de réduction",
    badge: "Offre Limitée",
    code: "MAMAN2025",
    interval: 4000,
    images: [
      { id: "1", src: "/images/promos/banner1.jpg?v=1747269269127", alt: "Promotion spéciale fête des mères" },
      { id: "2", src: "/images/promos/banner2.jpg?v=1747269269127", alt: "Offre exclusive" },
      { id: "3", src: "/images/promos/banner3.jpg?v=1747269269127", alt: "Réduction spéciale" }
    ],
    isActive: true
  })

  // État pour l'aperçu
  const [showPreview, setShowPreview] = useState(false)

  // Fonction pour ajouter une nouvelle image
  const addImage = () => {
    const newImage: BannerImage = {
      id: Date.now().toString(),
      src: "",
      alt: ""
    }
    setConfig({
      ...config,
      images: [...config.images, newImage]
    })
  }

  // Fonction pour supprimer une image
  const removeImage = (id: string) => {
    setConfig({
      ...config,
      images: config.images.filter(img => img.id !== id)
    })
  }

  // Fonction pour mettre à jour une image
  const updateImage = (id: string, field: keyof BannerImage, value: string) => {
    setConfig({
      ...config,
      images: config.images.map(img =>
        img.id === id ? { ...img, [field]: value } : img
      )
    })
  }

  // Fonction pour sauvegarder la configuration
  const saveConfig = () => {
    // Ici, vous implémenteriez la logique pour sauvegarder dans la base de données
    // Pour l'instant, nous simulons une sauvegarde réussie
    toast.success("Configuration de la bannière sauvegardée avec succès")
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Configuration de la Bannière Animée</CardTitle>
        <CardDescription>
          Personnalisez la bannière animée qui apparaît sur la page des promotions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="general">Général</TabsTrigger>
            <TabsTrigger value="images">Images</TabsTrigger>
            <TabsTrigger value="preview">Aperçu</TabsTrigger>
          </TabsList>

          {/* Onglet Général */}
          <TabsContent value="general" className="space-y-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Titre</Label>
                <Input
                  id="title"
                  value={config.title}
                  onChange={(e) => setConfig({...config, title: e.target.value})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="subtitle">Sous-titre</Label>
                <Input
                  id="subtitle"
                  value={config.subtitle}
                  onChange={(e) => setConfig({...config, subtitle: e.target.value})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="badge">Badge (optionnel)</Label>
                <Input
                  id="badge"
                  value={config.badge}
                  onChange={(e) => setConfig({...config, badge: e.target.value})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="code">Code Promo (optionnel)</Label>
                <Input
                  id="code"
                  value={config.code}
                  onChange={(e) => setConfig({...config, code: e.target.value})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="interval">Intervalle entre les transitions (ms)</Label>
                <div className="flex items-center gap-4">
                  <Slider
                    id="interval"
                    min={1000}
                    max={10000}
                    step={500}
                    value={[config.interval]}
                    onValueChange={(value) => setConfig({...config, interval: value[0]})}
                    className="flex-1"
                  />
                  <span className="w-16 text-right">{config.interval}ms</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  id="active"
                  checked={config.isActive}
                  onCheckedChange={(checked) => setConfig({...config, isActive: checked})}
                />
                <Label htmlFor="active">Activer la bannière</Label>
              </div>
            </div>
          </TabsContent>

          {/* Onglet Images */}
          <TabsContent value="images" className="space-y-4">
            <div className="flex justify-end mb-4">
              <Button onClick={addImage} className="flex items-center gap-2">
                <Plus className="h-4 w-4" /> Ajouter une image
              </Button>
            </div>

            {config.images.map((image, index) => (
              <Card key={image.id} className="mb-4">
                <CardContent className="pt-6">
                  <div className="grid gap-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">Image {index + 1}</h3>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => removeImage(image.id)}
                        disabled={config.images.length <= 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor={`src-${image.id}`}>URL de l'image</Label>
                      <Input
                        id={`src-${image.id}`}
                        value={image.src}
                        onChange={(e) => updateImage(image.id, 'src', e.target.value)}
                        placeholder="/images/promos/banner1.jpg"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor={`alt-${image.id}`}>Texte alternatif</Label>
                      <Input
                        id={`alt-${image.id}`}
                        value={image.alt}
                        onChange={(e) => updateImage(image.id, 'alt', e.target.value)}
                        placeholder="Description de l'image"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          {/* Onglet Aperçu */}
          <TabsContent value="preview">
            <div className="space-y-4">
              <Button
                onClick={() => setShowPreview(!showPreview)}
                className="flex items-center gap-2"
              >
                <Eye className="h-4 w-4" />
                {showPreview ? "Masquer l'aperçu" : "Afficher l'aperçu"}
              </Button>

              {showPreview && (
                <div className="mt-4">
                  <AnimatedBanner
                    images={config.images.map(img => ({ src: img.src, alt: img.alt }))}
                    title={config.title}
                    subtitle={config.subtitle}
                    badge={config.badge}
                    code={config.code}
                    interval={config.interval}
                  />
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button onClick={saveConfig} className="flex items-center gap-2">
          <Save className="h-4 w-4" /> Enregistrer les modifications
        </Button>
      </CardFooter>
    </Card>
  )
}
