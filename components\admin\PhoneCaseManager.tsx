"use client";

import { useState, useRef } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Trash2, Upload, Plus, Edit } from "lucide-react";
import Image from "next/image";

// Types pour les modèles de téléphone et les coques
interface PhoneModel {
  id: string;
  name: string;
  brand: string;
}

interface PhoneCase {
  id: string;
  modelId: string;
  imageUrl: string;
  name: string;
}

// Données d'exemple (à remplacer par des appels API réels)
const phoneModels: PhoneModel[] = [
  { id: "iphone15pro", name: "iPhone 15 Pro", brand: "Apple" },
  { id: "iphone15", name: "iPhone 15", brand: "Apple" },
  { id: "s23ultra", name: "Galaxy S23 Ultra", brand: "Samsung" },
  { id: "pixel7pro", name: "Pixel 7 Pro", brand: "Google" },
  { id: "nothingphone2", name: "Nothing Phone (2)", brand: "Nothing" },
];

export default function PhoneCaseManager() {
  const [cases, setCases] = useState<PhoneCase[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [caseName, setCaseName] = useState<string>("");
  const [editingCase, setEditingCase] = useState<PhoneCase | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  // Fonction pour gérer l'upload d'image
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Vérifier que c'est bien une image PNG avec fond transparent
      if (file.type !== "image/png") {
        alert("Veuillez sélectionner une image PNG avec fond transparent");
        return;
      }

      const reader = new FileReader();
      reader.onload = (event) => {
        setPreviewImage(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Fonction pour ajouter ou mettre à jour une coque
  const handleSaveCase = () => {
    if (!selectedModel || !caseName || !previewImage) {
      alert("Veuillez remplir tous les champs et sélectionner une image");
      return;
    }

    if (editingCase) {
      // Mise à jour d'une coque existante
      setCases(cases.map(c => 
        c.id === editingCase.id 
          ? { ...c, name: caseName, modelId: selectedModel, imageUrl: previewImage } 
          : c
      ));
      setEditingCase(null);
    } else {
      // Ajout d'une nouvelle coque
      const newCase: PhoneCase = {
        id: Date.now().toString(),
        modelId: selectedModel,
        name: caseName,
        imageUrl: previewImage,
      };
      setCases([...cases, newCase]);
    }

    // Réinitialiser le formulaire
    setSelectedModel("");
    setCaseName("");
    setPreviewImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Fonction pour éditer une coque
  const handleEditCase = (phoneCase: PhoneCase) => {
    setEditingCase(phoneCase);
    setSelectedModel(phoneCase.modelId);
    setCaseName(phoneCase.name);
    setPreviewImage(phoneCase.imageUrl);
  };

  // Fonction pour supprimer une coque
  const handleDeleteCase = (id: string) => {
    if (confirm("Êtes-vous sûr de vouloir supprimer cette coque ?")) {
      setCases(cases.filter(c => c.id !== id));
    }
  };

  // Fonction pour annuler l'édition
  const handleCancelEdit = () => {
    setEditingCase(null);
    setSelectedModel("");
    setCaseName("");
    setPreviewImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{editingCase ? "Modifier une coque" : "Ajouter une nouvelle coque"}</CardTitle>
          <CardDescription>
            Ajoutez des images de coques avec fond transparent pour chaque modèle de téléphone
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone-model">Modèle de téléphone</Label>
              <Select 
                value={selectedModel} 
                onValueChange={setSelectedModel}
              >
                <SelectTrigger id="phone-model">
                  <SelectValue placeholder="Sélectionner un modèle" />
                </SelectTrigger>
                <SelectContent>
                  {phoneModels.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.brand} - {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="case-name">Nom de la coque</Label>
              <Input 
                id="case-name" 
                value={caseName} 
                onChange={(e) => setCaseName(e.target.value)} 
                placeholder="ex: Coque transparente iPhone 15 Pro"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="case-image">Image de la coque (PNG avec fond transparent)</Label>
            <div className="flex items-center gap-4">
              <Button 
                variant="outline" 
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2"
              >
                <Upload size={16} /> Télécharger une image
              </Button>
              <Input 
                id="case-image" 
                type="file" 
                ref={fileInputRef}
                className="hidden" 
                accept="image/png"
                onChange={handleFileUpload}
              />
              <span className="text-sm text-muted-foreground">
                Format recommandé: PNG transparent, 1000x2000px
              </span>
            </div>
          </div>
          
          {previewImage && (
            <div className="mt-4 border rounded-md p-4 bg-muted/20">
              <p className="text-sm font-medium mb-2">Aperçu :</p>
              <div className="relative h-60 w-40 mx-auto">
                <Image 
                  src={previewImage} 
                  alt="Aperçu de la coque" 
                  fill
                  style={{ objectFit: "contain" }}
                />
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          {editingCase && (
            <Button variant="outline" onClick={handleCancelEdit}>
              Annuler
            </Button>
          )}
          <Button onClick={handleSaveCase} className="ml-auto">
            {editingCase ? "Mettre à jour" : "Ajouter la coque"}
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Coques disponibles</CardTitle>
          <CardDescription>
            Liste des coques transparentes pour personnalisation
          </CardDescription>
        </CardHeader>
        <CardContent>
          {cases.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              Aucune coque n'a été ajoutée. Commencez par en ajouter une.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Aperçu</TableHead>
                  <TableHead>Nom</TableHead>
                  <TableHead>Modèle</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {cases.map((phoneCase) => {
                  const model = phoneModels.find(m => m.id === phoneCase.modelId);
                  return (
                    <TableRow key={phoneCase.id}>
                      <TableCell>
                        <div className="relative h-16 w-12">
                          <Image 
                            src={phoneCase.imageUrl} 
                            alt={phoneCase.name} 
                            fill
                            style={{ objectFit: "contain" }}
                          />
                        </div>
                      </TableCell>
                      <TableCell>{phoneCase.name}</TableCell>
                      <TableCell>{model ? `${model.brand} - ${model.name}` : "Inconnu"}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => handleEditCase(phoneCase)}
                          >
                            <Edit size={16} />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => handleDeleteCase(phoneCase.id)}
                            className="text-destructive hover:text-destructive/80"
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}