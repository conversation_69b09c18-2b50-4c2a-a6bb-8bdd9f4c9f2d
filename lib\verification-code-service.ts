import { supabase } from './supabase';
import { sendVerificationCode } from './email-service';

// Durée de validité du code en secondes (10 minutes)
const CODE_EXPIRY_SECONDS = 10 * 60;

/**
 * Génère un code de vérification à 6 chiffres
 * @returns string Code à 6 chiffres
 */
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * Stocke un code de vérification dans la base de données
 * @param email Email de l'utilisateur
 * @param code Code de vérification
 * @returns Promise<boolean> Indique si le code a été stocké avec succès
 */
export async function storeVerificationCode(email: string, code: string): Promise<boolean> {
  try {
    // Calculer la date d'expiration
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + CODE_EXPIRY_SECONDS);

    // Supprimer les anciens codes pour cet email
    await supabase
      .from('verification_codes')
      .delete()
      .eq('email', email);

    // Insérer le nouveau code
    const { error } = await supabase
      .from('verification_codes')
      .insert([
        {
          email,
          code,
          expires_at: expiresAt.toISOString(),
        },
      ]);

    if (error) {
      console.error('Erreur lors du stockage du code de vérification:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erreur lors du stockage du code de vérification:', error);
    return false;
  }
}

/**
 * Vérifie si un code de vérification est valide
 * @param email Email de l'utilisateur
 * @param code Code de vérification à vérifier
 * @returns Promise<boolean> Indique si le code est valide
 */
export async function verifyCode(email: string, code: string): Promise<boolean> {
  try {
    // Récupérer le code de vérification
    const { data, error } = await supabase
      .from('verification_codes')
      .select('*')
      .eq('email', email)
      .eq('code', code)
      .single();

    if (error || !data) {
      console.error('Erreur lors de la vérification du code ou code non trouvé:', error);
      return false;
    }

    // Vérifier si le code a expiré
    const expiresAt = new Date(data.expires_at);
    const now = new Date();

    if (now > expiresAt) {
      console.error('Le code de vérification a expiré');
      return false;
    }

    // Supprimer le code après utilisation
    await supabase
      .from('verification_codes')
      .delete()
      .eq('email', email);

    return true;
  } catch (error) {
    console.error('Erreur lors de la vérification du code:', error);
    return false;
  }
}

/**
 * Envoie un code de vérification par email
 * @param email Email de l'utilisateur
 * @returns Promise<boolean> Indique si le code a été envoyé avec succès
 */
export async function sendAndStoreVerificationCode(email: string): Promise<boolean> {
  try {
    // Générer un code
    const code = generateVerificationCode();

    // Stocker le code dans la base de données
    const stored = await storeVerificationCode(email, code);
    if (!stored) {
      return false;
    }

    // Envoyer le code par email
    const sent = await sendVerificationCode(email, code);
    return sent;
  } catch (error) {
    console.error('Erreur lors de l\'envoi du code de vérification:', error);
    return false;
  }
}

/**
 * Fonction de secours pour le développement
 * Stocke le code dans localStorage et simule l'envoi d'email
 * @param email Email de l'utilisateur
 * @returns Promise<boolean> Toujours true en développement
 */
export async function devSendAndStoreVerificationCode(email: string): Promise<boolean> {
  try {
    // Générer un code
    const code = generateVerificationCode();
    
    // En développement, utiliser un code fixe pour faciliter les tests
    const fixedCode = "03800643d";
    
    // Stocker le code dans localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('verificationCode', fixedCode);
      localStorage.setItem('verificationEmail', email);
      localStorage.setItem('verificationExpiry', (Date.now() + CODE_EXPIRY_SECONDS * 1000).toString());
    }
    
    // Simuler l'envoi d'email
    console.log(`[DEV] Code de vérification envoyé pour ${email}`);
    
    // Afficher une alerte en développement
    if (typeof window !== 'undefined') {
      alert(`Code de vérification envoyé à ${email}`);
    }
    
    return true;
  } catch (error) {
    console.error('Erreur lors de la simulation d\'envoi du code:', error);
    return false;
  }
}

/**
 * Fonction de secours pour le développement
 * Vérifie le code stocké dans localStorage
 * @param email Email de l'utilisateur
 * @param code Code de vérification à vérifier
 * @returns Promise<boolean> Indique si le code est valide
 */
export async function devVerifyCode(email: string, code: string): Promise<boolean> {
  try {
    if (typeof window === 'undefined') {
      return false;
    }
    
    if (typeof window === 'undefined') {
      return false;
    }
    
    const storedCode = localStorage.getItem('verificationCode');
    const storedEmail = localStorage.getItem('verificationEmail');
    const expiryStr = localStorage.getItem('verificationExpiry');
    
    if (!storedCode || !storedEmail || !expiryStr) {
      return false;
    }
    
    // Vérifier l'email
    if (storedEmail !== email) {
      return false;
    }
    
    // Vérifier l'expiration
    const expiry = parseInt(expiryStr, 10);
    if (Date.now() > expiry) {
      return false;
    }
    
    // Vérifier le code
    const isValid = code === storedCode;
    
    // Nettoyer le localStorage si le code est valide
    if (isValid) {
      localStorage.removeItem('verificationCode');
      localStorage.removeItem('verificationEmail');
      localStorage.removeItem('verificationExpiry');
    }
    
    return isValid;
  } catch (error) {
    console.error('Erreur lors de la vérification du code en développement:', error);
    return false;
  }
}
