/**
 * Script pour créer tous les dossiers d'images nécessaires
 */

const fs = require('fs');
const path = require('path');

// Liste des dossiers à créer
const folders = [
  // Coques de téléphone
  'public/images/phone-cases/transparent',
  'public/images/phone-cases/magsafe',
  
  // Bannières
  'public/images/banners/home',
  'public/images/banners/promo',
  
  // Témoignages
  'public/images/testimonials',
  
  // Designs
  'public/images/designs/abstraits',
  'public/images/designs/nature',
  'public/images/designs/animaux',
  'public/images/designs/motifs',
  'public/images/designs/personnalises',
  'public/images/designs/sports',
  'public/images/designs/musique',
  'public/images/designs/citations',
  
  // Galerie
  'public/images/gallery/vagues-abstraites',
  'public/images/gallery/fleurs-tropicales',
  'public/images/gallery/galaxie-cosmique',
  'public/images/gallery/marbre-elegant',
  'public/images/gallery/retro-synthwave',
  'public/images/gallery/montagnes-minimalistes',
  'public/images/gallery/motif-geometrique',
  'public/images/gallery/neon-urbain',
  'public/images/gallery/mandala-zen',
  'public/images/gallery/animaux-polygonaux',
  'public/images/gallery/typographie-creative',
  
  // QR codes
  'public/images/qr-codes',
  
  // Méthodes de paiement
  'public/images/payments/wave/logo',
  'public/images/payments/wave/qr',
  'public/images/payments/orange/logo',
  'public/images/payments/orange/qr',
  
  // Modèles de téléphone
  'public/images/phone-models',
  
  // Marques
  'public/images/brands',
  
  // Produits
  'public/images/products/personnalisation',
  'public/images/products/coques',
  'public/images/products/accessoires',
  
  // Promotions
  'public/images/promos/saisonniere',
  'public/images/promos/packs',
  'public/images/promos/nouveaux-clients',
];

// Fonction pour créer un dossier s'il n'existe pas
function createFolder(folderPath) {
  const fullPath = path.join(process.cwd(), folderPath);
  
  if (!fs.existsSync(fullPath)) {
    try {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`✅ Dossier créé: ${folderPath}`);
      
      // Créer un fichier .gitkeep pour s'assurer que le dossier est inclus dans Git
      const gitkeepPath = path.join(fullPath, '.gitkeep');
      fs.writeFileSync(gitkeepPath, '');
      console.log(`   - Fichier .gitkeep ajouté`);
      
      return true;
    } catch (error) {
      console.error(`❌ Erreur lors de la création du dossier ${folderPath}:`, error);
      return false;
    }
  } else {
    console.log(`ℹ️ Le dossier existe déjà: ${folderPath}`);
    
    // Vérifier si le fichier .gitkeep existe, sinon le créer
    const gitkeepPath = path.join(fullPath, '.gitkeep');
    if (!fs.existsSync(gitkeepPath)) {
      fs.writeFileSync(gitkeepPath, '');
      console.log(`   - Fichier .gitkeep ajouté`);
    }
    
    return true;
  }
}

// Créer tous les dossiers
console.log('Création des dossiers d\'images...\n');

let successCount = 0;
let errorCount = 0;

for (const folder of folders) {
  if (createFolder(folder)) {
    successCount++;
  } else {
    errorCount++;
  }
}

console.log('\nRésumé:');
console.log(`- ${successCount} dossiers créés ou déjà existants`);
console.log(`- ${errorCount} erreurs`);

if (errorCount === 0) {
  console.log('\n✅ Tous les dossiers ont été créés avec succès!');
} else {
  console.log('\n⚠️ Certains dossiers n\'ont pas pu être créés. Vérifiez les erreurs ci-dessus.');
}
