"use client"

import { useCartStore, CartProduct } from "@/lib/store/cart-store"
import { useToast } from "@/components/ui/use-toast"

export function useSimpleCart() {
  const cartStore = useCartStore()
  const { toast } = useToast()
  
  // Ajouter un produit au panier
  const addToCart = (product: Omit<CartProduct, "quantity">, quantity: number = 1) => {
    try {
      cartStore.addItem(product, quantity)
      
      toast({
        title: "Produit ajouté",
        description: `${product.name} a été ajouté à votre panier.`,
      })
      
      return true
    } catch (error) {
      console.error("Erreur lors de l'ajout au panier:", error)
      
      toast({
        title: "Erreur",
        description: "Impossible d'ajouter ce produit au panier.",
        variant: "destructive"
      })
      
      return false
    }
  }
  
  // Mettre à jour la quantité d'un produit
  const updateQuantity = (productId: string, quantity: number) => {
    try {
      cartStore.updateItem(productId, quantity)
      return true
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la quantité:", error)
      
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour la quantité.",
        variant: "destructive"
      })
      
      return false
    }
  }
  
  // Supprimer un produit du panier
  const removeFromCart = (productId: string) => {
    try {
      cartStore.removeItem(productId)
      
      toast({
        title: "Produit supprimé",
        description: "Le produit a été retiré de votre panier.",
      })
      
      return true
    } catch (error) {
      console.error("Erreur lors de la suppression du panier:", error)
      
      toast({
        title: "Erreur",
        description: "Impossible de supprimer ce produit.",
        variant: "destructive"
      })
      
      return false
    }
  }
  
  // Vider le panier
  const clearCart = () => {
    try {
      cartStore.clearCart()
      
      toast({
        title: "Panier vidé",
        description: "Votre panier a été vidé avec succès.",
      })
      
      return true
    } catch (error) {
      console.error("Erreur lors du vidage du panier:", error)
      
      toast({
        title: "Erreur",
        description: "Impossible de vider votre panier.",
        variant: "destructive"
      })
      
      return false
    }
  }
  
  return {
    items: cartStore.items,
    subtotal: cartStore.subtotal,
    shippingCost: cartStore.shippingCost,
    total: cartStore.total,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart
  }
}
