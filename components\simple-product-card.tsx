"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Heart, ShoppingCart, Star } from "lucide-react"
import Link from "next/link"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import { useSimpleFavorites } from "@/hooks/use-simple-favorites"
import Image from "next/image"

interface Product {
  id: string | number
  name: string
  price: number
  image: string
  description?: string
  rating?: number
  reviews?: number
  isNew?: boolean
  isBestseller?: boolean
  category?: string
  type?: string
}

interface ProductCardProps {
  product: Product
  viewMode?: "grid" | "list"
}

// Composant ProductCard simplifié
export default function SimpleProductCard({ product, viewMode = "grid" }: ProductCardProps) {
  const [isAdding, setIsAdding] = useState(false)
  const [showDescription, setShowDescription] = useState(false)
  const { addToCart } = useSimpleCart()
  const { toggleFavorite, isFavorite } = useSimpleFavorites()
  
  // Convertir l'ID en string si ce n'est pas déjà le cas
  const productId = String(product.id)
  
  // Vérifier si le produit est dans les favoris
  const isLiked = isFavorite(productId)
  
  // Gérer le clic sur le bouton favoris
  const handleToggleFavorite = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }
    
    toggleFavorite({
      id: productId,
      name: product.name,
      price: product.price,
      image_url: product.image
    })
  }
  
  // Gérer le clic sur le bouton ajouter au panier
  const handleAddToCart = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }
    
    setIsAdding(true)
    
    try {
      addToCart({
        id: productId,
        name: product.name,
        price: product.price,
        image_url: product.image,
        customized: false
      }, 1)
    } finally {
      setIsAdding(false)
    }
  }

  // Gérer le clic sur l'image
  const handleImageClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setShowDescription(!showDescription)
  }
  
  // Formater le prix
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF'
    }).format(price)
  }
  
  // Rendu en mode grille
  if (viewMode === "grid") {
    return (
      <Card className="overflow-hidden transition-all duration-200 hover:shadow-md flex flex-col h-full">
        {/* Section Image - 80% de l'espace */}
        <div className="relative w-full flex-[4]">
          <div className="relative h-64 overflow-hidden" onClick={handleImageClick}>
            {!showDescription ? (
              <Image
                src={product.image || "/images/products/placeholder.png"}
                alt={product.name}
                fill
                className="object-cover transition-transform duration-300"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : (
              <div className="absolute inset-0 bg-purple-600 flex items-center justify-center p-4">
                <p className="text-white text-sm text-center">
                  {product.description || "Découvrez ce produit unique et personnalisable."}
                </p>
              </div>
            )}
            
            {/* Seuls les badges et bouton favoris sont en overlay sur l'image */}
            <div className="absolute top-2 right-2 flex flex-col gap-1">
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 rounded-full bg-white/90 backdrop-blur-sm hover:bg-white shadow-sm"
                onClick={handleToggleFavorite}
              >
                <Heart className={`h-4 w-4 ${isLiked ? "fill-red-500 text-red-500" : "text-gray-600"}`} />
              </Button>
            </div>
            
            {product.isNew && (
              <Badge className="absolute top-2 left-2 bg-blue-500 shadow-sm">Nouveau</Badge>
            )}
            
            {product.isBestseller && (
              <Badge className="absolute top-2 left-2 bg-amber-500 shadow-sm">Populaire</Badge>
            )}
          </div>
        </div>
        
        {/* Section Contenu - 20% de l'espace */}
        <CardContent className="p-2 flex-1 flex flex-col justify-between min-h-0">
          <div className="space-y-1">
            <Link href={`/products/${productId}`}>
              <h3 className="font-medium line-clamp-1 text-xs leading-tight hover:text-purple-600">{product.name}</h3>
            </Link>
            <div className="flex items-center justify-between">
              <span className="font-bold text-purple-600 text-sm">{formatPrice(product.price)}</span>
              {product.rating && (
                <div className="flex items-center text-xs text-amber-500">
                  <Star className="h-3 w-3 fill-current" />
                  <span className="ml-1">{product.rating.toFixed(1)}</span>
                </div>
              )}
            </div>
          </div>
          
          <Button
            className="mt-1 w-full bg-purple-600 hover:bg-purple-700 h-7 text-xs"
            size="sm"
            onClick={handleAddToCart}
            disabled={isAdding}
          >
            {isAdding ? (
              <span className="flex items-center">
                <svg className="mr-2 h-4 w-4 animate-spin" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Ajout...
              </span>
            ) : (
              <span className="flex items-center">
                <ShoppingCart className="mr-2 h-4 w-4" />
                Ajouter au panier
              </span>
            )}
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  // Rendu en mode liste
  return (
    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
      <div className="flex flex-col md:flex-row">
        <div className="relative md:w-1/3" onClick={handleImageClick}>
          <div className="relative aspect-[4/3] overflow-hidden">
            {!showDescription ? (
              <Image
                src={product.image || "/images/products/placeholder.png"}
                alt={product.name}
                fill
                className="object-cover transition-transform duration-300"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : (
              <div className="absolute inset-0 bg-purple-600 flex items-center justify-center p-4">
                <p className="text-white text-sm text-center">
                  {product.description || "Découvrez ce produit unique et personnalisable."}
                </p>
              </div>
            )}
            
            {product.isNew && (
              <Badge className="absolute top-2 left-2 bg-blue-500">Nouveau</Badge>
            )}
            
            {product.isBestseller && (
              <Badge className="absolute top-2 left-2 bg-amber-500">Populaire</Badge>
            )}
          </div>
        </div>
        
        <CardContent className="flex-1 p-4">
          <div className="flex justify-between items-start mb-2">
            <div>
              <Link href={`/products/${productId}`}>
                <h3 className="font-medium hover:text-purple-600">{product.name}</h3>
              </Link>
              {product.rating && (
                <div className="flex items-center text-amber-500 mt-1">
                  <Star className="h-4 w-4 fill-current" />
                  <span className="ml-1">{product.rating.toFixed(1)}</span>
                  {product.reviews && (
                    <span className="text-gray-500 text-sm ml-1">({product.reviews} avis)</span>
                  )}
                </div>
              )}
            </div>
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 rounded-full"
              onClick={handleToggleFavorite}
            >
              <Heart className={`h-4 w-4 ${isLiked ? "fill-red-500 text-red-500" : "text-gray-600"}`} />
            </Button>
          </div>
          
          <p className="text-gray-600 text-sm mb-4">{product.description}</p>
          
          <div className="flex items-center justify-between">
            <span className="font-bold text-purple-600">{formatPrice(product.price)}</span>
            <Button
              className="bg-purple-600 hover:bg-purple-700"
              onClick={handleAddToCart}
              disabled={isAdding}
            >
              {isAdding ? (
                <span className="flex items-center">
                  <svg className="mr-2 h-4 w-4 animate-spin" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  Ajout...
                </span>
              ) : (
                <span className="flex items-center">
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  Ajouter au panier
                </span>
              )}
            </Button>
          </div>
        </CardContent>
      </div>
    </Card>
  )
}
