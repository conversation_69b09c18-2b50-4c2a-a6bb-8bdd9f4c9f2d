-- Migration pour mettre à jour les politiques de sécurité de la table cart_items

-- Modifier la table cart_items pour permettre les utilisateurs anonymes
ALTER TABLE public.cart_items 
DROP CONSTRAINT IF EXISTS cart_items_user_id_fkey;

-- Ajouter une nouvelle contrainte qui permet les IDs anonymes
ALTER TABLE public.cart_items
ADD CONSTRAINT cart_items_user_id_fkey
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
DEFERRABLE INITIALLY DEFERRED;

-- Ajouter des politiques pour les utilisateurs anonymes
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs anonymes peuvent voir leurs articles du panier') THEN
    CREATE POLICY "Les utilisateurs anonymes peuvent voir leurs articles du panier"
    ON public.cart_items FOR SELECT
    USING (true);
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs anonymes peuvent ajouter des articles au panier') THEN
    CREATE POLICY "Les utilisateurs anonymes peuvent ajouter des articles au panier"
    ON public.cart_items FOR INSERT
    WITH CHECK (true);
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs anonymes peuvent mettre à jour leurs articles du panier') THEN
    CREATE POLICY "Les utilisateurs anonymes peuvent mettre à jour leurs articles du panier"
    ON public.cart_items FOR UPDATE
    USING (true)
    WITH CHECK (true);
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs anonymes peuvent supprimer leurs articles du panier') THEN
    CREATE POLICY "Les utilisateurs anonymes peuvent supprimer leurs articles du panier"
    ON public.cart_items FOR DELETE
    USING (true);
  END IF;
END
$$;
