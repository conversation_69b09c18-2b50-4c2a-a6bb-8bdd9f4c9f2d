import { Card, CardContent } from "@/components/ui/card"
import { Star } from "lucide-react"

// Sample testimonials data
const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=100&width=100",
    rating: 5,
    text: "J'ai créé une coque avec une photo de mes enfants, la qualité est exceptionnelle et la livraison a été très rapide. Je recommande vivement !",
  },
  {
    id: 2,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=100&width=100",
    rating: 5,
    text: "Interface super intuitive et résultat final au top. Ma coque personnalisée est exactement comme je l'imaginais.",
  },
  {
    id: 3,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=100&width=100",
    rating: 4,
    text: "Très satisfaite de ma commande. Le rendu des couleurs est fidèle à la prévisualisation et la coque est de bonne qualité.",
  },
]

export default function Testimonials() {
  return (
    <div className="grid md:grid-cols-3 gap-6">
      {testimonials.map((testimonial) => (
        <Card key={testimonial.id} className="overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                <img
                  src={testimonial.avatar || "/placeholder.svg"}
                  alt={testimonial.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h4 className="font-semibold">{testimonial.name}</h4>
                <div className="flex">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4"
                      fill={i < testimonial.rating ? "currentColor" : "none"}
                      color={i < testimonial.rating ? "#9333ea" : "#d1d5db"}
                    />
                  ))}
                </div>
              </div>
            </div>
            <p className="text-gray-600 italic">"{testimonial.text}"</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
