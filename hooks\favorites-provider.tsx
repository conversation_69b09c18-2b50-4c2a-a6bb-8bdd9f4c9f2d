"use client"

import React, { createContext, useContext, ReactNode } from "react"
import { useFavorites, FavoriteProduct } from "@/hooks/use-favorites"

// Type pour le contexte des favoris
type FavoritesContextType = {
  favorites: FavoriteProduct[]
  isLoading: boolean
  addFavorite: (product: FavoriteProduct) => void
  removeFavorite: (productId: string) => void
  toggleFavorite: (product: FavoriteProduct) => void
}

// Créer le contexte
const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined)

// Provider pour les favoris
export function FavoritesProvider({ children }: { children: ReactNode }) {
  // Utiliser le hook Zustand
  const favoritesHook = useFavorites()
  
  return (
    <FavoritesContext.Provider value={favoritesHook}>
      {children}
    </FavoritesContext.Provider>
  )
}

// Hook pour utiliser le contexte des favoris
export function useFavoritesContext() {
  const context = useContext(FavoritesContext)
  
  if (context === undefined) {
    throw new Error("useFavoritesContext doit être utilisé à l'intérieur d'un FavoritesProvider")
  }
  
  return context
}
