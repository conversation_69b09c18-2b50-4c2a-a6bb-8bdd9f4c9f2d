"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ExternalLink } from "lucide-react"

interface WavePaymentProps {
  amount: number
  onPaymentComplete: () => void
}

export default function WavePayment({ amount, onPaymentComplete }: WavePaymentProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  
  // Détecter si l'utilisateur est sur mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])
  
  // Formater le montant pour l'affichage
  const formattedAmount = new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'XOF'
  }).format(amount)
  
  // URL de paiement Wave
  const wavePaymentUrl = "https://pay.wave.com/m/M_oLBggfv-S8aE/c/ci/"
  
  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Image 
            src="/images/payments/wave/logo/wave-logo.png" 
            alt="Wave" 
            width={24} 
            height={24} 
          />
          Payer avec Wave
        </CardTitle>
        <CardDescription>
          {isMobile ? "Cliquez sur le bouton pour payer" : "Scannez le QR code pour payer"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center">
          {!isMobile && (
            <div className="bg-white p-4 rounded-lg mb-4">
              <Image 
                src="/images/payments/wave/qr/wave-qr.png" 
                alt="QR Code Wave" 
                width={200} 
                height={200} 
                className="mx-auto"
              />
            </div>
          )}
          <p className="text-center mb-4">
            Montant à payer: <strong>{formattedAmount}</strong>
          </p>
          {isMobile ? (
            <Button 
              className="flex items-center gap-2 w-full"
              onClick={() => window.open(wavePaymentUrl, '_blank')}
            >
              Payer avec Wave <ExternalLink size={16} />
            </Button>
          ) : (
            <Button 
              variant="outline" 
              className="flex items-center gap-2"
              onClick={() => window.open(wavePaymentUrl, '_blank')}
            >
              Ouvrir le lien Wave <ExternalLink size={16} />
            </Button>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex flex-col items-start pt-0">
        <Button 
          variant="link" 
          className="px-0 text-sm"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? "Masquer les instructions" : "Afficher les instructions"}
        </Button>
        
        {isExpanded && (
          <div className="text-sm mt-2 space-y-2">
            <p><strong>Comment payer avec Wave:</strong></p>
            <ol className="list-decimal pl-5 space-y-1">
              <li>Scannez le QR code avec l'application Wave</li>
              <li>Vérifiez le montant: {formattedAmount}</li>
              <li>Confirmez le paiement dans votre application</li>
              <li>Une fois le paiement effectué, cliquez sur "J'ai payé"</li>
            </ol>
          </div>
        )}
      </CardFooter>
    </Card>
  )
}
