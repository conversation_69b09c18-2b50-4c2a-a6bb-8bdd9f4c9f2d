# Script pour vérifier et corriger les chemins d'images dans le code

# Fonction pour rechercher des références à "Galerie" dans les fichiers
function Find-References-In-Files {
    param (
        [string]$searchPattern,
        [string]$fileExtension,
        [string]$rootPath = "."
    )

    Write-Host "Recherche de '$searchPattern' dans les fichiers $fileExtension..." -ForegroundColor Yellow

    $files = Get-ChildItem -Path $rootPath -Recurse -Include "*.$fileExtension" -File
    $foundReferences = @()

    foreach ($file in $files) {
        $content = [string]::Join("`n", (Get-Content -Path $file.FullName))
        if ($content -match $searchPattern) {
            $foundReferences += @{
                FilePath = $file.FullName
                FileName = $file.Name
                MatchCount = ([regex]::Matches($content, $searchPattern)).Count
            }
        }
    }

    return $foundReferences
}

# Rechercher des références à "Galerie" dans les fichiers
$searchPattern = "/images/Galerie"
$fileExtensions = @("tsx", "jsx", "ts", "js", "md")
$rootPath = "."

$allReferences = @()

foreach ($ext in $fileExtensions) {
    $references = Find-References-In-Files -searchPattern $searchPattern -fileExtension $ext -rootPath $rootPath
    $allReferences += $references
}

# Afficher les résultats
if ($allReferences.Count -gt 0) {
    Write-Host "`nRéférences trouvées à '$searchPattern':" -ForegroundColor Red

    foreach ($ref in $allReferences) {
        Write-Host "Fichier: $($ref.FilePath)" -ForegroundColor Cyan
        Write-Host "  Nombre de correspondances: $($ref.MatchCount)" -ForegroundColor Yellow
    }

    # Demander si l'utilisateur souhaite corriger les références
    Write-Host "`nVoulez-vous corriger ces références? (O/N)" -ForegroundColor Green
    $response = "O" # Automatiquement oui pour ce script

    if ($response -eq "O" -or $response -eq "o") {
        Write-Host "Correction des références..." -ForegroundColor Green

        foreach ($ref in $allReferences) {
            $content = [string]::Join("`n", (Get-Content -Path $ref.FilePath))
            $newContent = $content -replace "/images/Galerie", "/images/gallery"

            # Écrire le contenu modifié dans le fichier
            Set-Content -Path $ref.FilePath -Value $newContent

            Write-Host "Fichier corrigé: $($ref.FilePath)" -ForegroundColor Green
        }

        Write-Host "`nToutes les références ont été corrigées!" -ForegroundColor Green
    }
    else {
        Write-Host "Aucune correction n'a été effectuée." -ForegroundColor Yellow
    }
}
else {
    Write-Host "`nAucune référence à '$searchPattern' n'a été trouvée." -ForegroundColor Green
}

# Rechercher des références à "/placeholder.svg" dans les fichiers
$searchPattern = "/placeholder.svg"
$allReferences = @()

foreach ($ext in $fileExtensions) {
    $references = Find-References-In-Files -searchPattern $searchPattern -fileExtension $ext -rootPath $rootPath
    $allReferences += $references
}

# Afficher les résultats
if ($allReferences.Count -gt 0) {
    Write-Host "`nRéférences trouvées à '$searchPattern':" -ForegroundColor Yellow

    foreach ($ref in $allReferences) {
        Write-Host "Fichier: $($ref.FilePath)" -ForegroundColor Cyan
        Write-Host "  Nombre de correspondances: $($ref.MatchCount)" -ForegroundColor Yellow
    }

    Write-Host "`nCes références peuvent nécessiter une attention particulière." -ForegroundColor Yellow
    Write-Host "Vérifiez si ces placeholders doivent être remplacés par de vraies images." -ForegroundColor Yellow
}
else {
    Write-Host "`nAucune référence à '$searchPattern' n'a été trouvée." -ForegroundColor Green
}

Write-Host "`nVérification terminée!" -ForegroundColor Green
