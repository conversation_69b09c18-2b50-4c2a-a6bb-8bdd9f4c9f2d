"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, Grid, List, ArrowRight, Coffee, ShoppingBag, MousePointer, Home, Key, Star, StarHalf } from "lucide-react"
import Image from "next/image"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
// Importation du composant ProductCard
import ProductCard from "@/components/product-card"

// Types pour les produits
interface Product {
  id: number
  description?: string // Ajout de la description optionnelle
  name: string
  price: number
  image: string
  rating: number
  reviews: number
  isNew: boolean
  isBestseller: boolean
  category: string
  type: string
  colors: string[]
  collections: string[]
}

// Données fictives pour les produits
const products: Product[] = [
  // Tasses
  {
    id: 1,
    name: "Tasse Personnalisable Classique",
    price: 7990,
    image: "/images/products/mugs/classic-mug/classic-mug.png",
    rating: 4.7,
    reviews: 128,
    isNew: true,
    isBestseller: true,
    category: "tasses",
    type: "Céramique",
    colors: ["Blanc", "Noir"],
    collections: ["Classique"],
    description: "Une tasse classique en céramique, parfaite pour votre café du matin. Personnalisez-la avec votre design préféré."
  },
  // Tasses
  {
    id: 1,
    name: "Tasse Personnalisable Classique",
    price: 7990,
    image: "/images/products/mugs/classic-mug/classic-mug.png",
    rating: 4.7,
    reviews: 128,
    isNew: true,
    isBestseller: true,
    category: "tasses",
    type: "Céramique",
    colors: ["Blanc", "Noir"],
    collections: ["Classique"],
  },
  {
    id: 2,
    name: "Tasse Magique Thermosensible",
    price: 9990,
    image: "/images/products/mugs/magic-mug/magic-mug.png",
    rating: 4.5,
    reviews: 86,
    isNew: true,
    isBestseller: false,
    category: "tasses",
    type: "Thermosensible",
    colors: ["Noir", "Bleu"],
    collections: ["Premium"],
  },
  {
    id: 3,
    name: "Tasse Isotherme Inox",
    price: 12990,
    image: "/images/products/mugs/isothermal-mug/isothermal-mug.png",
    rating: 4.8,
    reviews: 210,
    isNew: false,
    isBestseller: true,
    category: "tasses",
    type: "Isotherme",
    colors: ["Argent", "Noir", "Blanc"],
    collections: ["Premium"],
  },

  // T-shirts
  {
    id: 4,
    name: "T-shirt Personnalisable Coton",
    price: 8990,
    image: "/images/products/tshirts/cotton-tshirt/cotton-tshirt.png",
    rating: 4.6,
    reviews: 156,
    isNew: false,
    isBestseller: true,
    category: "tshirts",
    type: "Coton",
    colors: ["Blanc", "Noir", "Gris", "Bleu"],
    collections: ["Basique"],
  },
  {
    id: 5,
    name: "T-shirt Premium Impression Totale",
    price: 12990,
    image: "/images/products/tshirts/premium-tshirt/premium-tshirt.png",
    rating: 4.9,
    reviews: 78,
    isNew: true,
    isBestseller: false,
    category: "tshirts",
    type: "Premium",
    colors: ["Blanc"],
    collections: ["Premium"],
  },
  {
    id: 6,
    name: "Polo Brodé Personnalisable",
    price: 14990,
    image: "/images/products/tshirts/polo-shirt/polo-shirt.png",
    rating: 4.7,
    reviews: 92,
    isNew: false,
    isBestseller: false,
    category: "tshirts",
    type: "Polo",
    colors: ["Blanc", "Noir", "Bleu marine"],
    collections: ["Business"],
  },

  // Tapis de souris
  {
    id: 7,
    name: "Tapis de Souris Standard",
    price: 4990,
    image: "/images/products/mousepads/standard-mousepad/standard-mousepad.png",
    rating: 4.5,
    reviews: 112,
    isNew: false,
    isBestseller: true,
    category: "tapis",
    type: "Standard",
    colors: ["Noir"],
    collections: ["Basique"],
  },
  {
    id: 8,
    name: "Tapis de Souris XXL Gamer",
    price: 9990,
    image: "/images/products/mousepads/xxl-gamer-mousepad/xxl-gamer-mousepad.png",
    rating: 4.8,
    reviews: 145,
    isNew: true,
    isBestseller: true,
    category: "tapis",
    type: "XXL",
    colors: ["Noir", "Rouge"],
    collections: ["Gaming"],
  },
  {
    id: 9,
    name: "Tapis de Souris avec Repose-Poignet",
    price: 7990,
    image: "/images/products/mousepads/ergonomic-mousepad/ergonomic-mousepad.png",
    rating: 4.6,
    reviews: 87,
    isNew: false,
    isBestseller: false,
    category: "tapis",
    type: "Ergonomique",
    colors: ["Noir", "Bleu"],
    collections: ["Confort"],
  },

  // Coussins
  {
    id: 10,
    name: "Coussin Décoratif Personnalisable",
    price: 11990,
    image: "/images/products/cushions/decorative-cushion/decorative-cushion.png",
    rating: 4.7,
    reviews: 76,
    isNew: true,
    isBestseller: false,
    category: "coussins",
    type: "Décoratif",
    colors: ["Blanc", "Beige", "Gris"],
    collections: ["Maison"],
  },
  {
    id: 11,
    name: "Coussin Photo Recto-Verso",
    price: 14990,
    image: "/images/products/cushions/photo-cushion/photo-cushion.png",
    rating: 4.9,
    reviews: 58,
    isNew: true,
    isBestseller: true,
    category: "coussins",
    type: "Photo",
    colors: ["Blanc"],
    collections: ["Premium"],
  },
  {
    id: 12,
    name: "Coussin de Sol XXL",
    price: 19990,
    image: "/images/products/cushions/xxl-cushion/xxl-cushion.png",
    rating: 4.8,
    reviews: 42,
    isNew: false,
    isBestseller: false,
    category: "coussins",
    type: "XXL",
    colors: ["Gris", "Bleu", "Beige"],
    collections: ["Confort"],
  },

  // Porte-clés
  {
    id: 13,
    name: "Porte-clé Photo Personnalisable",
    price: 3990,
    image: "/images/products/keychains/photo-keychain/photo-keychain.png",
    rating: 4.6,
    reviews: 198,
    isNew: false,
    isBestseller: true,
    category: "portecles",
    type: "Photo",
    colors: ["Transparent"],
    collections: ["Basique"],
  },
  {
    id: 14,
    name: "Porte-clé Métal Gravé",
    price: 5990,
    image: "/images/products/keychains/metal-keychain/metal-keychain.png",
    rating: 4.8,
    reviews: 124,
    isNew: true,
    isBestseller: false,
    category: "portecles",
    type: "Métal",
    colors: ["Argent", "Or"],
    collections: ["Premium"],
  },
  {
    id: 15,
    name: "Porte-clé Multifonction",
    price: 6990,
    image: "/images/products/keychains/multifunction-keychain/multifunction-keychain.png",
    rating: 4.7,
    reviews: 86,
    isNew: false,
    isBestseller: true,
    category: "portecles",
    type: "Multifonction",
    colors: ["Noir", "Rouge", "Bleu"],
    collections: ["Pratique"],
  },
]

// Catégories de produits
const categories = [
  { id: "all", name: "Tous les produits", icon: <Grid className="h-4 w-4" /> },
  { id: "tasses", name: "Tasses", icon: <Coffee className="h-4 w-4" /> },
  { id: "tshirts", name: "T-shirts", icon: <ShoppingBag className="h-4 w-4" /> },
  { id: "tapis", name: "Tapis de souris", icon: <MousePointer className="h-4 w-4" /> },
  { id: "coussins", name: "Coussins", icon: <Home className="h-4 w-4" /> },
  { id: "portecles", name: "Porte-clés", icon: <Key className="h-4 w-4" /> },
]

// Types de produits pour le filtrage
const productTypes = {
  tasses: ["Céramique", "Thermosensible", "Isotherme", "Émaillée"],
  tshirts: ["Coton", "Premium", "Polo", "Manches longues"],
  tapis: ["Standard", "XXL", "Ergonomique", "RGB"],
  coussins: ["Décoratif", "Photo", "XXL", "Cervical"],
  portecles: ["Photo", "Métal", "Multifonction", "Bois"],
}

// Couleurs pour le filtrage
const colors = [
  "Blanc", "Noir", "Gris", "Bleu", "Rouge", "Vert", "Rose",
  "Marron", "Argent", "Or", "Beige", "Transparent", "Multicolore"
]

// Collections pour le filtrage
const collections = [
  "Basique", "Premium", "Business", "Gaming", "Confort", "Maison", "Pratique"
]

const formatPrice = (price: number) => {
  return new Intl.NumberFormat("fr-CI", { style: "currency", currency: "XOF" }).format(price)
}

export default function ProductsPage() {
  // États pour les filtres et la recherche
  const [activeCategory, setActiveCategory] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState("popularity")
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedColors, setSelectedColors] = useState<string[]>([])
  const [selectedCollections, setSelectedCollections] = useState<string[]>([])
  const [priceRange, setPriceRange] = useState([0, 20000])

  // Filtrer les produits en fonction des critères
  const filteredProducts = products.filter((product) => {
    // Filtre par catégorie
    if (activeCategory !== "all" && product.category !== activeCategory) {
      return false
    }

    // Filtre par type
    if (selectedTypes.length > 0 && !selectedTypes.includes(product.type)) {
      return false
    }

    // Filtre par couleur
    if (selectedColors.length > 0 && !product.colors.some(color => selectedColors.includes(color))) {
      return false
    }

    // Filtre par collection
    if (selectedCollections.length > 0 && !product.collections.some(collection => selectedCollections.includes(collection))) {
      return false
    }

    // Filtre par prix
    if (product.price < priceRange[0] || product.price > priceRange[1]) {
      return false
    }

    // Filtre par recherche
    if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }

    return true
  })

  // Trier les produits
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "price-asc":
        return a.price - b.price
      case "price-desc":
        return b.price - a.price
      case "rating":
        return b.rating - a.rating
      case "newest":
        return a.isNew ? -1 : b.isNew ? 1 : 0
      case "popularity":
      default:
        return b.reviews - a.reviews
    }
  })

  // Réinitialiser tous les filtres
  const resetFilters = () => {
    setSelectedTypes([])
    setSelectedColors([])
    setSelectedCollections([])
    setPriceRange([0, 20000])
    setSearchQuery("")
  }

  // Formater le prix en FCFA
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + " FCFA"
  }

  // Composant pour les filtres
  const FiltersSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium mb-3">Types de produits</h3>
        <div className="space-y-2">
          {activeCategory !== "all" && productTypes[activeCategory as keyof typeof productTypes]?.map((type) => (
            <div key={type} className="flex items-center">
              <Checkbox
                id={`type-${type}`}
                checked={selectedTypes.includes(type)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedTypes([...selectedTypes, type])
                  } else {
                    setSelectedTypes(selectedTypes.filter((t) => t !== type))
                  }
                }}
              />
              <label htmlFor={`type-${type}`} className="ml-2 text-sm">
                {type}
              </label>
            </div>
          ))}
          {activeCategory === "all" && (
            <p className="text-sm text-gray-500">Sélectionnez une catégorie pour voir les types disponibles</p>
          )}
        </div>
      </div>

      <Separator />

      <div>
        <h3 className="font-medium mb-3">Prix</h3>
        <div className="space-y-4">
          <Slider
            value={priceRange}
            min={0}
            max={20000}
            step={1000}
            onValueChange={(value) => setPriceRange(value as [number, number])}
          />
          <div className="flex items-center justify-between">
            <span className="text-sm">{formatPrice(priceRange[0])}</span>
            <span className="text-sm">{formatPrice(priceRange[1])}</span>
          </div>
        </div>
      </div>

      <Separator />

      <div>
        <h3 className="font-medium mb-3">Couleurs</h3>
        <div className="grid grid-cols-2 gap-2">
          {colors.map((color) => (
            <div key={color} className="flex items-center">
              <Checkbox
                id={`color-${color}`}
                checked={selectedColors.includes(color)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedColors([...selectedColors, color])
                  } else {
                    setSelectedColors(selectedColors.filter((c) => c !== color))
                  }
                }}
              />
              <label htmlFor={`color-${color}`} className="ml-2 text-sm">
                {color}
              </label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      <div>
        <h3 className="font-medium mb-3">Collections</h3>
        <div className="space-y-2">
          {collections.map((collection) => (
            <div key={collection} className="flex items-center">
              <Checkbox
                id={`collection-${collection}`}
                checked={selectedCollections.includes(collection)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedCollections([...selectedCollections, collection])
                  } else {
                    setSelectedCollections(selectedCollections.filter((c) => c !== collection))
                  }
                }}
              />
              <label htmlFor={`collection-${collection}`} className="ml-2 text-sm">
                {collection}
              </label>
            </div>
          ))}
        </div>
      </div>

      <Button onClick={resetFilters} variant="outline" className="w-full">
        Réinitialiser les filtres
      </Button>
    </div>
  );

  const [clickedProductId, setClickedProductId] = useState<string | null>(null)

  return (
    <div className="container mx-auto px-4 py-8">
      {/* En-tête */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-2">🛍️ Nos Produits Personnalisables</h1>
        <p className="text-lg opacity-90">
          Découvrez notre gamme de produits personnalisables de haute qualité.
        </p>
      </div>

      {/* Tabs pour les catégories */}
      <Tabs value={activeCategory} onValueChange={setActiveCategory} className="mb-8">
        <TabsList className="grid grid-cols-3 md:grid-cols-6 mb-4">
          {categories.map((category) => (
            <TabsTrigger key={category.id} value={category.id} className="flex items-center">
              {category.icon}
              <span className="ml-2 hidden md:inline">{category.name}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {/* Contenu principal */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filtres (desktop) */}
          <div className="hidden lg:block">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-bold mb-4">Filtres</h2>
                <FiltersSection />
              </CardContent>
            </Card>
          </div>

          {/* Produits */}
          <div className="lg:col-span-3">
            {/* Barre de recherche et tri */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Input
                  placeholder="Rechercher..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              </div>

              <div className="flex items-center gap-2">
                {/* Bouton Filtres (mobile) */}
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="icon" className="lg:hidden">
                      <Filter className="h-4 w-4" />
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left">
                    <div className="py-4">
                      <h2 className="text-xl font-bold mb-4">Filtres</h2>
                      <FiltersSection />
                    </div>
                  </SheetContent>
                </Sheet>

                {/* Sélecteur de tri */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Trier par" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="popularity">Popularité</SelectItem>
                    <SelectItem value="price-asc">Prix croissant</SelectItem>
                    <SelectItem value="price-desc">Prix décroissant</SelectItem>
                    <SelectItem value="rating">Meilleures notes</SelectItem>
                    <SelectItem value="newest">Nouveautés</SelectItem>
                  </SelectContent>
                </Select>

                {/* Boutons de vue */}
                <div className="flex border rounded-md overflow-hidden">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="icon"
                    onClick={() => setViewMode("grid")}
                    className="rounded-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="icon"
                    onClick={() => setViewMode("list")}
                    className="rounded-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Affichage des produits */}
            {sortedProducts.length > 0 ? (
              <div className={viewMode === "grid" ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
                {sortedProducts.map((product, index) => (
                  <Card
                    key={`${product.id}-${index}`}
                    className={`overflow-hidden transition-all duration-300 hover:shadow-xl cursor-pointer ${clickedProductId === product.id.toString() ? 'ring-2 ring-purple-500' : ''}`}
                    onClick={() => setClickedProductId(clickedProductId === product.id.toString() ? null : product.id.toString())}
                  >
                    <CardContent className="p-0">
                      <div className="relative aspect-square">
                        {clickedProductId === product.id.toString() ? (
                          <div className="absolute inset-0 bg-purple-500 text-white p-4 flex flex-col justify-center items-center">
                            <h3 className="font-semibold text-center mb-2">{product.name}</h3>
                            <p className="text-xs text-center opacity-90 line-clamp-4">
                              {product.description || "Aucune description disponible."}
                            </p>
                          </div>
                        ) : (
                          <Image
                            src={product.image}
                            alt={product.name}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-cover transition-transform duration-300 group-hover:scale-105"
                          />
                        )}
                      </div>
                      <div className="p-4">
                        <h3 className="font-semibold truncate group-hover:text-purple-600 transition-colors">
                          <Link href={`/products/${product.id}`} className="hover:underline">
                            {product.name}
                          </Link>
                        </h3>
                        <div className="flex items-center justify-between mt-2">
                          <p className="text-lg font-bold text-purple-700">{formatPrice(product.price)}</p>
                          <div className="flex items-center">
                            {[...Array(Math.floor(product.rating))].map((_, i) => (
                              <Star key={`star-${i}`} className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                            ))}
                            {product.rating % 1 !== 0 && (
                              <StarHalf key="half-star" className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                            )}
                            <span className="text-xs text-gray-500 ml-1">({product.reviews})</span>
                          </div>
                        </div>
                        <Button 
                          variant="outline"
                          size="sm"
                          className="w-full mt-3"
                          onClick={(e) => {
                            e.stopPropagation() // Empêche le clic de se propager à la Card
                            // Logique d'ajout au panier
                            console.log(`Ajout au panier: ${product.name}`)
                          }}
                        >
                          Ajouter au panier
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <h3 className="text-xl font-medium mb-2">Aucun produit trouvé</h3>
                <p className="text-gray-500 mb-6">Essayez de modifier vos filtres pour voir plus de résultats.</p>
                <Button onClick={resetFilters}>Réinitialiser les filtres</Button>
              </div>
            )}

            {/* Lien vers la personnalisation */}
            <div className="mt-12 bg-gray-50 rounded-lg p-6 text-center">
              <h3 className="text-xl font-bold mb-2">Vous ne trouvez pas ce que vous cherchez ?</h3>
              <p className="text-gray-600 mb-4">
                Créez votre propre design personnalisé et appliquez-le sur n'importe quel produit.
              </p>
              <Link href="/customize">
                <Button className="bg-purple-600 hover:bg-purple-700">
                  Personnaliser un produit <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </Tabs>
    </div>
  );
}
