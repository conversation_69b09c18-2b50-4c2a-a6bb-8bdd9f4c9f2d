// Script pour appliquer les migrations Supabase
// Exécuter avec: node scripts/apply-migrations.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Vérifier les variables d'environnement
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Erreur: Variables d\'environnement manquantes.');
  console.error('Assurez-vous que NEXT_PUBLIC_SUPABASE_URL et SUPABASE_SERVICE_KEY sont définis dans .env.local');
  process.exit(1);
}

// Créer le client Supabase avec la clé de service
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Fonction pour appliquer une migration
async function applyMigration(filePath) {
  try {
    console.log(`Applying migration: ${path.basename(filePath)}`);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Exécuter le SQL
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error(`Erreur lors de l'application de la migration ${path.basename(filePath)}:`, error);
      return false;
    }
    
    console.log(`Migration ${path.basename(filePath)} appliquée avec succès.`);
    return true;
  } catch (error) {
    console.error(`Erreur lors de la lecture ou de l'exécution de la migration ${path.basename(filePath)}:`, error);
    return false;
  }
}

// Fonction principale
async function main() {
  console.log('Début de l\'application des migrations...');
  
  // Lire le dossier des migrations
  const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');
  
  try {
    // Vérifier si le dossier existe
    if (!fs.existsSync(migrationsDir)) {
      console.error(`Le dossier de migrations n'existe pas: ${migrationsDir}`);
      process.exit(1);
    }
    
    // Lire les fichiers de migration
    const files = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Trier par ordre alphabétique
    
    if (files.length === 0) {
      console.log('Aucun fichier de migration trouvé.');
      process.exit(0);
    }
    
    console.log(`${files.length} fichiers de migration trouvés.`);
    
    // Appliquer chaque migration
    let successCount = 0;
    for (const file of files) {
      const filePath = path.join(migrationsDir, file);
      const success = await applyMigration(filePath);
      if (success) {
        successCount++;
      }
    }
    
    console.log(`Migrations terminées. ${successCount}/${files.length} migrations appliquées avec succès.`);
    
  } catch (error) {
    console.error('Erreur lors de l\'application des migrations:', error);
    process.exit(1);
  }
}

// Exécuter le script
main().catch(error => {
  console.error('Erreur non gérée:', error);
  process.exit(1);
});
