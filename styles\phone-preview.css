.phone-container {
  width: 280px;
  height: 580px;
  position: relative;
  overflow: hidden; /* Empêche tout débordement */
  /* Proportions basées sur les dimensions réelles de l'iPhone 15 Pro */
  aspect-ratio: 70.6 / 146.6;
}

.phone-case {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Image de la coque (contour) */
.phone-case-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0px 10px 10px rgba(0, 0, 0, 0.15));
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10; /* Au-dessus de l'image personnalisée */
  pointer-events: none; /* Permet de cliquer à travers */
}

/* Conteneur pour l'image personnalisée - masqué par la forme de la coque */
.custom-image-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden; /* Empêche tout débordement */
  z-index: 5; /* En dessous de la coque */
  background-color: white; /* Fond blanc par défaut */
  /* Forme arrondie qui correspond à la coque */
  border-radius: 40px;
  /* Légèrement plus petit que la coque pour éviter les débordements */
  margin: 10px;
  /* Assure que rien ne sort du conteneur */
  clip-path: inset(0 0 0 0 round 40px);
}

/* Image personnalisée */
.custom-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain; /* Changé à contain pour éviter les débordements */
  transform-origin: center;
  max-width: 100%; /* Assure que l'image ne dépasse pas la largeur */
  max-height: 100%; /* Assure que l'image ne dépasse pas la hauteur */
}

/* Conteneur pour le texte personnalisé */
.custom-text-container {
  position: absolute;
  top: 22%;
  left: 5%;
  right: 5%;
  bottom: 5%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 6; /* Au-dessus de l'image mais en dessous de la coque */
  pointer-events: none; /* Permet de cliquer à travers */
}

/* Pour la compatibilité avec le code existant */
.canvas-container {
  display: none; /* Masquer le canvas */
}

/* Styles pour les éléments qui utilisaient des styles en ligne */
.phone-preview-image-style {
  transform-origin: center;
  object-position: center;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.phone-preview-text-style {
  /* Les styles de texte seront appliqués via des classes CSS */
}

.phone-preview-background {
  /* Style pour le fond de la coque */
}

.phone-preview-image-container {
  /* Style pour le conteneur d'image */
}