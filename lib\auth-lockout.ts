/**
 * Utilitaire pour gérer le blocage temporaire après plusieurs tentatives de connexion échouées
 */

// Nombre maximal de tentatives avant blocage
export const MAX_LOGIN_ATTEMPTS = 5;

// Durée du blocage en millisecondes (10 minutes)
export const LOCKOUT_DURATION = 10 * 60 * 1000;

// Clé pour stocker les informations de blocage dans le localStorage
const LOCKOUT_KEY = 'hcp_auth_lockout';

/**
 * Interface pour les informations de blocage
 */
interface LockoutInfo {
  attempts: number;
  timestamp: number | null;
}

/**
 * Récupérer les informations de blocage depuis le localStorage
 */
export function getLockoutInfo(): LockoutInfo {
  if (typeof window === 'undefined') {
    return { attempts: 0, timestamp: null };
  }

  const lockoutInfo = localStorage.getItem(LOCKOUT_KEY);
  
  if (!lockoutInfo) {
    return { attempts: 0, timestamp: null };
  }

  try {
    return JSON.parse(lockoutInfo) as LockoutInfo;
  } catch (error) {
    console.error('Erreur lors de la récupération des informations de blocage:', error);
    return { attempts: 0, timestamp: null };
  }
}

/**
 * Enregistrer les informations de blocage dans le localStorage
 */
export function saveLockoutInfo(info: LockoutInfo): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem(LOCKOUT_KEY, JSON.stringify(info));
}

/**
 * Incrémenter le nombre de tentatives de connexion échouées
 * @returns Les informations de blocage mises à jour
 */
export function incrementLoginAttempts(): LockoutInfo {
  const lockoutInfo = getLockoutInfo();
  
  // Incrémenter le nombre de tentatives
  lockoutInfo.attempts += 1;
  
  // Si le nombre maximal de tentatives est atteint, définir le timestamp de blocage
  if (lockoutInfo.attempts >= MAX_LOGIN_ATTEMPTS && !lockoutInfo.timestamp) {
    lockoutInfo.timestamp = Date.now();
  }
  
  // Enregistrer les informations de blocage
  saveLockoutInfo(lockoutInfo);
  
  return lockoutInfo;
}

/**
 * Réinitialiser les informations de blocage
 */
export function resetLockoutInfo(): void {
  saveLockoutInfo({ attempts: 0, timestamp: null });
}

/**
 * Vérifier si l'utilisateur est bloqué
 * @returns true si l'utilisateur est bloqué, false sinon
 */
export function isUserLocked(): boolean {
  const lockoutInfo = getLockoutInfo();
  
  // Si le nombre de tentatives est inférieur au maximum, l'utilisateur n'est pas bloqué
  if (lockoutInfo.attempts < MAX_LOGIN_ATTEMPTS) {
    return false;
  }
  
  // Si aucun timestamp de blocage n'est défini, l'utilisateur n'est pas bloqué
  if (!lockoutInfo.timestamp) {
    return false;
  }
  
  // Calculer le temps écoulé depuis le blocage
  const elapsedTime = Date.now() - lockoutInfo.timestamp;
  
  // Si le temps écoulé est supérieur à la durée de blocage, réinitialiser les informations de blocage
  if (elapsedTime >= LOCKOUT_DURATION) {
    resetLockoutInfo();
    return false;
  }
  
  // L'utilisateur est bloqué
  return true;
}

/**
 * Obtenir le temps restant avant la fin du blocage
 * @returns Le temps restant en millisecondes, ou 0 si l'utilisateur n'est pas bloqué
 */
export function getRemainingLockoutTime(): number {
  const lockoutInfo = getLockoutInfo();
  
  // Si l'utilisateur n'est pas bloqué, retourner 0
  if (!isUserLocked() || !lockoutInfo.timestamp) {
    return 0;
  }
  
  // Calculer le temps restant
  const elapsedTime = Date.now() - lockoutInfo.timestamp;
  const remainingTime = Math.max(0, LOCKOUT_DURATION - elapsedTime);
  
  return remainingTime;
}

/**
 * Formater le temps restant avant la fin du blocage
 * @returns Le temps restant formaté (ex: "8:45")
 */
export function formatRemainingLockoutTime(): string {
  const remainingTime = getRemainingLockoutTime();
  
  // Convertir en minutes et secondes
  const minutes = Math.floor(remainingTime / 60000);
  const seconds = Math.floor((remainingTime % 60000) / 1000);
  
  // Formater
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}
