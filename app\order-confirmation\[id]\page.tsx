"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, ArrowLeft, ShoppingBag, Phone } from "lucide-react"
import { supabaseApi } from "@/lib/supabase"

export default function OrderConfirmationPage() {
  const { id } = useParams()
  const [order, setOrder] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true)
        
        if (!id) {
          setError("ID de commande manquant")
          return
        }
        
        const { data, error } = await supabaseApi.orders.getById(id as string)
        
        if (error) {
          throw error
        }
        
        setOrder(data)
      } catch (err) {
        console.error("Erreur lors de la récupération de la commande:", err)
        setError("Impossible de récupérer les détails de la commande")
      } finally {
        setLoading(false)
      }
    }
    
    fetchOrder()
  }, [id])
  
  // Formater le prix
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF'
    }).format(price)
  }
  
  // Formater la date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  if (loading) {
    return (
      <div className="container mx-auto p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
        <p className="mt-4">Chargement des détails de la commande...</p>
      </div>
    )
  }
  
  if (error || !order) {
    return (
      <div className="container mx-auto p-8 text-center">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Erreur</h1>
        <p className="mb-6">{error || "Une erreur est survenue"}</p>
        <Link href="/">
          <Button>Retour à l'accueil</Button>
        </Link>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto p-8">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-8">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold mb-2">Commande confirmée</h1>
          <p className="text-gray-600">
            Merci pour votre commande ! Votre numéro de commande est <span className="font-bold">{order.id}</span>
          </p>
        </div>
        
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Détails de la commande</CardTitle>
            <CardDescription>
              Commande passée le {formatDate(order.created_at)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-sm text-gray-500">Statut</h3>
                  <p className="font-medium">
                    {order.status === 'pending' ? 'En attente' : 
                     order.status === 'processing' ? 'En traitement' :
                     order.status === 'shipped' ? 'Expédiée' :
                     order.status === 'delivered' ? 'Livrée' : 'Annulée'}
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-gray-500">Total</h3>
                  <p className="font-medium">{formatPrice(order.total)}</p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-gray-500">Méthode de paiement</h3>
                  <p className="font-medium">
                    {order.payment_method === 'wave' ? 'Wave' : 'Orange Money'}
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-gray-500">Adresse de livraison</h3>
                  <p className="font-medium">{order.shipping_address}, {order.shipping_city}</p>
                </div>
              </div>
              
              {order.items && order.items.length > 0 && (
                <div className="mt-6">
                  <h3 className="font-medium mb-2">Articles commandés</h3>
                  <div className="space-y-2">
                    {order.items.map((item: any) => (
                      <div key={item.id} className="flex justify-between border-b pb-2">
                        <div>
                          <p className="font-medium">{item.product?.name || 'Produit'}</p>
                          <p className="text-sm text-gray-500">Quantité: {item.quantity}</p>
                          {item.customized && (
                            <p className="text-sm text-purple-600">Personnalisé</p>
                          )}
                        </div>
                        <p className="font-medium">{formatPrice(item.price * item.quantity)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <Link href="/">
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Retour à l'accueil
            </Button>
          </Link>
          
          <Link href="/products">
            <Button className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700">
              <ShoppingBag className="h-4 w-4" />
              Continuer les achats
            </Button>
          </Link>
          
          <a href="https://wa.me/2250707070707" target="_blank" rel="noopener noreferrer">
            <Button variant="outline" className="flex items-center gap-2 border-green-500 text-green-500 hover:bg-green-50">
              <Phone className="h-4 w-4" />
              Contacter sur WhatsApp
            </Button>
          </a>
        </div>
      </div>
    </div>
  )
}
