-- Migration pour mettre à jour la table des commandes avec les champs de paiement

-- Ajouter les champs pour le paiement par QR code
ALTER TABLE public.orders 
ADD COLUMN IF NOT EXISTS payment_message TEXT,
ADD COLUMN IF NOT EXISTS payment_screenshot TEXT,
ADD COLUMN IF NOT EXISTS customer_name TEXT,
ADD COLUMN IF NOT EXISTS customer_phone TEXT;

-- Mettre à jour les politiques de sécurité pour les commandes anonymes
-- Permettre aux utilisateurs anonymes de créer des commandes
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs anonymes peuvent créer des commandes') THEN
    CREATE POLICY "Les utilisateurs anonymes peuvent créer des commandes"
    ON public.orders FOR INSERT
    WITH CHECK (true);
  END IF;
END
$$;

-- Permettre aux utilisateurs anonymes de voir leurs commandes via un identifiant unique
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs anonymes peuvent voir leurs commandes') THEN
    CREATE POLICY "Les utilisateurs anonymes peuvent voir leurs commandes"
    ON public.orders FOR SELECT
    USING (true);
  END IF;
END
$$;

-- Permettre aux utilisateurs anonymes de voir les articles de leurs commandes
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs anonymes peuvent voir les articles de leurs commandes') THEN
    CREATE POLICY "Les utilisateurs anonymes peuvent voir les articles de leurs commandes"
    ON public.order_items FOR SELECT
    USING (true);
  END IF;
END
$$;

-- Créer un bucket pour les captures d'écran de paiement s'il n'existe pas déjà
-- Note: Cette partie doit être exécutée manuellement dans la console Supabase
-- car les migrations SQL ne peuvent pas créer des buckets de stockage
-- 
-- Dans la console Supabase:
-- 1. Aller dans "Storage"
-- 2. Cliquer sur "Create bucket"
-- 3. Nom: "orders"
-- 4. Type: "Private"
-- 5. Cliquer sur "Create bucket"
--
-- Puis créer une politique pour permettre l'accès aux fichiers:
-- 1. Sélectionner le bucket "orders"
-- 2. Aller dans "Policies"
-- 3. Ajouter une politique pour "INSERT" avec la condition "true"
-- 4. Ajouter une politique pour "SELECT" avec la condition "true" pour les administrateurs
