/**
 * Script pour vérifier et corriger les problèmes d'images
 * 
 * Ce script vérifie que toutes les images référencées dans le code existent
 * et crée des images de remplacement si nécessaire.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Fonction pour vérifier si un fichier existe
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Fonction pour créer un répertoire s'il n'existe pas
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`Création du répertoire: ${dirPath}`);
    fs.mkdirSync(dirPath, { recursive: true });
    return true;
  }
  return false;
}

// Fonction pour créer une image SVG de base
function createBasicSvgImage(text, subtext = '') {
  return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="600" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="500" height="300" rx="5" fill="#E5E7EB"/>
  <text x="300" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">${text}</text>
  ${subtext ? `<text x="300" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">${subtext}</text>` : ''}
</svg>`;
}

// Fonction pour créer une image de remplacement
function createPlaceholderImage(outputPath, text = 'Image', subtext = 'Placeholder') {
  try {
    // Créer le répertoire parent s'il n'existe pas
    const dirPath = path.dirname(outputPath);
    ensureDirectoryExists(dirPath);
    
    // Créer l'image SVG
    const svgContent = createBasicSvgImage(text, subtext);
    fs.writeFileSync(outputPath, svgContent);
    console.log(`Image créée: ${outputPath}`);
    return true;
  } catch (error) {
    console.error(`Erreur lors de la création de l'image ${outputPath}:`, error);
    return false;
  }
}

// Fonction pour vérifier les images des bannières
function verifyBannerImages() {
  console.log("Vérification des images de bannières...");
  
  const bannersDir = path.join(process.cwd(), 'public', 'images', 'banners');
  ensureDirectoryExists(bannersDir);
  
  // Vérifier les bannières de promotion
  const promotionsBannersDir = path.join(bannersDir, 'promotions');
  ensureDirectoryExists(promotionsBannersDir);
  
  // Vérifier les bannières spécifiques
  for (let i = 1; i <= 3; i++) {
    const bannerPath = path.join(promotionsBannersDir, `banner${i}.png`);
    if (!fileExists(bannerPath)) {
      createPlaceholderImage(bannerPath, `Bannière ${i}`, 'Promotion');
    }
  }
}

// Fonction pour vérifier les images des promotions
function verifyPromoImages() {
  console.log("Vérification des images de promotions...");
  
  const promosDir = path.join(process.cwd(), 'public', 'images', 'promos');
  ensureDirectoryExists(promosDir);
  
  // Vérifier les sous-dossiers
  const subfolders = ['seasonal', 'bundle', 'new'];
  
  subfolders.forEach(folder => {
    const folderPath = path.join(promosDir, folder);
    ensureDirectoryExists(folderPath);
    
    // Créer les images spécifiques pour chaque dossier
    if (folder === 'seasonal') {
      const imagePath = path.join(folderPath, 'fete-des-meres.png');
      if (!fileExists(imagePath)) {
        createPlaceholderImage(imagePath, 'Fête des Mères', '20% de réduction');
      }
    } else if (folder === 'bundle') {
      const imagePath = path.join(folderPath, 'pack-famille.png');
      if (!fileExists(imagePath)) {
        createPlaceholderImage(imagePath, 'Pack Famille', '25% de réduction');
      }
    } else if (folder === 'new') {
      const imagePath = path.join(folderPath, 'bienvenue.png');
      if (!fileExists(imagePath)) {
        createPlaceholderImage(imagePath, 'Offre de Bienvenue', '15% de réduction');
      }
    }
  });
}

// Fonction pour vérifier les images de la galerie
function verifyGalleryImages() {
  console.log("Vérification des images de la galerie...");
  
  const galleryDir = path.join(process.cwd(), 'public', 'images', 'gallery');
  ensureDirectoryExists(galleryDir);
  
  // Créer une image placeholder pour la galerie
  const placeholderPath = path.join(galleryDir, 'gallery-placeholder.svg');
  if (!fileExists(placeholderPath)) {
    createPlaceholderImage(placeholderPath, 'Galerie', 'Image placeholder');
  }
  
  // Vérifier les variantes
  const variantsDir = path.join(galleryDir, 'variants');
  ensureDirectoryExists(variantsDir);
  
  // Liste des variantes
  const variants = [
    'vagues-abstraites',
    'fleurs-tropicales',
    'galaxie-cosmique',
    'marbre-elegant',
    'retro-synthwave',
    'montagnes-minimalistes',
    'motif-geometrique',
    'neon-urbain',
    'mandala-zen',
    'animaux-polygonaux',
    'typographie-creative'
  ];
  
  variants.forEach(variant => {
    const variantDir = path.join(variantsDir, variant);
    ensureDirectoryExists(variantDir);
    
    const imagePath = path.join(variantDir, `${variant}.png`);
    if (!fileExists(imagePath)) {
      const displayName = variant
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      
      createPlaceholderImage(imagePath, displayName, 'Design personnalisé');
    }
  });
}

// Fonction pour vérifier les images des marques
function verifyBrandImages() {
  console.log("Vérification des images des marques...");
  
  const brandsDir = path.join(process.cwd(), 'public', 'images', 'brands');
  ensureDirectoryExists(brandsDir);
  
  // Liste des marques
  const brands = ['apple', 'samsung', 'google', 'xiaomi', 'huawei'];
  
  brands.forEach(brand => {
    const imagePath = path.join(brandsDir, `${brand}.svg`);
    if (!fileExists(imagePath)) {
      createPlaceholderImage(imagePath, brand.charAt(0).toUpperCase() + brand.slice(1), 'Logo');
    }
  });
}

// Fonction pour vérifier les images des modèles de téléphone
function verifyPhoneModelImages() {
  console.log("Vérification des images des modèles de téléphone...");
  
  const phoneModelsDir = path.join(process.cwd(), 'public', 'images', 'phone-models');
  ensureDirectoryExists(phoneModelsDir);
  
  // Liste des modèles
  const models = [
    'iphone15promax', 'iphone15pro', 'iphone15plus', 'iphone15',
    'iphone14promax', 'iphone14pro', 'iphone14plus', 'iphone14',
    'samsungs23ultra', 'samsungs23plus', 'samsungs23',
    'pixel8pro', 'pixel8', 'pixel7pro', 'pixel7'
  ];
  
  models.forEach(model => {
    const imagePath = path.join(phoneModelsDir, `${model}.png`);
    if (!fileExists(imagePath)) {
      createPlaceholderImage(imagePath, model, 'Modèle de téléphone');
    }
  });
}

// Fonction principale
function main() {
  console.log("Début de la vérification des images...");
  
  // Vérifier les images des bannières
  verifyBannerImages();
  
  // Vérifier les images des promotions
  verifyPromoImages();
  
  // Vérifier les images de la galerie
  verifyGalleryImages();
  
  // Vérifier les images des marques
  verifyBrandImages();
  
  // Vérifier les images des modèles de téléphone
  verifyPhoneModelImages();
  
  console.log("Vérification des images terminée.");
}

// Exécuter la fonction principale
main();
