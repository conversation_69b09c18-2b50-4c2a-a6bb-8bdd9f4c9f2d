.phone-preview-dimensions {
  --phone-width: var(--phone-width);
  --phone-height: var(--phone-height);
  width: var(--phone-width);
  height: var(--phone-height);
}

.backgroundContainer {
  position: absolute;
  inset: 0;
  border-radius: 1.5rem;
  overflow: hidden;
}

.imageContainer {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.imageWrapper {
  max-width: 100%;
  max-height: 100%;
}

.textContainer {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.textWrapper {
  max-width: 80%;
  text-align: center;
  word-break: break-word;
}

.phoneFrame {
  position: absolute;
  inset: 0;
}

.phoneImage {
  object-fit: contain;
  pointer-events: none;
}


