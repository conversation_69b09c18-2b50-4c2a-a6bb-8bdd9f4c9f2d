/**
 * Script pour invalider le cache en ajoutant des paramètres de version aux images
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Fonction pour lire un fichier
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier ${filePath}:`, error);
    return null;
  }
}

// Fonction pour écrire dans un fichier
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'écriture dans le fichier ${filePath}:`, error);
    return false;
  }
}

// Fonction pour obtenir un timestamp
function getTimestamp() {
  return Date.now();
}

// Fonction pour ajouter des paramètres de version aux références d'images
function addVersionToImageReferences(filePath) {
  console.log(`Ajout de paramètres de version aux références d'images dans ${filePath}...`);
  
  const content = readFile(filePath);
  if (!content) return false;
  
  // Recherche des références aux images
  const imageRegex = /(?:src|image|imageUrl|thumbnail):\s*["']([^"']*\.(?:png|jpg|jpeg|svg|gif|webp))["']/g;
  let match;
  let modified = false;
  let newContent = content;
  
  const replacements = [];
  const timestamp = getTimestamp();
  
  while ((match = imageRegex.exec(content)) !== null) {
    const fullMatch = match[0];
    const imagePath = match[1];
    
    // Ignorer les URLs externes
    if (imagePath.startsWith('http')) continue;
    
    // Ajouter un paramètre de version
    const newImagePath = `${imagePath}?v=${timestamp}`;
    const newReference = fullMatch.replace(imagePath, newImagePath);
    
    replacements.push({ from: fullMatch, to: newReference });
    modified = true;
  }
  
  // Appliquer les remplacements
  for (const replacement of replacements) {
    newContent = newContent.replace(replacement.from, replacement.to);
  }
  
  // Enregistrer les modifications
  if (modified) {
    console.log(`Ajout de paramètres de version à ${replacements.length} références d'images dans ${filePath}`);
    return writeFile(filePath, newContent);
  }
  
  return false;
}

// Fonction pour parcourir récursivement un répertoire
function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    isDirectory ? walkDir(dirPath, callback) : callback(path.join(dir, f));
  });
}

// Fonction pour committer et pousser les modifications
function commitAndPush() {
  console.log("Commit et push des modifications...");
  
  try {
    // Ajouter tous les fichiers modifiés
    execSync('git add .');
    
    // Créer un commit
    execSync(`git commit -m "Invalidate cache: Add version parameters to image references"`);
    
    // Pousser vers GitHub
    execSync('git push');
    
    console.log("Modifications poussées avec succès.");
    return true;
  } catch (error) {
    console.error("Erreur lors du commit et push:", error);
    return false;
  }
}

// Fonction principale
function main() {
  console.log("Début du processus d'invalidation du cache...");
  
  let totalModified = 0;
  
  // Traiter spécifiquement la page modèle
  const modelsPagePath = path.join(process.cwd(), 'app', 'models', 'page.tsx');
  if (addVersionToImageReferences(modelsPagePath)) {
    totalModified++;
  }
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire app
  walkDir('app', (filePath) => {
    if ((filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) && !filePath.includes('models/page.tsx')) {
      if (addVersionToImageReferences(filePath)) {
        totalModified++;
      }
    }
  });
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire components
  walkDir('components', (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      if (addVersionToImageReferences(filePath)) {
        totalModified++;
      }
    }
  });
  
  console.log(`\nTotal des fichiers modifiés: ${totalModified}`);
  
  if (totalModified > 0) {
    // Committer et pousser les modifications
    const pushed = commitAndPush();
    
    if (pushed) {
      console.log("\nInvalidation du cache initiée avec succès.");
      console.log("Le site devrait être redéployé avec les nouvelles références d'images.");
    } else {
      console.log("\nÉchec de l'invalidation du cache.");
      console.log("Veuillez vérifier les erreurs ci-dessus et réessayer.");
    }
  } else {
    console.log("\nAucune modification n'a été effectuée.");
    console.log("L'invalidation du cache n'a pas été initiée.");
  }
}

// Exécuter la fonction principale
main();
