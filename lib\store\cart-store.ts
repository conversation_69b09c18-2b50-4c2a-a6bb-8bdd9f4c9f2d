"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"

// Type pour un produit du panier
export interface CartProduct {
  id: string
  name: string
  price: number
  image_url: string
  quantity: number
  customized?: boolean
  customization_data?: any
  phoneModel?: string
  customization?: {
    phoneModel?: string
    [key: string]: any
  }
}

// Interface pour le store du panier
interface CartStore {
  items: CartProduct[]
  addItem: (product: Omit<CartProduct, "quantity">, quantity: number) => void
  updateItem: (productId: string, quantity: number) => void
  removeItem: (productId: string) => void
  clearCart: () => void
  subtotal: number
  shippingCost: number
  total: number
}

// Coût de livraison par défaut (2000 FCFA)
const DEFAULT_SHIPPING_COST = 2000;
// Seuil pour la livraison gratuite (50000 FCFA)
const FREE_SHIPPING_THRESHOLD = 50000;

// Créer le store Zustand avec persistance
export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      
      addItem: (product, quantity) => {
        try {
          console.log("[DEBUG] Ajout du produit au panier:", {
            product,
            quantity,
            productPrice: product.price,
            productName: product.name
          });
          set((state) => {
            // Vérifier si le produit existe déjà
            const existingItemIndex = state.items.findIndex(
              item => item.id === product.id && 
                     JSON.stringify(item.customization_data) === JSON.stringify(product.customization_data)
            );
            
            if (existingItemIndex !== -1) {
              // Mettre à jour la quantité si le produit existe déjà
              const updatedItems = [...state.items];
              updatedItems[existingItemIndex].quantity += quantity;
              console.log("[DEBUG] Produit existant mis à jour:", updatedItems[existingItemIndex]);
              return { items: updatedItems };
            } else {
              // Ajouter un nouvel article
              const newItem = { ...product, quantity };
              console.log("[DEBUG] Nouvel article ajouté:", newItem);
              return {
                items: [...state.items, newItem]
              };
            }
          });
          
          // Afficher l'état du panier après ajout
          setTimeout(() => {
            const currentState = get();
            console.log("[DEBUG] État du panier après ajout:", {
              items: currentState.items,
              subtotal: currentState.subtotal,
              shippingCost: currentState.shippingCost,
              total: currentState.total
            });
          }, 100);
        } catch (error) {
          console.error("Erreur lors de l'ajout au panier:", error);
        }
      },
      
      updateItem: (productId, quantity) => {
        try {
          console.log("Mise à jour de la quantité:", productId, "nouvelle quantité:", quantity);
          
          if (quantity <= 0) {
            // Si la quantité est 0 ou négative, supprimer l'article
            get().removeItem(productId);
            return;
          }
          
          set((state) => ({
            items: state.items.map(item => 
              item.id === productId ? { ...item, quantity } : item
            )
          }));
        } catch (error) {
          console.error("Erreur lors de la mise à jour du panier:", error);
        }
      },
      
      removeItem: (productId) => {
        try {
          console.log("Suppression du produit du panier:", productId);
          set((state) => ({
            items: state.items.filter(item => item.id !== productId)
          }));
        } catch (error) {
          console.error("Erreur lors de la suppression du panier:", error);
        }
      },
      
      clearCart: () => {
        try {
          console.log("Vidage du panier");
          set({ items: [] });
        } catch (error) {
          console.error("Erreur lors du vidage du panier:", error);
        }
      },
      
      // Calculer le sous-total (propriété calculée)
      get subtotal() {
        try {
          const currentState = get();
          // Vérification de sécurité pour l'hydratation SSR
          if (!currentState || !currentState.items || currentState.items.length === 0) {
            console.log("[DEBUG] Subtotal: aucun item dans le panier", currentState?.items || []);
            return 0;
          }
          const calculatedSubtotal = currentState.items.reduce((total: number, item: CartProduct) => {
            const itemTotal = item.price * item.quantity;
            console.log(`[DEBUG] Item: ${item.name}, Prix: ${item.price}, Quantité: ${item.quantity}, Total item: ${itemTotal}`);
            return total + itemTotal;
          }, 0);
          console.log(`[DEBUG] Sous-total calculé: ${calculatedSubtotal}`);
          return calculatedSubtotal;
        } catch (error) {
          console.error("Erreur lors du calcul du sous-total:", error);
          return 0;
        }
      },
      
      // Calculer les frais de livraison (propriété calculée)
      get shippingCost() {
        try {
          const state = get();
          if (!state) {
            console.log("[DEBUG] ShippingCost: state vide");
            return DEFAULT_SHIPPING_COST;
          }
          const shipping = state.subtotal >= FREE_SHIPPING_THRESHOLD ? 0 : DEFAULT_SHIPPING_COST;
          console.log(`[DEBUG] Frais de livraison: ${shipping} (sous-total: ${state.subtotal}, seuil: ${FREE_SHIPPING_THRESHOLD})`);
          return shipping;
        } catch (error) {
          console.error("Erreur lors du calcul des frais de livraison:", error);
          return DEFAULT_SHIPPING_COST;
        }
      },
      
      // Calculer le total (propriété calculée)
      get total() {
        try {
          const state = get();
          if (!state) {
            console.log("[DEBUG] Total: state vide");
            return DEFAULT_SHIPPING_COST;
          }
          const calculatedTotal = state.subtotal + state.shippingCost;
          console.log(`[DEBUG] Total calculé: ${calculatedTotal} (sous-total: ${state.subtotal} + livraison: ${state.shippingCost})`);
          return calculatedTotal;
        } catch (error) {
          console.error("Erreur lors du calcul du total:", error);
          return DEFAULT_SHIPPING_COST;
        }
      }
    }),
    {
      name: "cart-storage",
      // Utiliser localStorage au lieu de sessionStorage
      storage: {
        getItem: (name) => {
          try {
            if (typeof window === 'undefined') return null;
            const str = localStorage.getItem(name);
            if (!str) return null;
            return JSON.parse(str);
          } catch (error) {
            console.error("Erreur lors de la récupération du panier:", error);
            return null;
          }
        },
        setItem: (name, value) => {
          try {
            if (typeof window === 'undefined') return;
            localStorage.setItem(name, JSON.stringify(value));
          } catch (error) {
            console.error("Erreur lors de l'enregistrement du panier:", error);
          }
        },
        removeItem: (name) => {
          try {
            if (typeof window === 'undefined') return;
            localStorage.removeItem(name);
          } catch (error) {
            console.error("Erreur lors de la suppression du panier:", error);
          }
        }
      }
    }
  )
)
