import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { isAdminEmail } from './lib/admin-config';

/**
 * Middleware pour protéger les routes sécurisées
 * Vérifie si l'utilisateur est authentifié et a les droits d'accès
 */
export function middleware(req: NextRequest) {
  console.log(`[Middleware] Traitement de la requête pour: ${req.nextUrl.pathname}`);
  
  // Redirection supprimée pour /admin - les utilisateurs doivent connaître l'URL exacte /hcp-admin-panel
  // Seule la redirection pour /dashboard-hcp-secure est conservée pour compatibilité
  if (req.nextUrl.pathname.startsWith('/dashboard-hcp-secure')) {
    console.log('[Middleware] Redirection de /dashboard-hcp-secure vers /hcp-admin-panel');
    // Rediriger vers la nouvelle URL
    let newPath = req.nextUrl.pathname.replace('/dashboard-hcp-secure', '/hcp-admin-panel');
    
    const url = new URL(newPath, req.url);

    // Conserver les paramètres de requête
    for (const [key, value] of req.nextUrl.searchParams.entries()) {
      url.searchParams.set(key, value);
    }

    return NextResponse.redirect(url);
  }

  // Vérifier si la route commence par /hcp-admin-panel
  if (req.nextUrl.pathname.startsWith('/hcp-admin-panel')) {
    console.log('[Middleware] Accès à une route protégée: /hcp-admin-panel');
    
    // Vérification de l'IP (liste blanche)
    const clientIp = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const allowedIps = (process.env.ALLOWED_ADMIN_IPS || '').split(',').map(ip => ip.trim());
    
    // Si la liste d'IPs autorisées est configurée et que l'IP du client n'est pas dans la liste
    if (allowedIps.length > 0 && allowedIps[0] !== '' && !allowedIps.includes(clientIp)) {
      console.log(`[Middleware] Accès refusé: IP non autorisée ${clientIp}`);
      return NextResponse.redirect(new URL('/', req.url));
    }
    
    // Vérification du User-Agent (bloquer les robots)
    const userAgent = req.headers.get('user-agent') || '';
    if (userAgent.toLowerCase().includes('bot') || userAgent.toLowerCase().includes('crawler')) {
      console.log(`[Middleware] Accès refusé: User-Agent suspect ${userAgent}`);
      return NextResponse.redirect(new URL('/', req.url));
    }
    
    // Page d'authentification avec token obligatoire
    if (req.nextUrl.pathname === '/hcp-admin-panel/auth') {
      const securityToken = req.nextUrl.searchParams.get('token');
      const validToken = process.env.ADMIN_ACCESS_TOKEN || 'secure_token_7a9b3c2d1e';
      
      if (securityToken !== validToken) {
        console.log('[Middleware] Tentative d\'accès non autorisée au BO');
        return NextResponse.redirect(new URL('/', req.url));
      }
      
      console.log('[Middleware] Page d\'authentification, accès autorisé avec token valide');
      return NextResponse.next();
    }
    
    // Ignorer la page de test d'authentification avec le même système de token
    if (req.nextUrl.pathname === '/hcp-admin-panel/auth-test') {
      const securityToken = req.nextUrl.searchParams.get('token');
      const validToken = process.env.ADMIN_ACCESS_TOKEN || 'secure_token_7a9b3c2d1e';
      
      if (securityToken !== validToken) {
        console.log('[Middleware] Tentative d\'accès non autorisée à la page de test');
        return NextResponse.redirect(new URL('/', req.url));
      }
      
      console.log('[Middleware] Page de test d\'authentification, accès autorisé avec token valide');
      return NextResponse.next();
    }

    // Vérifier si les cookies de session Supabase existent
    const hasSbAccessToken = req.cookies.has('sb-access-token');
    const hasSbRefreshToken = req.cookies.has('sb-refresh-token');
    
    // Vérifier le cookie d'authentification à deux facteurs
    const has2FAVerified = req.cookies.has('hcp-2fa-verified');
    
    console.log('[Middleware] Vérification des cookies:', {
      hasSbAccessToken,
      hasSbRefreshToken,
      has2FAVerified,
      cookies: Array.from(req.cookies.getAll()).map(c => c.name)
    });

    // Si aucun cookie de session n'existe, rediriger vers la page d'authentification avec token
    if (!hasSbAccessToken || !hasSbRefreshToken) {
      console.log('[Middleware] Cookies de session manquants, redirection vers la page d\'authentification');
      const url = new URL('/hcp-admin-panel/auth', req.url);
      url.searchParams.set('from', req.nextUrl.pathname);
      url.searchParams.set('token', process.env.ADMIN_ACCESS_TOKEN || 'secure_token_7a9b3c2d1e');
      return NextResponse.redirect(url);
    }
    
    // Vérifier l'authentification à deux facteurs pour les routes sensibles
    const sensitiveRoutes = ['/hcp-admin-panel/settings', '/hcp-admin-panel/users', '/hcp-admin-panel/orders'];
    if (sensitiveRoutes.some(route => req.nextUrl.pathname.startsWith(route)) && !has2FAVerified) {
      console.log('[Middleware] Accès à une route sensible sans 2FA');
      const url = new URL('/hcp-admin-panel/verify-2fa', req.url);
      url.searchParams.set('from', req.nextUrl.pathname);
      return NextResponse.redirect(url);
    }
    
    console.log('[Middleware] Cookies de session présents, accès autorisé');
  }

  return NextResponse.next();
}

// Configurer les chemins sur lesquels le middleware doit s'exécuter
export const config = {
  matcher: ['/dashboard-hcp-secure/:path*', '/hcp-admin-panel/:path*'],
};


