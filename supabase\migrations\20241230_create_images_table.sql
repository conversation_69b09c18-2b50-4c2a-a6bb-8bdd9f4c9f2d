-- Migration pour créer la table images avec support pour les catégories Gospel
-- Date: 2024-12-30

-- <PERSON><PERSON><PERSON> la table images
CREATE TABLE IF NOT EXISTS public.images (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  path TEXT NOT NULL,
  category TEXT NOT NULL,
  subcategory TEXT,
  description TEXT,
  width INTEGER DEFAULT 0,
  height INTEGER DEFAULT 0,
  alt TEXT,
  file_size INTEGER,
  mime_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_images_category ON public.images(category);
CREATE INDEX IF NOT EXISTS idx_images_subcategory ON public.images(subcategory);
CREATE INDEX IF NOT EXISTS idx_images_path ON public.images(path);
CREATE INDEX IF NOT EXISTS idx_images_created_at ON public.images(created_at);

-- Activer RLS (Row Level Security)
ALTER TABLE public.images ENABLE ROW LEVEL SECURITY;

-- Créer une politique pour que seuls les administrateurs puissent gérer les images
CREATE POLICY "Admins can manage images" 
  ON public.images 
  USING (auth.role() = 'service_role');

-- Créer une politique pour que tous les utilisateurs puissent lire les images
CREATE POLICY "All users can read images" 
  ON public.images 
  FOR SELECT
  USING (true);

-- Créer une fonction pour mettre à jour la date de mise à jour
CREATE OR REPLACE FUNCTION update_images_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger pour mettre à jour automatiquement updated_at
CREATE TRIGGER trigger_update_images_updated_at
  BEFORE UPDATE ON public.images
  FOR EACH ROW
  EXECUTE FUNCTION update_images_updated_at();

-- Insérer quelques exemples d'images Gospel pour la démonstration
INSERT INTO public.images (name, path, category, subcategory, description, alt)
VALUES 
  ('Carnet de Prière', '/images/gallery/gospel/carnet-priere.png', 'gospel', 'notebooks', 'Carnet de prière personnalisé avec versets bibliques', 'Carnet de prière Gospel'),
  ('Bloc-notes Versets', '/images/gallery/gospel/bloc-notes-versets.png', 'gospel', 'notebooks', 'Bloc-notes avec versets inspirants', 'Bloc-notes avec versets bibliques'),
  ('Tasse Blanche Gospel', '/images/gallery/gospel/tasse-blanche.png', 'gospel', 'mugs', 'Tasse blanche avec message gospel', 'Tasse blanche Gospel'),
  ('Tasse Magique Gospel', '/images/gallery/gospel/tasse-magique.png', 'gospel', 'mugs', 'Tasse magique qui révèle un message gospel', 'Tasse magique Gospel'),
  ('Stylo Standard Gospel', '/images/gallery/gospel/stylo-standard.png', 'gospel', 'pens', 'Stylo standard avec message gospel', 'Stylo Gospel standard'),
  ('Stylo VIP Gospel', '/images/gallery/gospel/stylo-vip.png', 'gospel', 'pens', 'Stylo VIP premium avec gravure gospel', 'Stylo Gospel VIP'),
  ('Kakémono Petit', '/images/gallery/gospel/kakemono-petit.png', 'gospel', 'banners', 'Petit kakémono avec message gospel', 'Petit kakémono Gospel'),
  ('Kakémono Grand', '/images/gallery/gospel/kakemono-grand.png', 'gospel', 'banners', 'Grand kakémono avec message gospel', 'Grand kakémono Gospel'),
  ('T-shirt VIP Gospel', '/images/gallery/gospel/tshirt-vip.png', 'gospel', 'tshirts', 'T-shirt VIP avec design gospel premium', 'T-shirt Gospel VIP'),
  ('T-shirt Standard Gospel', '/images/gallery/gospel/tshirt-standard.png', 'gospel', 'tshirts', 'T-shirt standard avec message gospel', 'T-shirt Gospel standard'),
  ('Porte-clé Plastique', '/images/gallery/gospel/porte-cle-plastique.png', 'gospel', 'keychains', 'Porte-clé en plastique avec message gospel', 'Porte-clé Gospel plastique'),
  ('Porte-clé Métal', '/images/gallery/gospel/porte-cle-metal.png', 'gospel', 'keychains', 'Porte-clé en métal avec gravure gospel', 'Porte-clé Gospel métal'),
  ('Gourde 600ml Gospel', '/images/gallery/gospel/gourde-600ml.png', 'gospel', 'bottles', 'Gourde 600ml avec message gospel', 'Gourde Gospel 600ml'),
  ('Gourde 700ml Gospel', '/images/gallery/gospel/gourde-700ml.png', 'gospel', 'bottles', 'Gourde 700ml avec message gospel', 'Gourde Gospel 700ml'),
  ('Coussin VIP 30cm', '/images/gallery/gospel/coussin-vip-30.png', 'gospel', 'cushions', 'Coussin VIP 30x30cm avec design gospel', 'Coussin Gospel VIP 30cm'),
  ('Coussin VIP 40cm', '/images/gallery/gospel/coussin-vip-40.png', 'gospel', 'cushions', 'Coussin VIP 40x40cm avec design gospel', 'Coussin Gospel VIP 40cm'),
  ('Coussin Royal 30cm', '/images/gallery/gospel/coussin-royal-30.png', 'gospel', 'cushions', 'Coussin Royal 30x30cm avec finitions dorées', 'Coussin Gospel Royal 30cm'),
  ('Coussin Royal 40cm', '/images/gallery/gospel/coussin-royal-40.png', 'gospel', 'cushions', 'Coussin Royal 40x40cm avec finitions dorées', 'Coussin Gospel Royal 40cm')
ON CONFLICT (path) DO NOTHING;