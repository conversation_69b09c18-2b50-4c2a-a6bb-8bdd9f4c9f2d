/**
 * Utilitaire pour ajouter un paramètre de version aux chemins d'images
 * Cela permet de contourner le cache du navigateur et du CDN lorsque les images sont mises à jour
 */

// Timestamp de déploiement - sera différent à chaque déploiement
const deploymentTimestamp = Date.now();

/**
 * Ajoute un paramètre de version à un chemin d'image
 * @param imagePath - Chemin de l'image
 * @returns Chemin de l'image avec un paramètre de version
 */
export function getVersionedImagePath(imagePath: string): string {
  // Si le chemin est vide ou non défini, retourner une chaîne vide
  if (!imagePath) return '';
  
  // Si le chemin est une URL externe (commence par http:// ou https://), ne pas modifier
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  
  // Ajouter le paramètre de version au chemin
  const separator = imagePath.includes('?') ? '&' : '?';
  return `${imagePath}${separator}v=${deploymentTimestamp}`;
}

/**
 * Fonction pour obtenir le chemin d'une image avec version pour les composants Image de Next.js
 * @param imagePath - Chemin de l'image
 * @returns Objet avec le chemin de l'image et la largeur/hauteur par défaut
 */
export function getNextImageProps(imagePath: string) {
  return {
    src: getVersionedImagePath(imagePath),
    width: 0,
    height: 0,
    sizes: "100vw",
    style: { width: '100%', height: 'auto' },
  };
}
