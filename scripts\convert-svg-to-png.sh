#!/bin/bash

# Script pour convertir tous les fichiers SVG en PNG

# Créer un fichier PNG simple
echo "Création d'un fichier PNG simple"
mkdir -p ./temp
cat > ./temp/placeholder.svg << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Produit</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Créer un fichier PNG pour chaque type de produit
echo "Création des fichiers PNG pour chaque type de produit"

# Tasses
cat > ./temp/mug_placeholder.svg << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Tasse</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# T-shirts
cat > ./temp/tshirt_placeholder.svg << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">T-shirt</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Tapis de souris
cat > ./temp/mousepad_placeholder.svg << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Tapis de souris</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Coussins
cat > ./temp/cushion_placeholder.svg << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Coussin</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Porte-clés
cat > ./temp/keychain_placeholder.svg << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Porte-clé</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Copier les fichiers PNG dans les dossiers correspondants
echo "Copie des fichiers PNG dans les dossiers correspondants"

# Dossier principal des produits
cp ./temp/placeholder.svg ./public/images/products/placeholder.png

# Tasses et leurs variantes
for variant in ceramique thermosensible isotherme emaillee; do
  cp ./temp/mug_placeholder.svg ./public/images/products/mugs/$variant/placeholder.png
done
cp ./temp/mug_placeholder.svg ./public/images/products/mugs/placeholder.png

# T-shirts et leurs variantes
for variant in coton premium polo "manches-longues"; do
  cp ./temp/tshirt_placeholder.svg ./public/images/products/tshirts/$variant/placeholder.png
done
cp ./temp/tshirt_placeholder.svg ./public/images/products/tshirts/placeholder.png

# Tapis de souris et leurs variantes
for variant in standard xxl ergonomique rgb; do
  cp ./temp/mousepad_placeholder.svg ./public/images/products/mousepads/$variant/placeholder.png
done
cp ./temp/mousepad_placeholder.svg ./public/images/products/mousepads/placeholder.png

# Coussins et leurs variantes
for variant in decoratif photo xxl cervical; do
  cp ./temp/cushion_placeholder.svg ./public/images/products/cushions/$variant/placeholder.png
done
cp ./temp/cushion_placeholder.svg ./public/images/products/cushions/placeholder.png

# Porte-clés et leurs variantes
for variant in photo metal multifonction bois; do
  cp ./temp/keychain_placeholder.svg ./public/images/products/keychains/$variant/placeholder.png
done
cp ./temp/keychain_placeholder.svg ./public/images/products/keychains/placeholder.png

# Autres dossiers de produits
cp ./temp/placeholder.svg ./public/images/products/accessoires/placeholder.png
cp ./temp/placeholder.svg ./public/images/products/coques/placeholder.png
cp ./temp/placeholder.svg ./public/images/products/nouveautes/placeholder.png
cp ./temp/placeholder.svg ./public/images/products/personnalisation/placeholder.png
cp ./temp/placeholder.svg ./public/images/products/promotions/placeholder.png

# Supprimer les fichiers SVG
echo "Suppression des fichiers SVG"
find ./public/images/products -name "placeholder.svg" -type f -delete

echo "Conversion des fichiers SVG en PNG terminée"
