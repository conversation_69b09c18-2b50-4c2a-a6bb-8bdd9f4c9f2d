"use client"

import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface HeroBannerProps {
  imagePath: string;
  title: string;
  subtitle: string;
  buttonText?: string;
  buttonLink?: string;
}

export function HeroBanner({
  imagePath,
  title,
  subtitle,
  buttonText,
  buttonLink = '/',
}: HeroBannerProps) {
  return (
    <div className="relative w-full h-[500px] overflow-hidden">
      {/* Image de fond */}
      <motion.div
        initial={{ scale: 1.1, opacity: 0.8 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 1.5, ease: "easeOut" }}
        className="absolute inset-0"
      >
        <img
          src={imagePath}
          alt={title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/20" />
      </motion.div>

      {/* Contenu - Positionné dans les 20% du bas */}
      <div className="absolute inset-x-0 bottom-0 h-[20%] flex flex-col items-center justify-center text-center px-4 md:px-8">
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="max-w-3xl"
        >
          <h1 className="text-2xl md:text-4xl font-bold text-white mb-2">{title}</h1>
          <p className="text-base md:text-lg text-white/90 mb-4">{subtitle}</p>

          {buttonText && (
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Link href={buttonLink}>
                <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white">
                  {buttonText}
                </Button>
              </Link>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
}