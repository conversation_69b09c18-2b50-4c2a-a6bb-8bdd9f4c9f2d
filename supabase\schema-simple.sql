-- Activer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table des profils utilisateurs (extension de la table auth.users de Supabase)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  whatsapp TEXT NOT NULL UNIQUE,
  email TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fonction pour mettre à jour le timestamp updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Table des produits
CREATE TABLE IF NOT EXISTS public.products (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price INTEGER NOT NULL, -- Prix en centimes FCFA
  category TEXT NOT NULL, -- tasses, tshirts, tapis, coussins, portecles
  subcategory TEXT, -- Sous-catégorie spécifique
  image_url TEXT NOT NULL,
  is_new BOOLEAN DEFAULT FALSE,
  is_bestseller BOOLEAN DEFAULT FALSE,
  type TEXT, -- Type de produit (ex: Céramique, Coton, etc.)
  colors TEXT[] DEFAULT '{}', -- Tableau des couleurs disponibles
  collections TEXT[] DEFAULT '{}', -- Tableau des collections
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des articles du panier
CREATE TABLE IF NOT EXISTS public.cart_items (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  customized BOOLEAN DEFAULT FALSE,
  customization_data JSONB DEFAULT NULL, -- Données de personnalisation (image, texte, etc.)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des commandes
CREATE TABLE IF NOT EXISTS public.orders (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  status TEXT NOT NULL DEFAULT 'pending', -- pending, processing, shipped, delivered, cancelled
  total INTEGER NOT NULL, -- Total en centimes FCFA
  shipping_address TEXT NOT NULL,
  shipping_city TEXT NOT NULL,
  shipping_notes TEXT,
  payment_method TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des articles de commande
CREATE TABLE IF NOT EXISTS public.order_items (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  order_id UUID REFERENCES public.orders(id) ON DELETE CASCADE NOT NULL,
  product_id UUID REFERENCES public.products(id) ON DELETE SET NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  price INTEGER NOT NULL, -- Prix unitaire au moment de la commande
  customized BOOLEAN DEFAULT FALSE,
  customization_data JSONB DEFAULT NULL, -- Données de personnalisation
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des modèles de téléphone
CREATE TABLE IF NOT EXISTS public.phone_models (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  brand TEXT NOT NULL,
  name TEXT NOT NULL,
  width INTEGER NOT NULL, -- Largeur en mm
  height INTEGER NOT NULL, -- Hauteur en mm
  image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(brand, name)
);

-- Table des coques de téléphone
CREATE TABLE IF NOT EXISTS public.phone_cases (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  model_id UUID REFERENCES public.phone_models(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  image_url TEXT NOT NULL,
  price INTEGER NOT NULL, -- Prix en centimes FCFA
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activer RLS sur toutes les tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.phone_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.phone_cases ENABLE ROW LEVEL SECURITY;

-- Politiques de base pour permettre l'accès aux données
CREATE POLICY "Accès public aux produits" ON public.products FOR SELECT USING (true);
CREATE POLICY "Accès public aux modèles de téléphone" ON public.phone_models FOR SELECT USING (true);
CREATE POLICY "Accès public aux coques de téléphone" ON public.phone_cases FOR SELECT USING (true);

-- Politiques pour les utilisateurs authentifiés
CREATE POLICY "Accès utilisateur à son profil" ON public.profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Modification utilisateur de son profil" ON public.profiles FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Accès utilisateur à son panier" ON public.cart_items FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Ajout utilisateur à son panier" ON public.cart_items FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Modification utilisateur de son panier" ON public.cart_items FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Suppression utilisateur de son panier" ON public.cart_items FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Accès utilisateur à ses commandes" ON public.orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Création utilisateur de commandes" ON public.orders FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Données de test pour les produits
INSERT INTO public.products (name, description, price, category, subcategory, image_url, is_new, is_bestseller, type, colors, collections)
VALUES
  ('Tasse Personnalisable', 'Tasse en céramique personnalisable', 7990, 'tasses', 'mugs', '/placeholder.svg', true, true, 'Céramique', ARRAY['Blanc', 'Noir'], ARRAY['Classique']),
  ('T-shirt Personnalisable', 'T-shirt 100% coton', 8990, 'tshirts', 'tshirts', '/placeholder.svg', false, true, 'Coton', ARRAY['Blanc', 'Noir'], ARRAY['Basique']),
  ('Tapis de Souris', 'Tapis de souris personnalisable', 4990, 'tapis', 'mousepads', '/placeholder.svg', false, true, 'Standard', ARRAY['Noir'], ARRAY['Basique']),
  ('Coussin Décoratif', 'Coussin décoratif personnalisable', 11990, 'coussins', 'cushions', '/placeholder.svg', true, false, 'Décoratif', ARRAY['Blanc'], ARRAY['Maison']),
  ('Porte-clé Photo', 'Porte-clé avec votre photo', 3990, 'portecles', 'keychains', '/placeholder.svg', false, true, 'Photo', ARRAY['Transparent'], ARRAY['Basique'])
ON CONFLICT DO NOTHING;

-- Données de test pour les modèles de téléphone
INSERT INTO public.phone_models (brand, name, width, height, image_url)
VALUES
  ('Apple', 'iPhone 15 Pro', 71, 147, '/placeholder.svg'),
  ('Samsung', 'Galaxy S23', 70, 146, '/placeholder.svg'),
  ('Xiaomi', 'Redmi Note 12', 76, 165, '/placeholder.svg')
ON CONFLICT DO NOTHING;

-- Données de test pour les coques de téléphone
INSERT INTO public.phone_cases (model_id, name, image_url, price)
SELECT 
  id, 
  'Coque Transparente', 
  '/placeholder.svg', 
  4990
FROM public.phone_models
ON CONFLICT DO NOTHING;
