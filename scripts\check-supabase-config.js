/**
 * Script pour vérifier la configuration Supabase
 * Exécuter avec: node scripts/check-supabase-config.js
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Récupérer les variables d'environnement
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Vérifier si les variables d'environnement sont définies
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Variables d\'environnement manquantes:');
  if (!supabaseUrl) console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  if (!supabaseAnonKey) console.error('   - NEXT_PUBLIC_SUPABASE_ANON_KEY');
  console.log('\n📋 Créez un fichier .env.local à la racine du projet avec ces variables.');
  process.exit(1);
}

// Créer le client Supabase
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Fonction pour vérifier la connexion
async function checkConnection() {
  console.log('🔍 Vérification de la connexion à Supabase...');
  
  try {
    // Tester une requête simple
    const { data, error } = await supabase.from('cart_items').select('count(*)', { count: 'exact', head: true });
    
    if (error) {
      if (error.code === '42P01') {
        console.error('❌ La table cart_items n\'existe pas.');
        console.log('\n📋 Exécutez le script SQL pour créer les tables:');
        console.log('   supabase db push');
        return false;
      }
      
      console.error('❌ Erreur de connexion:', error.message);
      return false;
    }
    
    console.log('✅ Connexion à Supabase établie avec succès!');
    return true;
  } catch (error) {
    console.error('❌ Erreur inattendue:', error.message);
    return false;
  }
}

// Fonction pour vérifier le projet Supabase
async function checkProject() {
  console.log('\n🔍 Vérification du projet Supabase...');
  
  try {
    // Vérifier si le projet est lié
    const { data: settings, error: settingsError } = await supabase.rpc('get_project_settings');
    
    if (settingsError) {
      console.log('ℹ️ Impossible de vérifier les paramètres du projet:', settingsError.message);
      console.log('\n📋 Assurez-vous que votre projet est correctement configuré:');
      console.log('   supabase link --project-ref <votre-project-ref>');
      return;
    }
    
    console.log('✅ Projet Supabase correctement configuré!');
  } catch (error) {
    console.log('ℹ️ Impossible de vérifier les paramètres du projet:', error.message);
  }
}

// Fonction principale
async function main() {
  console.log('🚀 Vérification de la configuration Supabase...\n');
  
  const isConnected = await checkConnection();
  
  if (isConnected) {
    await checkProject();
  }
  
  console.log('\n📝 Résumé:');
  console.log(`URL Supabase: ${supabaseUrl}`);
  console.log(`Clé Supabase: ${supabaseAnonKey.substring(0, 5)}...${supabaseAnonKey.substring(supabaseAnonKey.length - 5)}`);
  console.log(`Statut: ${isConnected ? '✅ Connecté' : '❌ Non connecté'}`);
  
  if (!isConnected) {
    console.log('\n🔧 Solutions possibles:');
    console.log('1. Vérifiez que votre projet Supabase est actif');
    console.log('2. Vérifiez que vos variables d\'environnement sont correctes');
    console.log('3. Exécutez `supabase start` si vous utilisez Supabase en local');
    console.log('4. Vérifiez que les tables nécessaires existent avec `node scripts/check-supabase-tables.js`');
    process.exit(1);
  }
}

main().catch(error => {
  console.error('Erreur:', error);
  process.exit(1);
});