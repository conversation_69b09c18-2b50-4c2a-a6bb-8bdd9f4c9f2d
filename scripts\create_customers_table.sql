-- Créer l'extension uuid-ossp si elle n'existe pas déjà
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON>er la table customers
CREATE TABLE IF NOT EXISTS public.customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  full_name TEXT NOT NULL,
  whatsapp TEXT NOT NULL UNIQUE,
  address TEXT,
  city TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer un index sur le numéro WhatsApp pour des recherches plus rapides
CREATE INDEX IF NOT EXISTS customers_whatsapp_idx ON public.customers (whatsapp);

-- Créer une politique RLS pour permettre l'accès anonyme en lecture/écriture
-- Note: Dans un environnement de production, vous voudrez peut-être restreindre davantage
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre l'insertion de nouveaux clients
CREATE POLICY insert_customers ON public.customers
  FOR INSERT
  TO anon
  WITH CHECK (true);

-- <PERSON>itique pour permettre la lecture des clients
CREATE POLICY read_customers ON public.customers
  FOR SELECT
  TO anon
  USING (true);

-- Politique pour permettre la mise à jour des clients
CREATE POLICY update_customers ON public.customers
  FOR UPDATE
  TO anon
  USING (true)
  WITH CHECK (true);

-- Commentaires sur la table et les colonnes
COMMENT ON TABLE public.customers IS 'Table pour stocker les informations des clients pour le processus de paiement';
COMMENT ON COLUMN public.customers.id IS 'Identifiant unique du client';
COMMENT ON COLUMN public.customers.full_name IS 'Nom complet du client';
COMMENT ON COLUMN public.customers.whatsapp IS 'Numéro WhatsApp du client (unique)';
COMMENT ON COLUMN public.customers.address IS 'Adresse de livraison du client';
COMMENT ON COLUMN public.customers.city IS 'Ville du client';
COMMENT ON COLUMN public.customers.created_at IS 'Date de création de l''enregistrement';
COMMENT ON COLUMN public.customers.updated_at IS 'Date de dernière mise à jour de l''enregistrement';
