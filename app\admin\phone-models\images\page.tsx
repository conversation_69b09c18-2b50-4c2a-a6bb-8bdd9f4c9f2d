"use client";

import { useState, useEffect, useRef } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Search, Plus, Edit, Trash2, Upload, Smartphone, Loader2 } from "lucide-react";
import Image from "next/image";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/lib/supabase";

// Import des services pour les images de modèles
import { 
  getAllModelImagesFromSupabase, 
  addModelImageToSupabase, 
  updateModelImageInSupabase, 
  deleteModelImageFromSupabase,
  uploadModelImage
} from "@/services/phone-model-images-supabase";

// Types pour les modèles de téléphone et leurs images
interface PhoneModel {
  id: string;
  brand: string;
  name: string;
}

interface PhoneModelImage {
  id: string;
  modelId: string;
  imageUrl: string;
  isDefault: boolean;
  name: string;
}

export default function PhoneModelImagesPage() {
  const [modelImages, setModelImages] = useState<PhoneModelImage[]>([]);
  const [phoneModels, setPhoneModels] = useState<PhoneModel[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedModel, setSelectedModel] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingImage, setEditingImage] = useState<PhoneModelImage | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // État pour le formulaire d'ajout/modification
  const [formData, setFormData] = useState<PhoneModelImage>({
    id: "",
    modelId: "",
    imageUrl: "",
    isDefault: false,
    name: ""
  });
  
  // Charger les modèles de téléphone et les images au chargement de la page
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Récupérer les modèles de téléphone
        const { data: modelsData, error: modelsError } = await supabase
          .from('phone_models')
          .select('*');
          
        if (modelsError) {
          throw modelsError;
        }
        
        const models = modelsData.map(model => ({
          id: model.id,
          brand: model.brand,
          name: model.name
        }));
        
        setPhoneModels(models);
        
        // Récupérer les images des modèles
        const images = await getAllModelImagesFromSupabase();
        setModelImages(images);
      } catch (error) {
        console.error("Erreur lors du chargement des données:", error);
        toast({
          title: "Erreur",
          description: "Impossible de charger les données. Veuillez réessayer.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // Filtrer les images en fonction de la recherche et du modèle sélectionné
  const filteredImages = modelImages.filter(image => {
    const matchesSearch = 
      image.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesModel = selectedModel === "all" || image.modelId === selectedModel;
    
    return matchesSearch && matchesModel;
  });

  // Gérer le téléchargement de fichier
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setUploadedFile(e.target.files[0]);
    }
  };

  // Gérer l'ouverture du formulaire d'édition
  const handleEditImage = (image: PhoneModelImage) => {
    setEditingImage(image);
    setFormData(image);
    setUploadedFile(null);
    setIsAddDialogOpen(true);
  };

  // Gérer la soumission du formulaire
  const handleSubmitForm = async () => {
    if (!formData.modelId || !formData.name) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      let imageUrl = formData.imageUrl;
      
      // Si un fichier a été téléchargé, l'envoyer à Supabase Storage
      if (uploadedFile) {
        const uploadedImageUrl = await uploadModelImage(uploadedFile, formData.modelId);
        if (!uploadedImageUrl) {
          throw new Error("Échec du téléchargement de l'image.");
        }
        imageUrl = uploadedImageUrl;
      }
      
      if (editingImage) {
        // Mise à jour d'une image existante
        const updatedImage = await updateModelImageInSupabase({
          ...formData,
          imageUrl: imageUrl
        });
        
        if (!updatedImage) {
          throw new Error("Échec de la mise à jour de l'image.");
        }
        
        // Mettre à jour l'état local
        setModelImages(modelImages.map(img => 
          img.id === updatedImage.id ? updatedImage : img
        ));
        
        toast({
          title: "Succès",
          description: "L'image a été mise à jour avec succès."
        });
      } else {
        // Ajout d'une nouvelle image
        const newImage = await addModelImageToSupabase({
          modelId: formData.modelId,
          imageUrl: imageUrl,
          isDefault: formData.isDefault,
          name: formData.name
        });
        
        if (!newImage) {
          throw new Error("Échec de l'ajout de l'image.");
        }
        
        // Mettre à jour l'état local
        setModelImages([...modelImages, newImage]);
        
        toast({
          title: "Succès",
          description: "L'image a été ajoutée avec succès."
        });
      }
      
      // Réinitialiser le formulaire et fermer le dialogue
      setFormData({
        id: "",
        modelId: "",
        imageUrl: "",
        isDefault: false,
        name: ""
      });
      setEditingImage(null);
      setUploadedFile(null);
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error("Erreur lors de la soumission du formulaire:", error);
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Une erreur est survenue. Veuillez réessayer.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Gérer la suppression d'une image
  const handleDeleteImage = async (id: string) => {
    try {
      const success = await deleteModelImageFromSupabase(id);
      
      if (!success) {
        throw new Error("Échec de la suppression de l'image.");
      }
      
      // Mettre à jour l'état local
      setModelImages(modelImages.filter(img => img.id !== id));
      
      toast({
        title: "Succès",
        description: "L'image a été supprimée avec succès."
      });
    } catch (error) {
      console.error("Erreur lors de la suppression de l'image:", error);
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Une erreur est survenue. Veuillez réessayer.",
        variant: "destructive"
      });
    }
  };

  // Définir une image comme par défaut
  const handleSetDefault = async (id: string) => {
    try {
      const imageToSetDefault = modelImages.find(img => img.id === id);
      if (!imageToSetDefault) return;
      
      const updatedImage = await updateModelImageInSupabase({
        ...imageToSetDefault,
        isDefault: true
      });
      
      if (!updatedImage) {
        throw new Error("Échec de la mise à jour de l'image par défaut.");
      }
      
      // Mettre à jour l'état local
      setModelImages(modelImages.map(img => 
        img.modelId === updatedImage.modelId 
          ? { ...img, isDefault: img.id === updatedImage.id }
          : img
      ));
      
      toast({
        title: "Succès",
        description: "L'image par défaut a été mise à jour avec succès."
      });
    } catch (error) {
      console.error("Erreur lors de la définition de l'image par défaut:", error);
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Une erreur est survenue. Veuillez réessayer.",
        variant: "destructive"
      });
    }
  };

  // Obtenir le nom du modèle à partir de l'ID
  const getModelName = (modelId: string) => {
    const model = phoneModels.find(m => m.id === modelId);
    return model ? `${model.brand} ${model.name}` : "Inconnu";
  };

  return (
    <AdminLayout>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Gestion des images de modèles</h1>
        <div className="flex gap-2">
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une image
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>{editingImage ? "Modifier une image" : "Ajouter une image"}</DialogTitle>
                <DialogDescription>
                  {editingImage 
                    ? "Modifiez les informations de l'image du modèle." 
                    : "Ajoutez une nouvelle image pour un modèle de téléphone."}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nom de l'image</Label>
                  <Input 
                    id="name" 
                    value={formData.name} 
                    onChange={(e) => setFormData({...formData, name: e.target.value})} 
                    placeholder="ex: iPhone 15 Pro - Standard"
                    disabled={isSubmitting}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="modelId">Modèle de téléphone</Label>
                  <Select 
                    value={formData.modelId} 
                    onValueChange={(value) => setFormData({...formData, modelId: value})}
                    disabled={isSubmitting}
                  >
                    <SelectTrigger id="modelId">
                      <SelectValue placeholder="Sélectionner un modèle" />
                    </SelectTrigger>
                    <SelectContent>
                      {phoneModels.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          {model.brand} {model.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="imageUrl">URL de l'image</Label>
                  <Input 
                    id="imageUrl" 
                    value={formData.imageUrl} 
                    onChange={(e) => setFormData({...formData, imageUrl: e.target.value})} 
                    placeholder="ex: /images/phone-cases/iphone/iphone15pro.png"
                    disabled={isSubmitting}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="imageUpload">Ou télécharger une image</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="imageUpload"
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="hidden"
                      ref={fileInputRef}
                      disabled={isSubmitting}
                    />
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => fileInputRef.current?.click()}
                      className="w-full"
                      disabled={isSubmitting}
                    >
                      <Upload className="mr-2 h-4 w-4" /> Choisir un fichier
                    </Button>
                  </div>
                  {uploadedFile && (
                    <p className="text-sm text-muted-foreground mt-1">
                      Fichier sélectionné: {uploadedFile.name}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isDefault"
                    checked={formData.isDefault}
                    onChange={(e) => setFormData({...formData, isDefault: e.target.checked})}
                    className="rounded border-gray-300"
                    disabled={isSubmitting}
                    aria-labelledby="isDefault-label"
                    title="Image par défaut pour ce modèle"
                  />
                  <Label htmlFor="isDefault">Image par défaut pour ce modèle</Label>
                </div>
                
                {(formData.imageUrl || uploadedFile) && (
                  <div className="flex justify-center p-2 bg-gray-50 rounded-md">
                    {uploadedFile ? (
                      <img 
                        src={URL.createObjectURL(uploadedFile)} 
                        alt="Aperçu de l'image" 
                        className="h-40 object-contain"
                      />
                    ) : formData.imageUrl ? (
                      <img 
                        src={formData.imageUrl} 
                        alt="Aperçu de l'image" 
                        className="h-40 object-contain"
                      />
                    ) : null}
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setIsAddDialogOpen(false);
                    setEditingImage(null);
                    setUploadedFile(null);
                  }}
                  disabled={isSubmitting}
                >
                  Annuler
                </Button>
                <Button 
                  onClick={handleSubmitForm} 
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Traitement...
                    </>
                  ) : (
                    "Enregistrer"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="grid gap-2 flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Rechercher une image..." 
                  className="w-full pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="grid gap-2 w-full md:w-[180px]">
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger>
                  <SelectValue placeholder="Tous les modèles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les modèles</SelectItem>
                  {phoneModels.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.brand} {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Chargement des images...</span>
            </div>
          ) : filteredImages.length === 0 ? (
            <div className="text-center p-8 text-muted-foreground">
              {searchQuery || selectedModel !== "all" ? (
                <p>Aucune image ne correspond à votre recherche.</p>
              ) : (
                <p>Aucune image n'a été ajoutée. Cliquez sur "Ajouter une image" pour commencer.</p>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Image</TableHead>
                  <TableHead>Nom</TableHead>
                  <TableHead>Modèle</TableHead>
                  <TableHead>Par défaut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredImages.map((image) => (
                  <TableRow key={image.id}>
                    <TableCell>
                      <div className="relative h-16 w-8">
                        <img 
                          src={image.imageUrl} 
                          alt={image.name} 
                          className="h-full object-contain"
                        />
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{image.name}</TableCell>
                    <TableCell>{getModelName(image.modelId)}</TableCell>
                    <TableCell>
                      {image.isDefault ? (
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                          Par défaut
                        </span>
                      ) : (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleSetDefault(image.id)}
                        >
                          Définir par défaut
                        </Button>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleEditImage(image)}
                        >
                          <Edit size={16} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="text-destructive hover:text-destructive/80"
                          onClick={() => handleDeleteImage(image.id)}
                          disabled={image.isDefault}
                        >
                          <Trash2 size={16} />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </AdminLayout>
  );
}