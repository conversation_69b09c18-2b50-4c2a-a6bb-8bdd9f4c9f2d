const fs = require('fs');
const path = require('path');

// Fonction pour créer un répertoire s'il n'existe pas
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`Création du répertoire: ${dirPath}`);
    fs.mkdirSync(dirPath, { recursive: true });
    return true;
  }
  return false;
}

// Fonction pour créer une image SVG de base
function createBasicSvgImage(text, subtext = '') {
  return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="600" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="500" height="300" rx="5" fill="#E5E7EB"/>
  <text x="300" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">${text}</text>
  ${subtext ? `<text x="300" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">${subtext}</text>` : ''}
</svg>`;
}

// Fonction pour créer une image PNG à partir d'un SVG
function createPngFromSvg(svgContent, outputPath) {
  try {
    fs.writeFileSync(outputPath, svgContent);
    console.log(`Image créée: ${outputPath}`);
    return true;
  } catch (error) {
    console.error(`Erreur lors de la création de l'image ${outputPath}:`, error);
    return false;
  }
}

// Fonction pour vérifier et créer les images de promotion
function checkAndCreatePromoImages() {
  console.log("Vérification des images de promotion...");
  
  const promosDir = path.join(process.cwd(), 'public', 'images', 'promos');
  ensureDirectoryExists(promosDir);
  
  // Vérifier les sous-dossiers
  const subfolders = ['seasonal', 'bundle', 'new'];
  
  subfolders.forEach(folder => {
    const folderPath = path.join(promosDir, folder);
    ensureDirectoryExists(folderPath);
    
    // Créer les images spécifiques pour chaque dossier
    if (folder === 'seasonal') {
      const imagePath = path.join(folderPath, 'fete-des-meres.png');
      if (!fs.existsSync(imagePath)) {
        createPngFromSvg(
          createBasicSvgImage('Fête des Mères', '20% de réduction'),
          imagePath
        );
      }
    } else if (folder === 'bundle') {
      const imagePath = path.join(folderPath, 'pack-famille.png');
      if (!fs.existsSync(imagePath)) {
        createPngFromSvg(
          createBasicSvgImage('Pack Famille', '25% de réduction'),
          imagePath
        );
      }
    } else if (folder === 'new') {
      const imagePath = path.join(folderPath, 'bienvenue.png');
      if (!fs.existsSync(imagePath)) {
        createPngFromSvg(
          createBasicSvgImage('Offre de Bienvenue', '15% de réduction'),
          imagePath
        );
      }
    }
    
    // Créer une image par défaut pour le dossier
    const defaultImagePath = path.join(folderPath, `${folder}.png`);
    if (!fs.existsSync(defaultImagePath)) {
      createPngFromSvg(
        createBasicSvgImage(folder.charAt(0).toUpperCase() + folder.slice(1)),
        defaultImagePath
      );
    }
  });
  
  // Créer les bannières
  const bannersDir = path.join(process.cwd(), 'public', 'images', 'banners', 'promotions');
  ensureDirectoryExists(bannersDir);
  
  for (let i = 1; i <= 3; i++) {
    const bannerPath = path.join(bannersDir, `banner${i}.png`);
    if (!fs.existsSync(bannerPath)) {
      createPngFromSvg(
        createBasicSvgImage(`Bannière ${i}`, 'Promotion spéciale'),
        bannerPath
      );
    }
  }
}

// Fonction pour vérifier et créer les images de la galerie
function checkAndCreateGalleryImages() {
  console.log("Vérification des images de la galerie...");
  
  const galleryDir = path.join(process.cwd(), 'public', 'images', 'gallery');
  ensureDirectoryExists(galleryDir);
  
  const variantsDir = path.join(galleryDir, 'variants');
  ensureDirectoryExists(variantsDir);
  
  // Liste des variantes
  const variants = [
    'vagues-abstraites',
    'fleurs-tropicales',
    'galaxie-cosmique',
    'marbre-elegant',
    'retro-synthwave',
    'montagnes-minimalistes',
    'motif-geometrique',
    'neon-urbain',
    'mandala-zen',
    'animaux-polygonaux',
    'typographie-creative'
  ];
  
  variants.forEach(variant => {
    const variantDir = path.join(variantsDir, variant);
    ensureDirectoryExists(variantDir);
    
    const imagePath = path.join(variantDir, `${variant}.png`);
    if (!fs.existsSync(imagePath)) {
      const displayName = variant
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      
      createPngFromSvg(
        createBasicSvgImage(displayName, 'Design personnalisé'),
        imagePath
      );
    }
  });
}

// Fonction principale
function main() {
  console.log("Début de la vérification et correction des images...");
  
  // Vérifier et créer les images de promotion
  checkAndCreatePromoImages();
  
  // Vérifier et créer les images de la galerie
  checkAndCreateGalleryImages();
  
  console.log("Vérification et correction des images terminées.");
}

// Exécuter la fonction principale
main();
