# Guide pour les images du site HCP Design CI

Ce guide explique comment gérer les images du site pour qu'elles s'affichent correctement.

## Format des images

Le site est configuré pour utiliser des images au format PNG ou JPG/JPEG. Les images SVG ne sont plus utilisées.

## Structure des dossiers

Les images sont organisées dans des dossiers selon la structure suivante :

```
public/
  images/
    gallery/
      variants/
        vagues-abstraites/
          vagues-abstraites.png
        fleurs-tropicales/
          fleurs-tropicales.png
        galaxie-cosmique/
          galaxie-cosmique.png
        marbre-elegant/
          marbre-elegant.png
        retro-synthwave/
          retro-synthwave.png
        ...
    products/
      personnalisation/
        personnalisation.png
      ...
    gifts/
      anniversary-pack.jpg
      wedding-pack.jpg
      dinner-pack.jpg
      corporate-pack.jpg
      baby-pack.jpg
      graduation-pack.jpg
      ...
    ...
```

## Comment ajouter ou remplacer des images

1. **Identifiez le bon dossier** : Chaque image doit être placée dans le dossier correspondant à sa catégorie.

2. **Utilisez le bon format** : Utilisez des images au format PNG ou JPG/JPEG.

3. **Utilisez le bon nom de fichier** : Le nom du fichier doit correspondre au nom du dossier dans lequel il se trouve.

4. **Remplacez les images existantes** : Pour mettre à jour une image, remplacez simplement le fichier existant par votre nouvelle image.

## Exemple : Mettre à jour une image de la galerie

Pour mettre à jour l'image "Vagues Abstraites" dans la galerie :

1. Préparez votre nouvelle image au format PNG ou JPG
2. Renommez-la en `vagues-abstraites.png` ou `vagues-abstraites.jpg`
3. Placez-la dans le dossier `public/images/gallery/variants/vagues-abstraites/`

## Exemple : Ajouter une nouvelle variante à la galerie

Pour ajouter une nouvelle variante "Forêt Enchantée" à la galerie :

1. Créez un nouveau dossier `public/images/gallery/variants/foret-enchantee/`
2. Préparez votre image au format PNG ou JPG
3. Renommez-la en `foret-enchantee.png` ou `foret-enchantee.jpg`
4. Placez-la dans le dossier que vous venez de créer
5. Modifiez le code pour ajouter cette nouvelle variante (cette étape nécessite une modification du code)

## Résolution des problèmes courants

### Les images ne s'affichent pas

1. **Vérifiez le format** : Assurez-vous que l'image est au format PNG ou JPG/JPEG.
2. **Vérifiez le chemin** : Assurez-vous que l'image est placée dans le bon dossier.
3. **Vérifiez le nom** : Assurez-vous que le nom du fichier correspond au nom attendu par le code.
4. **Vérifiez la casse** : Les noms de fichiers et de dossiers sont sensibles à la casse.
5. **Vérifiez les permissions** : Assurez-vous que les fichiers ont les bonnes permissions.

### Les images s'affichent localement mais pas en production

1. **Vérifiez que les images sont bien committées** : Assurez-vous que les images sont bien ajoutées au dépôt Git.
2. **Vérifiez que les images sont bien poussées** : Assurez-vous que les images sont bien poussées vers le dépôt distant.
3. **Vérifiez les chemins en production** : Assurez-vous que les chemins des images sont corrects en production.

## Scripts utiles

Plusieurs scripts ont été créés pour vous aider à gérer les images :

1. `scripts/create-png-placeholders.ps1` : Crée des fichiers PNG vides pour chaque fichier SVG existant.
2. `scripts/convert-svg-to-png-references.js` : Modifie les références aux images SVG en PNG dans le code.
3. `scripts/check-image-references.js` : Vérifie si les images référencées dans le code existent dans le système de fichiers.
4. `scripts/create-missing-images.ps1` : Crée des fichiers PNG ou JPG vides pour les images manquantes.

Pour exécuter ces scripts, utilisez les commandes suivantes :

```bash
# Pour créer des fichiers PNG vides pour chaque fichier SVG existant
powershell -ExecutionPolicy Bypass -File scripts\create-png-placeholders.ps1

# Pour modifier les références aux images SVG en PNG dans le code
node scripts\convert-svg-to-png-references.js

# Pour vérifier si les images référencées dans le code existent
node scripts\check-image-references.js

# Pour créer des fichiers PNG ou JPG vides pour les images manquantes
powershell -ExecutionPolicy Bypass -File scripts\create-missing-images.ps1
```

## Conclusion

En suivant ces instructions, vous devriez être en mesure de gérer efficacement les images du site HCP Design CI. Si vous rencontrez des problèmes, n'hésitez pas à consulter les scripts fournis ou à demander de l'aide.
