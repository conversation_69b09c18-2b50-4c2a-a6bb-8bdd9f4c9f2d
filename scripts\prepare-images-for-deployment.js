const fs = require('fs');
const path = require('path');

// Fonction pour créer un répertoire s'il n'existe pas
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`Création du répertoire: ${dirPath}`);
    fs.mkdirSync(dirPath, { recursive: true });
    return true;
  }
  return false;
}

// Fonction pour créer un fichier .gitkeep dans un répertoire
function createGitKeep(dirPath) {
  const gitkeepPath = path.join(dirPath, '.gitkeep');
  if (!fs.existsSync(gitkeepPath)) {
    fs.writeFileSync(gitkeepPath, '');
    console.log(`Fichier .gitkeep créé dans: ${dirPath}`);
  }
}

// Fonction pour créer une image SVG de base
function createBasicSvgImage(text, subtext = '') {
  return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="600" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="500" height="300" rx="5" fill="#E5E7EB"/>
  <text x="300" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">${text}</text>
  ${subtext ? `<text x="300" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">${subtext}</text>` : ''}
</svg>`;
}

// Fonction pour créer une image PNG à partir d'un SVG
function createPngFromSvg(svgContent, outputPath) {
  try {
    fs.writeFileSync(outputPath, svgContent);
    console.log(`Image créée: ${outputPath}`);
    return true;
  } catch (error) {
    console.error(`Erreur lors de la création de l'image ${outputPath}:`, error);
    return false;
  }
}

// Fonction pour créer un fichier README.md dans un répertoire
function createReadme(dirPath, content) {
  const readmePath = path.join(dirPath, 'README.md');
  if (!fs.existsSync(readmePath)) {
    fs.writeFileSync(readmePath, content);
    console.log(`Fichier README.md créé dans: ${dirPath}`);
  }
}

// Fonction pour préparer les images de promotion
function preparePromoImages() {
  console.log("Préparation des images de promotion...");
  
  const promosDir = path.join(process.cwd(), 'public', 'images', 'promos');
  ensureDirectoryExists(promosDir);
  
  // Créer un README pour le dossier promos
  createReadme(promosDir, `# Images des promotions

Ce dossier contient les images utilisées pour les promotions sur le site.

## Structure des dossiers

- **seasonal/** - Images pour les promotions saisonnières
- **bundle/** - Images pour les promotions de packs
- **new/** - Images pour les promotions destinées aux nouveaux clients
- **banners/** - Bannières animées pour les promotions

## Recommandations pour les images

- **Bannières animées** : 1200x400 pixels
- **Images de promotions** : 600x400 pixels
- Format : PNG pour une meilleure compatibilité
`);
  
  // Vérifier les sous-dossiers
  const subfolders = ['seasonal', 'bundle', 'new'];
  
  subfolders.forEach(folder => {
    const folderPath = path.join(promosDir, folder);
    ensureDirectoryExists(folderPath);
    createGitKeep(folderPath);
    
    // Créer les images spécifiques pour chaque dossier
    if (folder === 'seasonal') {
      const imagePath = path.join(folderPath, 'fete-des-meres.png');
      if (!fs.existsSync(imagePath)) {
        createPngFromSvg(
          createBasicSvgImage('Fête des Mères', '20% de réduction'),
          imagePath
        );
      }
    } else if (folder === 'bundle') {
      const imagePath = path.join(folderPath, 'pack-famille.png');
      if (!fs.existsSync(imagePath)) {
        createPngFromSvg(
          createBasicSvgImage('Pack Famille', '25% de réduction'),
          imagePath
        );
      }
    } else if (folder === 'new') {
      const imagePath = path.join(folderPath, 'bienvenue.png');
      if (!fs.existsSync(imagePath)) {
        createPngFromSvg(
          createBasicSvgImage('Offre de Bienvenue', '15% de réduction'),
          imagePath
        );
      }
    }
    
    // Créer une image par défaut pour le dossier
    const defaultImagePath = path.join(folderPath, `${folder}.png`);
    if (!fs.existsSync(defaultImagePath)) {
      createPngFromSvg(
        createBasicSvgImage(folder.charAt(0).toUpperCase() + folder.slice(1)),
        defaultImagePath
      );
    }
  });
  
  // Créer les bannières
  const bannersDir = path.join(process.cwd(), 'public', 'images', 'banners', 'promotions');
  ensureDirectoryExists(bannersDir);
  createGitKeep(bannersDir);
  
  for (let i = 1; i <= 3; i++) {
    const bannerPath = path.join(bannersDir, `banner${i}.png`);
    if (!fs.existsSync(bannerPath)) {
      createPngFromSvg(
        createBasicSvgImage(`Bannière ${i}`, 'Promotion spéciale'),
        bannerPath
      );
    }
  }
}

// Fonction pour préparer les images de la galerie
function prepareGalleryImages() {
  console.log("Préparation des images de la galerie...");
  
  const galleryDir = path.join(process.cwd(), 'public', 'images', 'gallery');
  ensureDirectoryExists(galleryDir);
  
  // Créer un README pour le dossier gallery
  createReadme(galleryDir, `# Images de la galerie

Ce dossier contient les images utilisées pour la galerie de designs sur le site.

## Structure des dossiers

- **variants/** - Différentes variantes de designs disponibles
`);
  
  const variantsDir = path.join(galleryDir, 'variants');
  ensureDirectoryExists(variantsDir);
  createGitKeep(variantsDir);
  
  // Créer un README pour le dossier variants
  createReadme(variantsDir, `# Variantes de designs

Ce dossier contient les différentes variantes de designs disponibles dans la galerie.

## Liste des variantes

- Vagues Abstraites
- Fleurs Tropicales
- Galaxie Cosmique
- Marbre Élégant
- Rétro Synthwave
- Montagnes Minimalistes
- Motif Géométrique
- Néon Urbain
- Mandala Zen
- Animaux Polygonaux
- Typographie Créative
`);
  
  // Liste des variantes
  const variants = [
    'vagues-abstraites',
    'fleurs-tropicales',
    'galaxie-cosmique',
    'marbre-elegant',
    'retro-synthwave',
    'montagnes-minimalistes',
    'motif-geometrique',
    'neon-urbain',
    'mandala-zen',
    'animaux-polygonaux',
    'typographie-creative'
  ];
  
  variants.forEach(variant => {
    const variantDir = path.join(variantsDir, variant);
    ensureDirectoryExists(variantDir);
    createGitKeep(variantDir);
    
    const imagePath = path.join(variantDir, `${variant}.png`);
    if (!fs.existsSync(imagePath)) {
      const displayName = variant
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      
      createPngFromSvg(
        createBasicSvgImage(displayName, 'Design personnalisé'),
        imagePath
      );
    }
  });
  
  // Créer une image placeholder pour la galerie
  const placeholderPath = path.join(galleryDir, 'gallery-placeholder.svg');
  if (!fs.existsSync(placeholderPath)) {
    fs.writeFileSync(placeholderPath, createBasicSvgImage('Galerie', 'Image placeholder'));
    console.log(`Image placeholder créée: ${placeholderPath}`);
  }
}

// Fonction principale
function main() {
  console.log("Début de la préparation des images pour le déploiement...");
  
  // Préparer les images de promotion
  preparePromoImages();
  
  // Préparer les images de la galerie
  prepareGalleryImages();
  
  console.log("Préparation des images pour le déploiement terminée.");
}

// Exécuter la fonction principale
main();
