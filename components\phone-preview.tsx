import React from "react"
import Image from "next/image"
import styles from "./phone-preview.module.css"
import "./phone-preview-dynamic.css"

export interface PhonePreviewProps {
  model: {
    id: string
    name: string
    image: string
    dimensions: {
      width: number
      height: number
    }
  }
  backgroundColor: string
  uploadedImage: string | null
  customText: string
  textColor: string
  textSize: number
  textPosX: number
  textPosY: number
  textRotation: number
  imageZoom: number
  imageRotation: number
  imageOffsetX: number
  imageOffsetY: number
}

export default function PhonePreview({
  model,
  backgroundColor,
  uploadedImage,
  customText,
  textColor,
  textSize,
  textPosX,
  textPosY,
  textRotation,
  imageZoom,
  imageRotation,
  imageOffsetX,
  imageOffsetY
}: PhonePreviewProps) {
  // Calculate the dimensions for the phone display
  const phoneWidth = 280
  const phoneHeight = (model.dimensions.height / model.dimensions.width) * phoneWidth

  // Function to get the correct frame path based on model ID
  const getFramePath = (modelId: string) => {
    // Map model IDs to their corresponding folder structure
    const modelMapping: { [key: string]: string } = {
      // iPhone models
      'iphone12': 'iphone/iphone-12',
      'iphone12pro': 'iphone/iphone-12-pro',
      'iphone12promax': 'iphone/iphone-12-pro-max',
      'iphone13': 'iphone/iphone-13',
      'iphone13pro': 'iphone/iphone-13-pro',
      'iphone13promax': 'iphone/iphone-13-pro-max',
      'iphone14': 'iphone/iphone-14',
      'iphone14plus': 'iphone/iphone-14-plus',
      'iphone14pro': 'iphone/iphone-14-pro',
      'iphone14promax': 'iphone/iphone-14-pro-max',
      'iphone15': 'iphone/iphone-15',
      'iphone15plus': 'iphone/iphone-15-plus',
      'iphone15pro': 'iphone/iphone-15-pro',
      'iphone15promax': 'iphone/iphone-15-pro-max',
      'iphone16': 'iphone/iphone-16',
      'iphone16plus': 'iphone/iphone-16-plus',
      'iphone16pro': 'iphone/iphone-16-pro',
      'iphone16promax': 'iphone/iphone-16-pro-max',
      'iphonese2022': 'iphone/iphone-se-2022',
      // Samsung models
      'samsungs22': 'samsung/galaxy-s22',
      'samsungs22plus': 'samsung/galaxy-s22-plus',
      'samsungs22ultra': 'samsung/galaxy-s22-ultra',
      'samsungs23': 'samsung/galaxy-s23',
      'samsungs23plus': 'samsung/galaxy-s23-plus',
      'samsungs23ultra': 'samsung/galaxy-s23-ultra',
      'samsungs24': 'samsung/galaxy-s24',
      'samsungs24plus': 'samsung/galaxy-s24-plus',
      'samsungs24ultra': 'samsung/galaxy-s24-ultra',
      // Google Pixel models
      'pixel6': 'google/pixel-6',
      'pixel6pro': 'google/pixel-6-pro',
      'pixel7': 'google/pixel-7',
      'pixel7pro': 'google/pixel-7-pro',
      'pixel8': 'google/pixel-8',
      'pixel8pro': 'google/pixel-8-pro',
    }

    const folderPath = modelMapping[modelId]
    if (folderPath) {
      return `/images/phone-cases/${folderPath}/frame.png`
    }
    
    // Fallback to placeholder if model not found
    return model.image
  }

  const framePath = getFramePath(model.id)

  // Log pour le débogage
  React.useEffect(() => {
    console.log("PhonePreview - Modèle sélectionné:", model.id, model.name);
    console.log("PhonePreview - Chemin du frame:", framePath);
  }, [model, framePath]);

  // Set CSS variables for dynamic styles
  React.useEffect(() => {
    document.documentElement.style.setProperty('--phone-width', `${phoneWidth}px`);
    document.documentElement.style.setProperty('--phone-height', `${phoneHeight}px`);
    document.documentElement.style.setProperty('--background-color', backgroundColor);
    document.documentElement.style.setProperty('--image-zoom', `${imageZoom}`);
    document.documentElement.style.setProperty('--image-rotation', `${imageRotation}deg`);
    document.documentElement.style.setProperty('--image-offset-x', `${imageOffsetX}%`);
    document.documentElement.style.setProperty('--image-offset-y', `${imageOffsetY}%`);
    document.documentElement.style.setProperty('--text-color', textColor);
    document.documentElement.style.setProperty('--text-size', `${textSize}px`);
    document.documentElement.style.setProperty('--text-pos-x', `${textPosX}%`);
    document.documentElement.style.setProperty('--text-pos-y', `${textPosY}%`);
    document.documentElement.style.setProperty('--text-rotation', `${textRotation}deg`);
  }, [
    phoneWidth, phoneHeight, backgroundColor,
    imageZoom, imageRotation, imageOffsetX, imageOffsetY,
    textColor, textSize, textPosX, textPosY, textRotation
  ]);

  return (
    <div className={`${styles.phoneContainer} phone-preview-container phone-preview-dimensions`}>
      {/* Background color */}
      <div className={`${styles.backgroundContainer} phone-preview-background`}>
        {/* Uploaded image */}
        {uploadedImage && (
          <div className={styles.imageContainer}>
            <div className={`${styles.imageWrapper} phone-preview-image-transform`}>
              <Image
                src={uploadedImage}
                alt="Custom design"
                width={500}
                height={500}
                className="object-contain"
                onError={(e) => {
                  // Si l'image ne peut pas être chargée, utiliser une image de remplacement
                  const target = e.target as HTMLImageElement;
                  target.src = "/images/phone-cases/phone-case-placeholder.png";
                  target.onerror = null; // Éviter les boucles infinies
                }}
              />
            </div>
          </div>
        )}

        {/* Custom text */}
        {customText && (
          <div className={styles.textContainer}>
            <div className={`${styles.textWrapper} phone-preview-text`}>
              {customText}
            </div>
          </div>
        )}
      </div>

      {/* Phone frame overlay */}
      <div className={styles.phoneFrame}>
        <Image
          src={framePath}
          alt={model.name}
          fill
          className={styles.phoneImage}
          onError={(e) => {
            console.error("Erreur de chargement de l'image du cadre:", framePath);
            const target = e.target as HTMLImageElement;
            target.src = "/images/placeholder.svg?height=400&width=600";
            target.onerror = null; // Éviter les boucles infinies
          }}
          priority={true} // Charger l'image en priorité
        />
      </div>
    </div>
  )
}
