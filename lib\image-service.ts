// Types pour les images
export interface SiteImage {
  id: string;
  name: string;
  path: string;
  category: string;
  description: string;
  width: number;
  height: number;
  alt: string;
  createdAt: string;
  updatedAt: string;
}

import { supabase } from './supabase';

export const ImageService = {
  // R<PERSON>cupérer toutes les images depuis Supabase
  getAllImages: async () => {
    const { data, error } = await supabase.from('images').select('*').order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  // Récupérer une image par son ID
  getImageById: async (id: string) => {
    const { data, error } = await supabase.from('images').select('*').eq('id', id).single();
    if (error) throw error;
    return data;
  },

  // Ajouter une nouvelle image
  addImage: async (image: Omit<SiteImage, 'id'>) => {
    const { data, error } = await supabase.from('images').insert([image]).select().single();
    if (error) throw error;
    return data;
  },

  // Mettre à jour une image existante
  updateImage: async (id: string, updatedImage: Partial<SiteImage>) => {
    const { data, error } = await supabase.from('images').update(updatedImage).eq('id', id).select().single();
    if (error) throw error;
    return data;
  },

  // Supprimer une image
  deleteImage: async (id: string) => {
    const { error } = await supabase.from('images').delete().eq('id', id);
    if (error) throw error;
    return true;
  },

  // Upload une image dans Supabase Storage
  uploadImageToStorage: async (file: File, category: string) => {
    const filePath = `${category}/${Date.now()}-${file.name}`;
    const { data, error } = await supabase.storage
      .from('site-images')
      .upload(filePath, file, { upsert: true });
    if (error) throw error;
    const { data: publicUrlData } = supabase.storage
      .from('site-images')
      .getPublicUrl(filePath);
    return publicUrlData.publicUrl;
  },

  // Ajoutez cette méthode à l'intérieur de l'objet ImageService
  getBase64ByPath: async (path: string): Promise<string | null> => {
    try {
      // Implémentez ici la logique pour convertir le chemin de l'image en base64
      // Ceci est une implémentation de remplacement
      // Vous devrez peut-être récupérer l'image et la convertir en base64
      console.warn("getBase64ByPath is a placeholder and needs full implementation.");
      return path; // Retourne le chemin original pour l'instant
    } catch (error) {
      console.error('Error getting base64 for image:', error);
      return null;
    }
  } // Pas de virgule ici si c'est le dernier élément
};
