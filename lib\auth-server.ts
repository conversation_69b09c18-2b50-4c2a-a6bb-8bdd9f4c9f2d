/**
 * Utilitaires d'authentification côté serveur
 */
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { NextRequest, NextResponse } from 'next/server';
import { isAdminEmail } from './admin-config';

/**
 * Crée un client Supabase côté serveur
 */
export function createServerSupabaseClient() {
  const cookieStore = cookies();
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          cookieStore.set({ name, value, ...options });
        },
        remove(name: string, options: any) {
          cookieStore.set({ name, value: '', ...options });
        },
      },
    }
  );
}

/**
 * Vérifie si l'utilisateur est authentifié
 * @returns L'utilisateur si authentifié, null sinon
 */
export async function getAuthenticatedUser() {
  const supabase = createServerSupabaseClient();
  const { data: { session } } = await supabase.auth.getSession();
  
  if (!session) {
    return null;
  }
  
  return session.user;
}

/**
 * Vérifie si l'utilisateur est un administrateur
 * @returns L'utilisateur si c'est un administrateur, null sinon
 */
export async function getAuthenticatedAdmin() {
  const user = await getAuthenticatedUser();
  
  if (!user || !isAdminEmail(user.email)) {
    return null;
  }
  
  return user;
}

/**
 * Middleware pour protéger les routes API d'administration
 * @param req Requête Next.js
 * @param handler Gestionnaire de la route API
 */
export async function withAdminAuth(
  req: NextRequest,
  handler: (req: NextRequest, user: any) => Promise<NextResponse>
) {
  const user = await getAuthenticatedAdmin();
  
  if (!user) {
    return NextResponse.json(
      { error: 'Non autorisé' },
      { status: 401 }
    );
  }
  
  return handler(req, user);
}
