# Configuration de Supabase pour HCP-DESIGN CI

Ce document explique comment configurer Supabase pour le projet HCP-DESIGN CI.

## Prérequis

- Un compte Supabase (gratuit pour commencer)
- Node.js et npm installés sur votre machine

## Étapes de configuration

### 1. <PERSON><PERSON>er un projet Supabase

1. Allez sur [supabase.com](https://supabase.com/) et connectez-vous
2. Créez un nouveau projet
3. Notez l'URL et la clé anon de votre projet

### 2. Configurer les variables d'environnement

Créez un fichier `.env.local` à la racine de votre projet avec les informations suivantes :

```
NEXT_PUBLIC_SUPABASE_URL=votre-url-supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=votre-clé-anon-supabase
```

### 3. Exécuter le script SQL

Vous avez deux options pour exécuter le script SQL :

#### Option 1 : Via l'interface Supabase (recommandé)

1. Connectez-vous à votre projet Supabase
2. <PERSON><PERSON> dans l'onglet "SQL Editor"
3. Créez un nouveau script
4. <PERSON><PERSON><PERSON> le contenu du fichier `schema.sql` dans l'éditeur
5. Exécutez le script

#### Option 2 : Via le script Node.js

1. Installez les dépendances nécessaires :
   ```
   npm install dotenv @supabase/supabase-js
   ```

2. Exécutez le script :
   ```
   node scripts/execute-schema.js
   ```

### 4. Vérifier la configuration

1. Lancez le serveur de développement :
   ```
   npm run dev
   ```

2. Accédez à la page de test :
   ```
   http://localhost:3000/supabase-test
   ```

3. Vérifiez que la connexion à Supabase fonctionne correctement

## Structure de la base de données

Le schéma SQL crée les tables suivantes :

- `profiles` : Profils des utilisateurs (extension de la table auth.users)
- `products` : Produits disponibles à la vente
- `cart_items` : Articles dans le panier des utilisateurs
- `orders` : Commandes passées par les utilisateurs
- `order_items` : Articles dans les commandes
- `phone_models` : Modèles de téléphone disponibles
- `phone_cases` : Coques de téléphone disponibles

## Politiques de sécurité (RLS)

Le schéma SQL configure également des politiques de sécurité Row Level Security (RLS) pour chaque table, garantissant que :

- Les utilisateurs ne peuvent voir et modifier que leurs propres données
- Tout le monde peut voir les produits, modèles de téléphone et coques
- Seuls les utilisateurs authentifiés peuvent créer des commandes

## Données initiales

Le schéma SQL insère également des données initiales pour :

- Produits (tasses, t-shirts, tapis de souris, coussins, porte-clés)
- Modèles de téléphone (iPhone, Samsung, Xiaomi, Google)
- Coques de téléphone

## Utilisation dans le code

Le projet utilise les hooks suivants pour interagir avec Supabase :

- `useAuth` : Gestion de l'authentification
- `useCart` : Gestion du panier d'achat

Ces hooks sont disponibles dans le dossier `hooks/` et sont utilisés dans les composants correspondants.
