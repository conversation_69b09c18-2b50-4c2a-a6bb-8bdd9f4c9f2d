"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { X } from "lucide-react"

export default function AnnouncementBanner() {
  const [isVisible, setIsVisible] = useState(true)
  const [bannerText, setBannerText] = useState("Livraison gratuite pour toutes les commandes au-dessus de 15 000 FCFA")
  
  // Vérifier si la bannière a été fermée précédemment
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const bannerState = localStorage.getItem("announcementBannerVisible")
      if (bannerState === "false") {
        setIsVisible(false)
      }
    }
  }, [])
  
  const closeBanner = () => {
    setIsVisible(false)
    if (typeof window !== 'undefined') {
      localStorage.setItem("announcementBannerVisible", "false")
    }
  }
  
  if (!isVisible) return null
  
  return (
    <div className="bg-purple-600 text-white py-2 px-4 text-center text-sm relative">
      <p>
        {bannerText}
        {" "}
        <Link href="/promos" className="underline font-medium">
          Voir nos promotions
        </Link>
      </p>
      <button 
        onClick={closeBanner}
        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-200"
        aria-label="Fermer la bannière"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}