import React from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function OrderDetailPage() {
  // À terme, récupérer les détails de la commande via l'ID
  // Pour l'instant, affichage statique
  return (
    <main className="container mx-auto px-4 py-8">
      <div className="bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-2">Détail de la commande</h1>
        <p className="text-lg opacity-90">Retrouvez ici les informations de votre commande.</p>
      </div>
      <div className="text-center py-12">
        <h3 className="text-xl font-medium mb-2">Aucun détail disponible pour cette commande</h3>
        <p className="text-gray-500 mb-6">La logique d'affichage des détails sera intégrée prochainement.</p>
        <Link href="/account/orders">
          <Button>Retour à mes commandes</Button>
        </Link>
      </div>
    </main>
  );
}