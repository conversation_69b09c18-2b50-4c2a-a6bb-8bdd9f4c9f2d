#!/bin/bash

# Script pour créer les dossiers des variantes de produits avec des placeholders PNG

# Dossier de base pour les produits
BASE_DIR="./public/images/products"

# Créer un placeholder PNG simple
echo "Création du placeholder PNG générique"
mkdir -p ./temp
cat > ./temp/placeholder.svg << EOF
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#F3F4F6"/>
  <rect x="100" y="100" width="200" height="200" rx="10" fill="#E5E7EB"/>
  <text x="200" y="220" font-family="Arial" font-size="20" text-anchor="middle" fill="#6B7280">Produit</text>
  <text x="200" y="250" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">HCP-DESIGN CI</text>
</svg>
EOF

# Créer les dossiers pour les variantes de tasses (mugs)
echo "Création des dossiers pour les tasses"
mkdir -p "$BASE_DIR/mugs/ceramique"
mkdir -p "$BASE_DIR/mugs/thermosensible"
mkdir -p "$BASE_DIR/mugs/isotherme"
mkdir -p "$BASE_DIR/mugs/emaillee"

# Créer les dossiers pour les variantes de t-shirts
echo "Création des dossiers pour les t-shirts"
mkdir -p "$BASE_DIR/tshirts/coton"
mkdir -p "$BASE_DIR/tshirts/premium"
mkdir -p "$BASE_DIR/tshirts/polo"
mkdir -p "$BASE_DIR/tshirts/manches-longues"

# Créer les dossiers pour les variantes de tapis de souris
echo "Création des dossiers pour les tapis de souris"
mkdir -p "$BASE_DIR/mousepads/standard"
mkdir -p "$BASE_DIR/mousepads/xxl"
mkdir -p "$BASE_DIR/mousepads/ergonomique"
mkdir -p "$BASE_DIR/mousepads/rgb"

# Créer les dossiers pour les variantes de coussins
echo "Création des dossiers pour les coussins"
mkdir -p "$BASE_DIR/cushions/decoratif"
mkdir -p "$BASE_DIR/cushions/photo"
mkdir -p "$BASE_DIR/cushions/xxl"
mkdir -p "$BASE_DIR/cushions/cervical"

# Créer les dossiers pour les variantes de porte-clés
echo "Création des dossiers pour les porte-clés"
mkdir -p "$BASE_DIR/keychains/photo"
mkdir -p "$BASE_DIR/keychains/metal"
mkdir -p "$BASE_DIR/keychains/multifonction"
mkdir -p "$BASE_DIR/keychains/bois"

# Créer un fichier placeholder.png dans chaque dossier
echo "Création des placeholders dans chaque dossier"
for dir in $(find "$BASE_DIR" -type d); do
  if [ ! -f "$dir/placeholder.png" ]; then
    echo "Création du placeholder pour $dir"
    cp ./temp/placeholder.svg "$dir/placeholder.svg"
  fi
done

echo "Création des dossiers de variantes de produits terminée"
