"use client"

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface MediaItem {
  type: 'image' | 'video';
  src: string;
  alt?: string;
  poster?: string; // Pour les vidéos
}

interface MediaBannerProps {
  mediaItems: MediaItem[];
  title: string;
  subtitle: string;
  buttonText?: string;
  buttonLink?: string;
  interval?: number; // en millisecondes
}

export function MediaBanner({
  mediaItems,
  title,
  subtitle,
  buttonText,
  buttonLink = '/',
  interval = 5000, // 5 secondes par défaut
}: MediaBannerProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (mediaItems.length <= 1) return;

    const rotateMedia = () => {
      setIsTransitioning(true);
      
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % mediaItems.length);
        setIsTransitioning(false);
      }, 500);
    };

    const intervalId = setInterval(rotateMedia, interval);
    return () => clearInterval(intervalId);
  }, [mediaItems.length, interval]);

  const handleIndicatorClick = (index: number) => {
    if (index !== currentIndex) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentIndex(index);
        setIsTransitioning(false);
      }, 500);
    }
  };

  return (
    <div className="relative w-full h-[500px] overflow-hidden">
      {/* Media Items */}
      {mediaItems.map((item, index) => (
        <div
          key={index}
          className={cn(
            "absolute inset-0 transition-all duration-500 ease-in-out",
            index === currentIndex ? "opacity-100" : "opacity-0",
            isTransitioning && index === currentIndex ? "scale-105" : "scale-100"
          )}
        >
          {item.type === 'image' ? (
            <img
              src={item.src}
              alt={item.alt || title}
              className="w-full h-full object-cover"
            />
          ) : (
            <video
              src={item.src}
              poster={item.poster}
              autoPlay
              muted
              loop
              playsInline
              className="w-full h-full object-cover"
            >
              Votre navigateur ne supporte pas les vidéos HTML5.
            </video>
          )}
          <div className="absolute inset-0 bg-black/20" />
        </div>
      ))}

      {/* Contenu - Positionné dans les 20% du bas */}
      <div className="absolute inset-x-0 bottom-0 h-[20%] flex flex-col items-center justify-center text-center px-4 md:px-8 z-10">
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="max-w-3xl"
        >
          <h1 className="text-2xl md:text-4xl font-bold text-white mb-2">{title}</h1>
          <p className="text-base md:text-lg text-white/90 mb-4">{subtitle}</p>

          {buttonText && (
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Link href={buttonLink}>
                <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white">
                  {buttonText}
                </Button>
              </Link>
            </motion.div>
          )}
        </motion.div>
      </div>

      {/* Indicateurs de navigation */}
      {mediaItems.length > 1 && (
        <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-20">
          {mediaItems.map((_, index) => (
            <button
              key={index}
              type="button"
              className={cn(
                "w-3 h-3 rounded-full transition-all duration-300",
                index === currentIndex 
                  ? "bg-white w-6" 
                  : "bg-white/50 hover:bg-white/70"
              )}
              onClick={() => handleIndicatorClick(index)}
              aria-label={`Voir le média ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Contrôles de navigation (flèches) */}
      {mediaItems.length > 1 && (
        <>
          <button
            type="button"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all z-20"
            onClick={() => handleIndicatorClick((currentIndex - 1 + mediaItems.length) % mediaItems.length)}
            aria-label="Média précédent"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            type="button"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all z-20"
            onClick={() => handleIndicatorClick((currentIndex + 1) % mediaItems.length)}
            aria-label="Média suivant"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}
    </div>
  );
}