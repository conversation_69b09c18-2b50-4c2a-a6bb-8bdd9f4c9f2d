-- Migration pour ajouter la table des images de modèles de téléphone

-- Création de la table phone_model_images si elle n'existe pas
CREATE TABLE IF NOT EXISTS public.phone_model_images (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  model_id UUID REFERENCES public.phone_models(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  image_url TEXT NOT NULL,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger pour mettre à jour le timestamp updated_at
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_phone_model_images_updated_at') THEN
    CREATE TRIGGER update_phone_model_images_updated_at
    BEFORE UPDATE ON public.phone_model_images
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- Données initiales pour les images de modèles de téléphone
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM public.phone_model_images LIMIT 1) THEN
    INSERT INTO public.phone_model_images (model_id, name, image_url, is_default)
    SELECT 
      id,
      brand || ' ' || name || ' - Standard',
      '/images/phone-cases/' || 
        CASE 
          WHEN brand = 'Apple' THEN 'iphone/'
          WHEN brand = 'Samsung' THEN 'samsung/'
          WHEN brand = 'Google' THEN 'google/'
          WHEN brand = 'Xiaomi' THEN 'xiaomi/'
          ELSE ''
        END ||
        CASE
          WHEN brand = 'Apple' AND name = 'iPhone 15 Pro' THEN 'iphone15pro.png'
          WHEN brand = 'Apple' AND name = 'iPhone 14' THEN 'iphone14.png'
          WHEN brand = 'Apple' AND name = 'iPhone 13' THEN 'iphone13.png'
          WHEN brand = 'Samsung' AND name = 'Galaxy S23' THEN 's23.png'
          WHEN brand = 'Samsung' AND name = 'Galaxy S22' THEN 's22.png'
          WHEN brand = 'Xiaomi' AND name = 'Redmi Note 12' THEN 'redminote12.png'
          WHEN brand = 'Google' AND name = 'Pixel 7' THEN 'pixel7.png'
          ELSE 'default.png'
        END,
      true
    FROM public.phone_models;
  END IF;
END
$$;