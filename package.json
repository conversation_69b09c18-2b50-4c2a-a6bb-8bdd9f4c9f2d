{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@sendgrid/mail": "^8.1.5", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/nodemailer": "^6.4.17", "@vercel/postgres": "^0.10.0", "autoprefixer": "^10.4.20", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.0.0", "embla-carousel-react": "8.5.1", "framer-motion": "^11.0.8", "input-otp": "1.4.1", "js-cookie": "^3.0.5", "lucide-react": "^0.454.0", "mailersend": "^2.5.0", "next": "15.2.4", "next-themes": "^0.4.4", "nodemailer": "^7.0.3", "react": "18.3.1", "react-day-picker": "^9.0.0", "react-dom": "18.3.1", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "^5.0.4"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "canvas": "^3.1.0", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}, "engines": {"node": "22.x"}}