# Script pour créer des fichiers PNG vides pour chaque fichier SVG
# Ce script parcourt tous les fichiers SVG du répertoire public/images
# et crée un fichier PNG vide avec le même nom dans le même répertoire

# Fonction pour créer un fichier PNG vide
function Create-Empty-PNG {
    param (
        [string]$svgPath
    )
    
    # Obtenir le chemin du fichier PNG correspondant
    $pngPath = $svgPath -replace "\.svg$", ".png"
    
    # Vérifier si le fichier PNG existe déjà
    if (Test-Path $pngPath) {
        Write-Host "Le fichier PNG existe déjà: $pngPath" -ForegroundColor Yellow
        return
    }
    
    # Créer un fichier PNG vide (1x1 pixel transparent)
    # Nous utilisons un fichier PNG de base64 encodé qui représente un pixel transparent
    $base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
    $bytes = [Convert]::FromBase64String($base64)
    
    # Écrire le fichier PNG
    [System.IO.File]::WriteAllBytes($pngPath, $bytes)
    
    Write-Host "Fichier PNG créé: $pngPath" -ForegroundColor Green
}

# Fonction pour parcourir récursivement un répertoire
function Process-Directory {
    param (
        [string]$directory
    )
    
    # Vérifier si le répertoire existe
    if (-not (Test-Path $directory)) {
        Write-Host "Le répertoire $directory n'existe pas." -ForegroundColor Red
        return
    }
    
    # Obtenir tous les fichiers SVG dans le répertoire
    $svgFiles = Get-ChildItem -Path $directory -Filter "*.svg" -File -Recurse
    
    Write-Host "Traitement de $($svgFiles.Count) fichiers SVG dans $directory..." -ForegroundColor Cyan
    
    # Créer un fichier PNG vide pour chaque fichier SVG
    foreach ($svgFile in $svgFiles) {
        Create-Empty-PNG -svgPath $svgFile.FullName
    }
}

# Fonction principale
function Main {
    # Parcourir le répertoire public/images
    Process-Directory -directory "public\images"
    
    Write-Host "Traitement terminé!" -ForegroundColor Green
}

# Exécuter la fonction principale
Main
