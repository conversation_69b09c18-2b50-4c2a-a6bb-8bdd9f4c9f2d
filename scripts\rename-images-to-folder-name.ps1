# Script pour renommer les fichiers d'images avec le nom du dossier parent
# Ce script parcourt tous les fichiers placeholder.png et les renomme avec le nom du dossier parent

# Fonction pour renommer un fichier placeholder avec le nom du dossier parent
function Rename-Placeholder-To-Folder-Name {
    param (
        [string]$placeholderPath
    )

    # Vérifier si le fichier existe
    if (-not (Test-Path -Path $placeholderPath)) {
        Write-Host "Le fichier $placeholderPath n'existe pas."
        return
    }

    # Obtenir le dossier parent
    $directory = Split-Path -Path $placeholderPath -Parent
    $folderName = Split-Path -Path $directory -Leaf

    # Créer le nouveau chemin de fichier
    $newPath = Join-Path -Path $directory -ChildPath "$folderName.png"

    # Vérifier si le nouveau fichier existe déjà
    if (Test-Path -Path $newPath) {
        Write-Host "Le fichier $newPath existe déjà."
        return
    }

    # Renommer le fichier
    Rename-Item -Path $placeholderPath -NewName "$folderName.png"
    Write-Host "Renommé: $placeholderPath -> $newPath"
}

# Trouver tous les fichiers placeholder.png dans le dossier public/images
$placeholderFiles = Get-ChildItem -Path "public\images" -Filter "placeholder.png" -Recurse

# Renommer chaque fichier placeholder
foreach ($placeholderFile in $placeholderFiles) {
    Rename-Placeholder-To-Folder-Name -placeholderPath $placeholderFile.FullName
}

Write-Host "Renommage terminé! $($placeholderFiles.Count) fichiers traités."

# Créer des bannières animées pour les promotions
function Create-Animated-Banner-Images {
    param (
        [string]$bannerType,
        [int]$count = 3
    )

    $directory = "public\images\banners\$bannerType"
    
    # Vérifier si le dossier existe
    if (-not (Test-Path -Path $directory)) {
        Write-Host "Le dossier $directory n'existe pas."
        return
    }

    # Créer les images de la bannière animée
    for ($i = 1; $i -le $count; $i++) {
        $filePath = Join-Path -Path $directory -ChildPath "$bannerType-$i.png"
        
        # Vérifier si le fichier existe déjà
        if (Test-Path -Path $filePath) {
            Write-Host "Le fichier $filePath existe déjà."
            continue
        }

        # Copier le fichier principal pour créer les images de la bannière animée
        $mainImagePath = Join-Path -Path $directory -ChildPath "$bannerType.png"
        if (Test-Path -Path $mainImagePath) {
            Copy-Item -Path $mainImagePath -Destination $filePath
            Write-Host "Créé: $filePath"
        } else {
            Write-Host "L'image principale $mainImagePath n'existe pas."
        }
    }
}

# Créer des bannières animées pour les promotions
Create-Animated-Banner-Images -bannerType "promotions"

Write-Host "Création des bannières animées terminée!"
