"use client"

import { ImageExamples } from '@/components/image-examples';

export default function ImageTestPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Test des images</h1>
      <p className="text-gray-600 mb-8">
        Cette page permet de tester le chargement des images à partir des dossiers créés.
        Vous pouvez voir comment les images sont liées au code et comment elles peuvent être mises à jour facilement.
      </p>
      
      <ImageExamples />
      
      <div className="mt-12 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Comment mettre à jour les images</h2>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Remplacez le fichier <code className="bg-gray-100 p-1 rounded">placeholder.svg</code> dans le dossier correspondant par votre nouvelle image.</li>
          <li>Assurez-vous que la nouvelle image a le même nom que l'ancienne.</li>
          <li>Si vous souhaitez utiliser un nom différent, mettez à jour le fichier <code className="bg-gray-100 p-1 rounded">lib/image-paths.ts</code>.</li>
          <li>Le site utilisera automatiquement la nouvelle image sans nécessiter de modifications de code supplémentaires.</li>
        </ol>
      </div>
    </div>
  );
}
