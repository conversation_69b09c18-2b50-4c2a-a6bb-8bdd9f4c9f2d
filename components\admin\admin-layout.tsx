"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  LayoutDashboard,
  ShoppingBag,
  Users,
  Settings,
  Smartphone,
  Palette,
  Image,
  ChevronDown,
  Menu,
  X,
  LogOut,
  User,
  Package,
  Tag,
  Images
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAuth } from "@/hooks/use-auth";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";

interface AdminLayoutProps {
  children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, signOut } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Gérer la déconnexion
  const handleSignOut = async () => {
    await signOut();
    router.push('/dashboard-hcp-secure/auth');
  };

  const routes = [
    {
      label: "Tableau de bord",
      icon: LayoutDashboard,
      href: "/dashboard-hcp-secure",
      active: pathname === "/dashboard-hcp-secure",
    },
    {
      label: "Commandes",
      icon: ShoppingBag,
      href: "/dashboard-hcp-secure/orders",
      active: pathname === "/dashboard-hcp-secure/orders" || pathname?.startsWith("/dashboard-hcp-secure/orders/"),
    },
    {
      label: "Produits",
      icon: Package,
      href: "/dashboard-hcp-secure/products",
      active: pathname === "/dashboard-hcp-secure/products" || pathname?.startsWith("/dashboard-hcp-secure/products/"),
    },
    {
      label: "Clients",
      icon: Users,
      href: "/dashboard-hcp-secure/customers",
      active: pathname === "/dashboard-hcp-secure/customers" || pathname?.startsWith("/dashboard-hcp-secure/customers/"),
    },
    {
      label: "Modèles de téléphones",
      icon: Smartphone,
      href: "/dashboard-hcp-secure/phone-models",
      active: pathname === "/dashboard-hcp-secure/phone-models" || pathname?.startsWith("/dashboard-hcp-secure/phone-models/"),
    },
    {
      label: "Coques transparentes",
      icon: Smartphone,
      href: "/dashboard-hcp-secure/phone-cases",
      active: pathname === "/dashboard-hcp-secure/phone-cases" || pathname?.startsWith("/dashboard-hcp-secure/phone-cases/"),
    },
    {
      label: "Designs prédéfinis",
      icon: Palette,
      href: "/dashboard-hcp-secure/designs",
      active: pathname === "/dashboard-hcp-secure/designs" || pathname?.startsWith("/dashboard-hcp-secure/designs/"),
    },
    {
      label: "Designs de galerie",
      icon: Images,
      href: "/dashboard-hcp-secure/gallery-designs",
      active: pathname === "/dashboard-hcp-secure/gallery-designs" || pathname?.startsWith("/dashboard-hcp-secure/gallery-designs/"),
    },
    {
      label: "Images",
      icon: Image,
      href: "/dashboard-hcp-secure/images",
      active: pathname === "/dashboard-hcp-secure/images" || pathname?.startsWith("/dashboard-hcp-secure/images/"),
    },
    {
      label: "Bannière Galerie",
      icon: Image,
      href: "/dashboard-hcp-secure/gallery-banner",
      active: pathname === "/dashboard-hcp-secure/gallery-banner" || pathname?.startsWith("/dashboard-hcp-secure/gallery-banner/"),
    },
    {
      label: "Gestion Page Promo",
      icon: Tag,
      href: "/dashboard-hcp-secure/promo-page",
      active: pathname === "/dashboard-hcp-secure/promo-page" || pathname?.startsWith("/dashboard-hcp-secure/promo-page/"),
    },
    {
      label: "Paramètres du site",
      icon: Settings,
      href: "/dashboard-hcp-secure/settings",
      active: pathname === "/dashboard-hcp-secure/settings",
    },
  ];

  return (
    <div className="h-full relative">
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden fixed top-4 left-4 z-50"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        {isMobileMenuOpen ? <X /> : <Menu />}
      </Button>

      {/* Sidebar for mobile */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-72 bg-background border-r transition-transform duration-300 md:hidden",
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="px-6 py-4 h-full flex flex-col">
          <div className="mb-8 flex items-center">
            <h1 className="text-2xl font-bold">HCP-DESIGN CI</h1>
          </div>
          <ScrollArea className="flex-1 -mx-6 px-6">
            <nav className="flex flex-col gap-1">
              {routes.map((route) => (
                <Link
                  key={route.href}
                  href={route.href}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                    route.active
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted"
                  )}
                >
                  <route.icon className="h-5 w-5" />
                  {route.label}
                </Link>
              ))}
            </nav>
          </ScrollArea>

          {/* User menu - Mobile */}
          <div className="mt-auto pt-4 border-t">
            <div className="flex items-center justify-between p-2">
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>{user?.first_name?.charAt(0) || 'A'}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium">{user?.first_name || 'Admin'}</p>
                  <p className="text-xs text-muted-foreground">{user?.email || ''}</p>
                </div>
              </div>
              <Button variant="ghost" size="icon" onClick={handleSignOut}>
                <LogOut className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar for desktop */}
      <div
        className={cn(
          "hidden md:flex h-full w-72 flex-col fixed inset-y-0 z-40 border-r bg-background transition-all duration-300",
          isSidebarOpen ? "left-0" : "-left-72"
        )}
      >
        <div className="px-6 py-4 h-full flex flex-col">
          <div className="mb-8 flex items-center justify-between">
            <h1 className="text-2xl font-bold">HCP-DESIGN CI</h1>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              className="md:hidden"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
          <ScrollArea className="flex-1 -mx-6 px-6">
            <nav className="flex flex-col gap-1">
              {routes.map((route) => (
                <Link
                  key={route.href}
                  href={route.href}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                    route.active
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted"
                  )}
                >
                  <route.icon className="h-5 w-5" />
                  {route.label}
                </Link>
              ))}
            </nav>
          </ScrollArea>

          {/* User menu - Desktop */}
          <div className="mt-auto pt-4 border-t">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="w-full flex items-center justify-between p-2">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>{user?.first_name?.charAt(0) || 'A'}</AvatarFallback>
                    </Avatar>
                    <div className="text-left">
                      <p className="text-sm font-medium">{user?.first_name || 'Admin'}</p>
                      <p className="text-xs text-muted-foreground truncate max-w-[120px]">{user?.email || ''}</p>
                    </div>
                  </div>
                  <ChevronDown className="h-4 w-4 opacity-50" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Mon compte</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut} className="text-red-600 cursor-pointer">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Déconnexion</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Toggle button for desktop */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        className="hidden md:flex fixed top-4 left-4 z-50"
      >
        <Menu className="h-5 w-5" />
      </Button>

      {/* Main content */}
      <main
        className={cn(
          "transition-all duration-300 min-h-screen bg-muted/20",
          isSidebarOpen
            ? "md:pl-72"
            : "md:pl-0"
        )}
      >
        <div className="container py-6 md:py-12 px-4 md:px-6">
          {children}
        </div>
      </main>
    </div>
  );
}

export default AdminLayout;
