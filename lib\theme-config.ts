// Configuration du thème partagée entre le site principal et le back office
export const themeConfig = {
  colors: {
    primary: {
      main: 'hsl(270, 70%, 40%)', // Violet principal
      light: 'hsl(270, 70%, 60%)',
      dark: 'hsl(270, 70%, 30%)',
      contrastText: 'hsl(0, 0%, 100%)'
    },
    secondary: {
      main: 'hsl(270, 70%, 95%)',
      light: 'hsl(270, 70%, 98%)',
      dark: 'hsl(270, 70%, 90%)',
      contrastText: 'hsl(270, 70%, 40%)'
    },
    success: {
      main: 'hsl(142, 76%, 36%)',
      light: 'hsl(142, 76%, 46%)',
      dark: 'hsl(142, 76%, 26%)',
      contrastText: 'hsl(0, 0%, 100%)'
    },
    error: {
      main: 'hsl(0, 84%, 60%)',
      light: 'hsl(0, 84%, 70%)',
      dark: 'hsl(0, 84%, 50%)',
      contrastText: 'hsl(0, 0%, 100%)'
    },
    warning: {
      main: 'hsl(45, 100%, 51%)',
      light: 'hsl(45, 100%, 61%)',
      dark: 'hsl(45, 100%, 41%)',
      contrastText: 'hsl(0, 0%, 0%)'
    },
    info: {
      main: 'hsl(207, 90%, 54%)',
      light: 'hsl(207, 90%, 64%)',
      dark: 'hsl(207, 90%, 44%)',
      contrastText: 'hsl(0, 0%, 100%)'
    },
    text: {
      primary: 'hsl(0, 0%, 13%)',
      secondary: 'hsl(0, 0%, 45%)',
      disabled: 'hsl(0, 0%, 62%)'
    },
    background: {
      default: 'hsl(0, 0%, 100%)',
      paper: 'hsl(0, 0%, 100%)',
      light: 'hsl(0, 0%, 98%)'
    },
    divider: 'hsl(0, 0%, 88%)'
  },
  typography: {
    fontFamily: 'Inter, sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 700,
      lineHeight: 1.3
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4
    },
    h4: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.4
    },
    h5: {
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: 1.5
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      lineHeight: 1.5
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.5
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 500,
      textTransform: 'none'
    }
  },
  shape: {
    borderRadius: '0.5rem'
  },
  spacing: (factor: number) => `${0.25 * factor}rem`
}

// Fonction pour obtenir une couleur du thème
export function getThemeColor(path: string): string {
  const parts = path.split('.');
  let result: any = themeConfig;
  
  for (const part of parts) {
    if (result && result[part]) {
      result = result[part];
    } else {
      return '';
    }
  }
  
  return typeof result === 'string' ? result : '';
}