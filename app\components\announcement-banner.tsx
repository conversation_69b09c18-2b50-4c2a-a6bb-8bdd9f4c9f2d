"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { X } from "lucide-react"

export default function AnnouncementBanner() {
  const [isVisible, setIsVisible] = useState(true)
  const [bannerText, setBannerText] = useState("Livraison gratuite pour toutes les commandes au-dessus de 50€")
  
  // Vérifier si la bannière a été fermée précédemment
  useEffect(() => {
    const bannerState = localStorage.getItem("announcementBannerVisible")
    if (bannerState === "false") {
      setIsVisible(false)
    }
    
    // Récupérer le texte de la bannière depuis le stockage local ou l'API
    const fetchBannerText = async () => {
      try {
        const response = await fetch('/api/site-content?key=announcement_banner')
        if (response.ok) {
          const data = await response.json()
          if (data.value) {
            setBannerText(data.value)
          }
        }
      } catch (error) {
        console.error("Erreur lors de la récupération du texte de la bannière:", error)
      }
    }
    
    fetchBannerText()
  }, [])
  
  const closeBanner = () => {
    setIsVisible(false)
    localStorage.setItem("announcementBannerVisible", "false")
  }
  
  if (!isVisible) return null
  
  return (
    <div className="bg-purple-600 text-white py-2 px-4 text-center text-sm relative">
      <p>
        {bannerText}
        {" "}
        <Link href="/shop" className="underline font-medium">
          Voir plus
        </Link>
      </p>
      <button 
        onClick={closeBanner}
        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-200"
        aria-label="Fermer la bannière"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}