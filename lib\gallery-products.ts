/**
 * Produits temporaires pour la galerie
 * Ces produits sont utilisés pour l'ajout au panier depuis la galerie
 * en attendant l'intégration complète avec Supabase
 */

// Fonction pour créer un produit de galerie
export function createGalleryProduct(
  designId: number,
  designTitle: string,
  modelName: string,
  modelId: string,
  brandName: string,
  price: number,
  imageUrl: string,
  isMagSafe: boolean = false,
  customName: string | null = null
) {
  // Générer un ID unique basé sur les paramètres
  const uniqueId = `gallery_${designId}_${modelId}_${isMagSafe ? 'magsafe' : 'standard'}_${Date.now()}`;
  
  return {
    id: uniqueId,
    name: `${designTitle} - ${modelName}${isMagSafe ? ' (MagSafe)' : ''}`,
    description: `Coque premium pour ${modelName} avec design "${designTitle}"${isMagSafe ? ' et compatibilité MagSafe' : ''}`,
    price: price,
    category: 'phone-cases',
    subcategory: 'gallery',
    image_url: imageUrl,
    is_new: false,
    is_bestseller: false,
    type: 'gallery',
    colors: ['default'],
    collections: ['gallery'],
    created_at: new Date().toISOString(),
    customization: {
      designId: designId,
      designTitle: designTitle,
      type: 'gallery',
      isMagSafe: isMagSafe,
      customName: customName,
      modelName: modelName,
      modelId: modelId,
      brandName: brandName
    }
  };
}

// Fonction pour obtenir le prix d'une coque de galerie
export function getGalleryProductPrice(isMagSafe: boolean = false) {
  const basePrice = 8000; // Prix de base pour une coque premium: 8000 Fr
  const magSafePrice = isMagSafe ? 2000 : 0; // Supplément MagSafe: 2000 Fr (pour un total de 10000 Fr)
  return basePrice + magSafePrice;
}
