// Script pour créer un fichier placeholder.png
const fs = require('fs');
const { createCanvas, loadImage } = require('canvas');

// Créer un canvas pour le placeholder
const width = 300;
const height = 400;
const canvas = createCanvas(width, height);
const ctx = canvas.getContext('2d');

// Fond gris clair
ctx.fillStyle = '#F3F4F6';
ctx.fillRect(0, 0, width, height);

// Rectangle central
ctx.fillStyle = '#E5E7EB';
ctx.roundRect(75, 150, 150, 100, 8);
ctx.fill();

// Icône stylisée
ctx.strokeStyle = '#9CA3AF';
ctx.lineWidth = 4;
ctx.beginPath();
ctx.moveTo(125, 200);
ctx.lineTo(150, 175);
ctx.lineTo(175, 200);
ctx.lineTo(200, 175);
ctx.stroke();

// Cercle
ctx.fillStyle = '#9CA3AF';
ctx.beginPath();
ctx.arc(110, 180, 10, 0, Math.PI * 2);
ctx.fill();

// Texte
ctx.fillStyle = '#6B7280';
ctx.font = '14px Arial';
ctx.textAlign = 'center';
ctx.fillText('Image à venir', 150, 230);

ctx.fillStyle = '#9CA3AF';
ctx.font = '12px Arial';
ctx.fillText('HCP-DESIGN CI', 150, 250);

// Enregistrer l'image
const buffer = canvas.toBuffer('image/png');
fs.writeFileSync('./public/images/placeholder.png', buffer);

console.log('Placeholder PNG créé avec succès');
