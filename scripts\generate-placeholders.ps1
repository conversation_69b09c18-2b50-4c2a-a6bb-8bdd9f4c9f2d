# Script pour générer des placeholders SVG pour tous les modèles de téléphones

# Modèles iPhone
$iphoneModels = @(
    "iphone-15-pro-max",
    "iphone-15-pro",
    "iphone-15-plus",
    "iphone-15",
    "iphone-14-pro-max",
    "iphone-14-pro",
    "iphone-14-plus",
    "iphone-14",
    "iphone-13-pro-max",
    "iphone-13-pro",
    "iphone-13",
    "iphone-12-pro-max",
    "iphone-12-pro",
    "iphone-12",
    "iphone-se-2022"
)

# Modèles Samsung
$samsungModels = @(
    "galaxy-s24-ultra",
    "galaxy-s24-plus",
    "galaxy-s24",
    "galaxy-s23-ultra",
    "galaxy-s23-plus",
    "galaxy-s23",
    "galaxy-s22-ultra",
    "galaxy-s22-plus",
    "galaxy-s22",
    "galaxy-a54",
    "galaxy-a53",
    "galaxy-a34"
)

# Modèles Google
$googleModels = @(
    "pixel-8-pro",
    "pixel-8",
    "pixel-7-pro",
    "pixel-7",
    "pixel-6-pro",
    "pixel-6"
)

# Types de coques
$caseTypes = @(
    "transparente",
    "silicone",
    "rigide",
    "antichoc",
    "portefeuille",
    "magnetique"
)

# Types de bannières
$bannerTypes = @(
    "accueil",
    "promotions",
    "categories",
    "produits",
    "evenements",
    "saisonniers"
)

# Types de témoignages
$testimonialTypes = @(
    "clients",
    "entreprises",
    "influenceurs"
)

# Types de designs
$designTypes = @(
    "abstraits",
    "animaux",
    "fleurs",
    "geometriques",
    "personnages",
    "sports",
    "marques",
    "personnalises"
)

# Types de produits
$productTypes = @(
    "coques",
    "accessoires",
    "personnalisation",
    "nouveautes",
    "promotions"
)

# Fonction pour créer un PNG placeholder
function Create-PNG-Placeholder {
    param (
        [string]$filePath,
        [string]$title,
        [string]$subtitle
    )

    # Créer le dossier parent s'il n'existe pas
    $directory = Split-Path -Path $filePath -Parent
    if (-not (Test-Path -Path $directory)) {
        New-Item -Path $directory -ItemType Directory -Force | Out-Null
    }

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="300" height="600" viewBox="0 0 300 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="300" height="600" rx="40" fill="#F3F4F6"/>
  <rect x="30" y="30" width="240" height="540" rx="20" fill="#E5E7EB"/>
  <text x="150" y="300" font-family="Arial" font-size="24" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="150" y="330" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Utiliser une méthode pour convertir SVG en PNG
    # Note: Cette partie nécessite un outil de conversion comme Inkscape, ImageMagick, ou une bibliothèque .NET
    # Pour cet exemple, nous allons simplement copier le fichier SVG et changer l'extension
    # Dans un environnement de production, vous devriez utiliser un vrai convertisseur

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force

    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force

    Write-Host "Créé: $filePath"
}

# Créer des placeholders pour les modèles iPhone
foreach ($model in $iphoneModels) {
    $modelName = $model -replace "iphone-", "iPhone " -replace "-", " " -replace "pro max", "Pro Max" -replace "pro", "Pro" -replace "plus", "Plus"
    Create-PNG-Placeholder -filePath "public\images\phone-cases\iphone\$model\placeholder.png" -title $modelName -subtitle "Coque transparente"
}

# Créer des placeholders pour les modèles Samsung
foreach ($model in $samsungModels) {
    $modelName = $model -replace "galaxy-", "Galaxy " -replace "-", " " -replace "ultra", "Ultra" -replace "plus", "Plus"
    Create-PNG-Placeholder -filePath "public\images\phone-cases\samsung\$model\placeholder.png" -title $modelName -subtitle "Coque transparente"
}

# Créer des placeholders pour les modèles Google
foreach ($model in $googleModels) {
    $modelName = $model -replace "pixel-", "Pixel " -replace "-", " " -replace "pro", "Pro"
    Create-PNG-Placeholder -filePath "public\images\phone-cases\google\$model\placeholder.png" -title $modelName -subtitle "Coque transparente"
}

# Créer des placeholders pour les types de coques
foreach ($type in $caseTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    Create-PNG-Placeholder -filePath "public\images\phone-cases\types\$type\placeholder.png" -title "Coque $typeName" -subtitle "Pour tous les modèles"
}

# Créer des placeholders pour les types de bannières
foreach ($type in $bannerTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    Create-PNG-Placeholder -filePath "public\images\banners\$type\placeholder.png" -title "Bannière $typeName" -subtitle "Format standard"
}

# Créer des placeholders pour les types de témoignages
foreach ($type in $testimonialTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    Create-PNG-Placeholder -filePath "public\images\testimonials\$type\placeholder.png" -title "Témoignage $typeName" -subtitle "Photo de profil"
}

# Créer des placeholders pour les types de designs
foreach ($type in $designTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    Create-PNG-Placeholder -filePath "public\images\designs\$type\placeholder.png" -title "Design $typeName" -subtitle "Motif personnalisable"
}

# Créer des placeholders pour les types de produits
foreach ($type in $productTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    Create-PNG-Placeholder -filePath "public\images\products\$type\placeholder.png" -title "Produit $typeName" -subtitle "Catégorie principale"
}

Write-Host "Génération des placeholders terminée !"
