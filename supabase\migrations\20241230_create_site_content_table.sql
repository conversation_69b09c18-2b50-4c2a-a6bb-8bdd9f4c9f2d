-- Migration pour créer la table site_content
-- Date: 2024-12-30

-- <PERSON><PERSON><PERSON> la table site_content pour gérer le contenu éditable du site
CREATE TABLE IF NOT EXISTS public.site_content (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  value TEXT,
  description TEXT,
  category TEXT DEFAULT 'general',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON>er des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_site_content_key ON public.site_content(key);
CREATE INDEX IF NOT EXISTS idx_site_content_category ON public.site_content(category);

-- Activer RLS (Row Level Security)
ALTER TABLE public.site_content ENABLE ROW LEVEL SECURITY;

-- Créer une politique pour que seuls les administrateurs puissent gérer le contenu
CREATE POLICY "Admins can manage site content" 
  ON public.site_content 
  USING (auth.role() = 'service_role');

-- Créer une politique pour que tous les utilisateurs puissent lire le contenu
CREATE POLICY "All users can read site content" 
  ON public.site_content 
  FOR SELECT
  USING (true);

-- Créer une fonction pour mettre à jour la date de mise à jour
CREATE OR REPLACE FUNCTION update_site_content_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger pour mettre à jour automatiquement updated_at
CREATE TRIGGER trigger_update_site_content_updated_at
  BEFORE UPDATE ON public.site_content
  FOR EACH ROW
  EXECUTE FUNCTION update_site_content_updated_at();

-- Insérer le contenu par défaut
INSERT INTO public.site_content (key, value, description, category)
VALUES 
  ('banner_title', 'HCP Design CI', 'Titre principal de la bannière', 'banner'),
  ('banner_subtitle', 'Coques de téléphone personnalisées', 'Sous-titre de la bannière', 'banner'),
  ('banner_button', 'Découvrir nos produits', 'Texte du bouton de la bannière', 'banner'),
  ('homepage_intro', 'Bienvenue chez HCP Design CI, votre spécialiste en coques de téléphone personnalisées.', 'Texte d\'introduction de la page d\'accueil', 'homepage'),
  ('footer_text', '© 2024 HCP Design CI. Tous droits réservés.', 'Texte du pied de page', 'footer'),
  ('gospel_section_title', 'Produits Gospel', 'Titre de la section Gospel', 'gospel'),
  ('gospel_section_description', 'Découvrez notre collection exclusive de produits Gospel pour exprimer votre foi avec style.', 'Description de la section Gospel', 'gospel'),
  ('general_designs_title', 'Designs Généraux', 'Titre de la section designs généraux', 'designs'),
  ('general_designs_description', 'Explorez notre vaste gamme de designs créatifs et tendances pour personnaliser vos coques.', 'Description de la section designs généraux', 'designs')
ON CONFLICT (key) DO UPDATE
SET 
  value = EXCLUDED.value,
  description = EXCLUDED.description,
  category = EXCLUDED.category,
  updated_at = NOW();