"use client"

import React, { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import { Gift, Package, Calendar, MessageSquare, Send, Check, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

// Types pour les packs
interface Pack {
  id: string
  title: string
  price: number
  description: string
  items: string[]
  image: string
  category: string
}

// Données des packs
const eventPacks: Pack[] = [
  {
    id: "anniversary",
    title: "Pack Anniversaire Classique",
    price: 15000,
    description: "Parfait pour célébrer un anniversaire avec style et personnalité.",
    items: [
      "1 mug personnalisé (photo + prénom)",
      "1 t-shirt ou totebag au choix",
      "1 badge ou pin's à thème"
    ],
    image: "/images/gifts/anniversary-pack/anniversary-pack.png",
    category: "anniversaire"
  },
  {
    id: "wedding",
    title: "Pack Mariage Premium",
    price: 30000,
    description: "Célébrez l'union parfaite avec nos cadeaux élégants et personnalisés.",
    items: [
      "2 mugs Mr & Mrs ou prénoms des mariés",
      "1 porte-clé duo personnalisé",
      "1 cadre souvenir design",
      "Boîte cadeau avec ruban chic"
    ],
    image: "/images/gifts/wedding-pack/wedding-pack.png",
    category: "mariage"
  },
  {
    id: "dinner",
    title: "Pack Dîner VIP",
    price: 20000,
    description: "Impressionnez vos invités avec des cadeaux élégants et personnalisés.",
    items: [
      "4 sous-verres personnalisés",
      "Carte de remerciement avec prénom",
      "Mini cadre souvenir ou magnet photo"
    ],
    image: "/images/gifts/dinner-pack/dinner-pack.png",
    category: "diner"
  },
  {
    id: "corporate",
    title: "Pack Entreprise",
    price: 25000,
    description: "Des cadeaux professionnels qui reflètent l'identité de votre entreprise.",
    items: [
      "Stylo gravé avec logo",
      "Bloc-notes A5 design",
      "Tasse ou gourde avec nom de l'invité",
      "Emballage cadeau aux couleurs de votre marque"
    ],
    image: "/images/gifts/corporate-pack/corporate-pack.png",
    category: "entreprise"
  },
  {
    id: "baby",
    title: "Pack Baby Shower",
    price: 18000,
    description: "Célébrez l'arrivée d'un nouveau-né avec des cadeaux adorables.",
    items: [
      "1 body personnalisé avec prénom",
      "1 petit doudou brodé",
      "1 carte souvenir avec empreinte",
      "Emballage cadeau thématique"
    ],
    image: "/images/gifts/baby-pack/baby-pack.jpg",
    category: "baby"
  },
  {
    id: "graduation",
    title: "Pack Remise de Diplôme",
    price: 22000,
    description: "Marquez cette étape importante avec des souvenirs personnalisés.",
    items: [
      "1 mug avec année de diplôme",
      "1 porte-clé gravé",
      "1 carnet de notes personnalisé",
      "Boîte cadeau élégante"
    ],
    image: "/images/gifts/graduation-pack/graduation-pack.jpg",
    category: "diplome"
  }
];

// Catégories d'événements
const eventCategories = [
  { id: "all", name: "Tous les packs" },
  { id: "anniversaire", name: "Anniversaires" },
  { id: "mariage", name: "Mariages & Fiançailles" },
  { id: "diner", name: "Dîners VIP" },
  { id: "entreprise", name: "Événements d'entreprise" },
  { id: "baby", name: "Baby Showers" },
  { id: "diplome", name: "Remises de diplôme" }
];

export default function GiftsPage() {
  const [activeCategory, setActiveCategory] = useState("all")
  const [selectedPack, setSelectedPack] = useState<Pack | null>(null)
  const [isCustomQuoteOpen, setIsCustomQuoteOpen] = useState(false)
  const [clickedPackId, setClickedPackId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    eventType: "",
    eventDate: "",
    guestCount: "",
    message: ""
  })
  const { toast } = useToast()
  const { addToCart } = useSimpleCart()

  // Filtrer les packs par catégorie
  const filteredPacks = activeCategory === "all"
    ? eventPacks
    : eventPacks.filter(pack => pack.category === activeCategory)

  // Gérer la soumission du formulaire de devis
  const handleQuoteSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Ici, vous pourriez envoyer les données à votre backend
    toast({
      title: "Demande envoyée !",
      description: "Nous vous contacterons bientôt pour discuter de votre projet.",
      duration: 5000,
    })
    setIsCustomQuoteOpen(false)
  }

  // Gérer la commande d'un pack
  const handleOrderPack = () => {
    if (!selectedPack) return

    // Convertir le pack en produit pour le panier
    const packProduct = {
      id: `pack-${selectedPack.id}`,
      name: selectedPack.title,
      price: selectedPack.price,
      image_url: selectedPack.image,
      category: "Packs Cadeaux",
      subcategory: selectedPack.category,
      customized: false
    }

    // Ajouter au panier
    addToCart(packProduct, 1)

    toast({
      title: "Pack ajouté au panier !",
      description: `Le pack ${selectedPack.title} a été ajouté à votre panier.`,
      action: (
        <Button variant="outline" size="sm" onClick={() => window.location.href = "/cart"}>
          Voir le panier
        </Button>
      ),
    })

    setSelectedPack(null)
  }

  return (
    <main className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-2">🎁 Cadeaux & Gadgets Événementiels</h1>
        <p className="text-lg opacity-90">
          Rendez vos événements vraiment inoubliables avec HCP-DESIGN !
        </p>
      </div>

      {/* Introduction */}
      <div className="mb-12 text-center max-w-3xl mx-auto">
        <p className="text-lg mb-6">
          Chez HCP-DESIGN, nous transformons vos idées en objets personnalisés pour marquer chaque moment important.
          Que ce soit pour un anniversaire, un dîner privé, un mariage, une célébration d'entreprise ou toute autre occasion spéciale,
          nos packs sur-mesure sont pensés pour émerveiller et faire plaisir.
        </p>
        <Button
          onClick={() => setIsCustomQuoteOpen(true)}
          className="bg-purple-600 hover:bg-purple-700"
          size="lg"
        >
          Demander un devis personnalisé <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>

      {/* Event Ideas */}
      <div className="mb-16">
        <h2 className="text-2xl font-bold mb-6 text-center">✨ Des idées originales pour chaque événement</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card className="text-center hover:shadow-md transition-all">
            <CardContent className="p-6">
              <div className="mb-3 text-3xl">🎂</div>
              <h3 className="font-medium">Anniversaires</h3>
              <p className="text-sm text-gray-500">enfants & adultes</p>
            </CardContent>
          </Card>
          <Card className="text-center hover:shadow-md transition-all">
            <CardContent className="p-6">
              <div className="mb-3 text-3xl">🥂</div>
              <h3 className="font-medium">Dîners VIP</h3>
              <p className="text-sm text-gray-500">& fêtes privées</p>
            </CardContent>
          </Card>
          <Card className="text-center hover:shadow-md transition-all">
            <CardContent className="p-6">
              <div className="mb-3 text-3xl">💼</div>
              <h3 className="font-medium">Séminaires</h3>
              <p className="text-sm text-gray-500">& événements d'entreprise</p>
            </CardContent>
          </Card>
          <Card className="text-center hover:shadow-md transition-all">
            <CardContent className="p-6">
              <div className="mb-3 text-3xl">💍</div>
              <h3 className="font-medium">Mariages</h3>
              <p className="text-sm text-gray-500">& fiançailles</p>
            </CardContent>
          </Card>
          <Card className="text-center hover:shadow-md transition-all">
            <CardContent className="p-6">
              <div className="mb-3 text-3xl">🍼</div>
              <h3 className="font-medium">Baby showers</h3>
              <p className="text-sm text-gray-500">& baptêmes</p>
            </CardContent>
          </Card>
          <Card className="text-center hover:shadow-md transition-all">
            <CardContent className="p-6">
              <div className="mb-3 text-3xl">🎓</div>
              <h3 className="font-medium">Remises de diplôme</h3>
              <p className="text-sm text-gray-500">& célébrations scolaires</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Packs */}
      <div className="mb-16">
        <h2 className="text-2xl font-bold mb-6 text-center">📦 Nos packs personnalisés</h2>

        {/* Filtres */}
        <Tabs value={activeCategory} onValueChange={setActiveCategory} className="mb-8">
          <TabsList className="flex flex-wrap justify-center mb-6 bg-transparent h-auto space-x-2 space-y-2">
            {eventCategories.map((category) => (
              <TabsTrigger
                key={category.id}
                value={category.id}
                className="data-[state=active]:bg-purple-600 data-[state=active]:text-white rounded-full px-4 py-2 border"
              >
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        {/* Liste des packs */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPacks.map((pack) => (
            <Card 
              key={pack.id} 
              className="group flex flex-col h-full overflow-hidden border rounded-lg shadow-sm cursor-pointer" // Added cursor-pointer
              onClick={() => {
                setClickedPackId(prev => (prev === pack.id ? null : pack.id));
              }}
            >
              <div className="relative w-full aspect-[4/3] overflow-hidden">
                {/* Image - Hidden on Hover or Click */}
                <div className={cn(
                  "transition-opacity duration-300",
                  (clickedPackId === pack.id) ? "opacity-0" : "group-hover:opacity-0"
                )}>
                  <Image
                    src={pack.image}
                    alt={pack.title}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/images/gifts/placeholder-gift.jpg";
                    }}
                  />
                </div>
                {/* Description and Items - Visible on Hover or Click, takes place of image */}
                <div className={cn(
                  "absolute inset-0 bg-purple-700 p-4 opacity-0 transition-opacity duration-300 flex flex-col justify-center items-center text-center overflow-auto",
                  (clickedPackId === pack.id) ? "opacity-100" : "group-hover:opacity-100"
                )}>
                    <h4 className="font-semibold text-white mb-2 text-md">{pack.title}</h4>
                    <p className="text-purple-200 mb-3 text-sm line-clamp-4">{pack.description}</p>
                    <ul className="space-y-1 text-xs text-left w-full max-w-xs">
                      {pack.items.map((item, index) => (
                        <li key={index} className="flex items-start text-purple-100">
                          <Check className="h-3 w-3 text-green-300 mr-1.5 mt-0.5 flex-shrink-0" />
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                </div>
              </div>
              <CardContent className="p-4 flex-grow flex flex-col bg-white">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-bold text-md text-gray-800 truncate pr-2">{pack.title}</h3>
                  <div className="font-semibold text-purple-600 text-sm whitespace-nowrap">{pack.price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ')} Fr</div>
                </div>
                <div className="mt-auto pt-2">
                    <Button
                    className="w-full bg-purple-600 hover:bg-purple-800 text-white py-2 text-sm"
                    onClick={(e) => { // Stop propagation to prevent card click when button is clicked
                        e.stopPropagation();
                        setSelectedPack(pack);
                    }}
                    >
                    Commander ce pack
                    </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Personnalisation */}
      <div className="mb-16 bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-bold mb-4 text-center">🖌️ Personnalisez selon vos envies</h2>
        <div className="max-w-3xl mx-auto">
          <ul className="mb-6 space-y-2">
            <li className="flex items-center">
              <Check className="h-5 w-5 text-purple-600 mr-2" />
              <span>Couleurs, prénoms, photos, logos, citations, messages...</span>
            </li>
            <li className="flex items-center">
              <Check className="h-5 w-5 text-purple-600 mr-2" />
              <span>Possibilité d'ajouter un packaging haut de gamme</span>
            </li>
            <li className="flex items-center">
              <Check className="h-5 w-5 text-purple-600 mr-2" />
              <span>Livraison rapide et soignée</span>
            </li>
            <li className="flex items-center">
              <Check className="h-5 w-5 text-purple-600 mr-2" />
              <span>Service client dédié pour vous accompagner</span>
            </li>
          </ul>
          <div className="text-center">
            <Button
              onClick={() => setIsCustomQuoteOpen(true)}
              variant="outline"
              size="lg"
              className="border-purple-600 text-purple-600 hover:bg-purple-50"
            >
              Demander un devis sur mesure <MessageSquare className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* CTA */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-4">📩 Besoin d'un pack sur mesure ?</h2>
        <p className="mb-6">
          Contactez-nous pour discuter de vos besoins spécifiques et obtenir un devis personnalisé.
        </p>
        <Button
          onClick={() => setIsCustomQuoteOpen(true)}
          className="bg-purple-600 hover:bg-purple-700"
          size="lg"
        >
          Demander un devis personnalisé <Send className="ml-2 h-4 w-4" />
        </Button>
      </div>

      {/* Dialog pour commander un pack */}
      <Dialog open={selectedPack !== null} onOpenChange={(open) => !open && setSelectedPack(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Commander {selectedPack?.title}</DialogTitle>
            <DialogDescription>
              Personnalisez votre commande selon vos besoins.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="event-date">Date de l'événement</Label>
              <Input id="event-date" type="date" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="message">Message personnalisé</Label>
              <Textarea id="message" placeholder="Indiquez les détails de personnalisation (noms, couleurs, etc.)" />
            </div>
            <div className="flex justify-between pt-4">
              <Button variant="outline" onClick={() => setSelectedPack(null)}>
                Annuler
              </Button>
              <Button className="bg-purple-600 hover:bg-purple-700" onClick={handleOrderPack}>
                Ajouter au panier
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog pour demander un devis personnalisé */}
      <Dialog open={isCustomQuoteOpen} onOpenChange={setIsCustomQuoteOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Demande de devis personnalisé</DialogTitle>
            <DialogDescription>
              Parlez-nous de votre événement et de vos besoins spécifiques.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleQuoteSubmit} className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom complet</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Téléphone / WhatsApp</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="event-type">Type d'événement</Label>
                <Select
                  value={formData.eventType}
                  onValueChange={(value) => setFormData({...formData, eventType: value})}
                >
                  <SelectTrigger id="event-type">
                    <SelectValue placeholder="Sélectionner" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="anniversary">Anniversaire</SelectItem>
                    <SelectItem value="wedding">Mariage</SelectItem>
                    <SelectItem value="corporate">Événement d'entreprise</SelectItem>
                    <SelectItem value="dinner">Dîner VIP</SelectItem>
                    <SelectItem value="baby">Baby Shower</SelectItem>
                    <SelectItem value="graduation">Remise de diplôme</SelectItem>
                    <SelectItem value="other">Autre</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="event-date">Date de l'événement</Label>
                <Input
                  id="event-date"
                  type="date"
                  value={formData.eventDate}
                  onChange={(e) => setFormData({...formData, eventDate: e.target.value})}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="guest-count">Nombre d'invités/cadeaux</Label>
              <Input
                id="guest-count"
                type="number"
                value={formData.guestCount}
                onChange={(e) => setFormData({...formData, guestCount: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="message">Détails de votre demande</Label>
              <Textarea
                id="message"
                placeholder="Décrivez vos besoins, idées, budget..."
                className="min-h-[100px]"
                value={formData.message}
                onChange={(e) => setFormData({...formData, message: e.target.value})}
                required
              />
            </div>
            <div className="flex justify-end pt-4">
              <Button type="submit" className="bg-purple-600 hover:bg-purple-700">
                Envoyer ma demande <Send className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </main>
  )
}
