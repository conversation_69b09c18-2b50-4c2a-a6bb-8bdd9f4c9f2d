import type { ReactNode } from "react";
import { AdminSecurityGuard } from "@/components/admin/admin-security-guard";
import { AuthProvider } from "@/hooks/use-auth";
import { AdminSessionProvider } from "@/components/admin/session-provider";
import { Toaster } from "@/components/ui/sonner";

export default function AdminRootLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <AuthProvider>
      <AdminSecurityGuard>
        <AdminSessionProvider>
          {children}
          <Toaster />
        </AdminSessionProvider>
      </AdminSecurityGuard>
    </AuthProvider>
  );
}
