// Script pour tester l'envoi de notification Telegram
const fetch = require('node-fetch');

// Remplacez par votre token de bot et chat_id
const token = '7164687738:AAE8skE__H4GsGdZqElYJa0EByg7o3-0TBA';
const chatId = 'VOTRE_CHAT_ID'; // À remplacer par votre chat_id réel

// Message de test
const message = `
*🛒 Test de Notification Telegram*

Ceci est un message de test pour vérifier que les notifications Telegram fonctionnent correctement.

*⏱️ Date et heure*
${new Date().toLocaleString('fr-FR')}
`;

// Fonction pour envoyer un message Telegram
async function sendTelegramMessage() {
  try {
    const response = await fetch(
      `https://api.telegram.org/bot${token}/sendMessage`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: chatId,
          text: message,
          parse_mode: 'Markdown'
        }),
      }
    );
    
    const data = await response.json();
    
    if (data.ok) {
      console.log('✅ Message envoyé avec succès!');
      console.log('Réponse:', JSON.stringify(data, null, 2));
    } else {
      console.error('❌ Erreur lors de l\'envoi du message:', data.description);
    }
  } catch (error) {
    console.error('❌ Exception lors de l\'envoi du message:', error);
  }
}

// Exécuter la fonction
console.log('Envoi d\'un message de test à Telegram...');
sendTelegramMessage();
