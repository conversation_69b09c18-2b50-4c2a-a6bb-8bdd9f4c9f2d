"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Mail, Phone, MapPin, Facebook, Instagram, FileText, MessageSquare } from "lucide-react"
import { toast } from "sonner"

// Liste des articles pour le devis organisée par catégories
const quoteCategories = [
  {
    id: 'papeterie',
    name: '📝 Papeterie personnalisée',
    items: [
      { id: 'carnet', name: 'Carnet', needsColor: false, isCustom: false },
      { id: 'bloc-notes', name: 'Bloc-notes', needsColor: false, isCustom: false }
    ]
  },
  {
    id: 'accessoires-bureau',
    name: '☕ Accessoires de bureau et usage quotidien',
    items: [
      { id: 'tasse-blanche', name: 'Tasse blanche', needsColor: false, isCustom: false },
      { id: 'tasse-magique', name: 'Tasse magique', needsColor: false, isCustom: false },
      { id: 'stylo-standard', name: 'Stylo standard', needsColor: false, isCustom: false },
      { id: 'stylo-vip', name: 'Stylo VIP', needsColor: false, isCustom: false }
    ]
  },
  {
    id: 'communication',
    name: '📢 Supports de communication visuelle',
    items: [
      { id: 'kakemonos-petite', name: 'Kakémono petite base', needsColor: false, isCustom: false },
      { id: 'kakemonos-grande', name: 'Kakémono grande base', needsColor: false, isCustom: false }
    ]
  },
  {
    id: 'textiles',
    name: '👕 Textiles personnalisés',
    items: [
      { id: 'tshirt-vip', name: 'T-shirt VIP (couleur à préciser)', needsColor: true, isCustom: false },
      { id: 'tshirt-faible', name: 'T-shirt faible (couleur à préciser)', needsColor: true, isCustom: false }
    ]
  },
  {
    id: 'accessoires',
    name: '🔑 Accessoires',
    items: [
      { id: 'porte-cle-plastique', name: 'Porte-clé plastique', needsColor: false, isCustom: false },
      { id: 'porte-cle-metallique', name: 'Porte-clé métallique', needsColor: false, isCustom: false }
    ]
  },
  {
    id: 'hydratation',
    name: '🥤 Hydratation',
    items: [
      { id: 'gourde-600', name: 'Gourde 600 ml', needsColor: false, isCustom: false },
      { id: 'gourde-700', name: 'Gourde 700 ml', needsColor: false, isCustom: false }
    ]
  },
  {
    id: 'decoration',
    name: '🛋️ Décoration',
    items: [
      { id: 'coussin-vip-30', name: 'Coussin VIP (30x30 cm)', needsColor: false, isCustom: false },
      { id: 'coussin-vip-40', name: 'Coussin VIP (40x40 cm)', needsColor: false, isCustom: false },
      { id: 'coussin-royal-30', name: 'Coussin Royal (30x30 cm)', needsColor: false, isCustom: false },
      { id: 'coussin-royal-40', name: 'Coussin Royal (40x40 cm)', needsColor: false, isCustom: false }
    ]
  }
]

// Liste plate pour compatibilité avec le code existant
const quoteItems = quoteCategories.flatMap(category => category.items).concat([
  { id: 'autre-produit', name: 'Autre produit (préciser le nom)', needsColor: false, isCustom: true }
])

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  })
  
  const [quoteData, setQuoteData] = useState({
    name: "",
    email: "",
    phone: "",
    deadline: "",
    selectedItems: {} as Record<string, { selected: boolean; quantity: number; color?: string; customName?: string }>
  })
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isQuoteSubmitting, setIsQuoteSubmitting] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleQuoteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setQuoteData((prev) => ({ ...prev, [name]: value }))
  }

  const handleItemToggle = (itemId: string) => {
    setQuoteData(prev => ({
      ...prev,
      selectedItems: {
        ...prev.selectedItems,
        [itemId]: {
          ...prev.selectedItems[itemId],
          selected: !prev.selectedItems[itemId]?.selected,
          quantity: prev.selectedItems[itemId]?.quantity || 1
        }
      }
    }))
  }

  const handleQuantityChange = (itemId: string, quantity: number) => {
    setQuoteData(prev => ({
      ...prev,
      selectedItems: {
        ...prev.selectedItems,
        [itemId]: {
          ...prev.selectedItems[itemId],
          quantity: Math.max(1, quantity)
        }
      }
    }))
  }

  const handleColorChange = (itemId: string, color: string) => {
    setQuoteData(prev => ({
      ...prev,
      selectedItems: {
        ...prev.selectedItems,
        [itemId]: {
          ...prev.selectedItems[itemId],
          color
        }
      }
    }))
  }

  const handleCustomNameChange = (itemId: string, customName: string) => {
    setQuoteData(prev => ({
      ...prev,
      selectedItems: {
        ...prev.selectedItems,
        [itemId]: {
          ...prev.selectedItems[itemId],
          customName
        }
      }
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Simuler l'envoi du formulaire
      await new Promise((resolve) => setTimeout(resolve, 1000))
      
      // Réinitialiser le formulaire
      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: "",
      })
      
      toast.success("Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.")
    } catch (error) {
      toast.error("Une erreur s'est produite lors de l'envoi du message. Veuillez réessayer.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleQuoteSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsQuoteSubmitting(true)

    try {
      // Vérifier qu'au moins un article est sélectionné
      const selectedItems = Object.entries(quoteData.selectedItems).filter(([_, item]) => item.selected)
      if (selectedItems.length === 0) {
        toast.error("Veuillez sélectionner au moins un article pour votre devis.")
        return
      }

      // Simuler l'envoi du devis
      await new Promise((resolve) => setTimeout(resolve, 1000))
      
      // Réinitialiser le formulaire de devis
      setQuoteData({
        name: "",
        email: "",
        phone: "",
        deadline: "",
        selectedItems: {}
      })
      
      toast.success("Votre demande de devis a été envoyée avec succès. Nous vous contacterons dans les plus brefs délais.")
    } catch (error) {
      toast.error("Une erreur s'est produite lors de l'envoi de votre demande de devis. Veuillez réessayer.")
    } finally {
      setIsQuoteSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold text-center mb-8">Contactez-nous</h1>
      
      <Tabs defaultValue="quote" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8 bg-gray-100 p-1 rounded-lg">
          <TabsTrigger 
            value="quote" 
            className="flex items-center gap-2 data-[state=active]:bg-green-500 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 hover:bg-green-100 text-green-700 font-medium"
          >
            <FileText className="h-4 w-4" />
            Demander un devis
          </TabsTrigger>
          <TabsTrigger 
            value="contact" 
            className="flex items-center gap-2 data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 hover:bg-blue-100 text-blue-700 font-medium"
          >
            <MessageSquare className="h-4 w-4" />
            Contact général
          </TabsTrigger>
        </TabsList>

        <TabsContent value="contact">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Formulaire de contact */}
            <Card>
              <CardHeader>
                <CardTitle>Envoyez-nous un message</CardTitle>
                <CardDescription>
                  Remplissez le formulaire ci-dessous et nous vous répondrons dans les plus brefs délais.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Nom complet</Label>
                      <Input
                        id="name"
                        name="name"
                        placeholder="Votre nom"
                        value={formData.name}
                        onChange={handleChange}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Téléphone</Label>
                      <Input
                        id="phone"
                        name="phone"
                        placeholder="+225 XX XX XX XX XX"
                        value={formData.phone}
                        onChange={handleChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="subject">Sujet</Label>
                      <Input
                        id="subject"
                        name="subject"
                        placeholder="Sujet de votre message"
                        value={formData.subject}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      name="message"
                      placeholder="Votre message..."
                      rows={5}
                      value={formData.message}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  
                  <Button type="submit" className="w-full" disabled={isSubmitting}>
                    {isSubmitting ? "Envoi en cours..." : "Envoyer le message"}
                  </Button>
                </form>
              </CardContent>
            </Card>
            
            {/* Informations de contact */}
            <div className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Nos coordonnées</CardTitle>
                  <CardDescription>
                    N'hésitez pas à nous contacter directement par téléphone ou email.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start">
                    <Phone className="h-5 w-5 text-purple-600 mr-3 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Téléphone</h3>
                      <p className="text-gray-600">+225 07 09 49 58 49</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Mail className="h-5 w-5 text-purple-600 mr-3 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Email</h3>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <MapPin className="h-5 w-5 text-purple-600 mr-3 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Adresse</h3>
                      <p className="text-gray-600">Abidjan, Côte d'Ivoire</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Suivez-nous</CardTitle>
                  <CardDescription>
                    Retrouvez-nous sur les réseaux sociaux pour découvrir nos dernières créations.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex space-x-4">
                    <a 
                      href="https://www.facebook.com/HabillagesetCoquesPersonnalises/" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center text-gray-600 hover:text-purple-600"
                    >
                      <Facebook className="h-5 w-5 mr-2" />
                      <span>Facebook</span>
                    </a>
                    <a 
                      href="https://www.instagram.com/designshcp/" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center text-gray-600 hover:text-purple-600"
                    >
                      <Instagram className="h-5 w-5 mr-2" />
                      <span>Instagram</span>
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="quote">
          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Demande de devis personnalisé
              </CardTitle>
              <CardDescription>
                Sélectionnez les articles qui vous intéressent et précisez les quantités souhaitées.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleQuoteSubmit} className="space-y-6">
                {/* Informations personnelles */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quote-name">Nom complet *</Label>
                    <Input
                      id="quote-name"
                      name="name"
                      placeholder="Votre nom"
                      value={quoteData.name}
                      onChange={handleQuoteChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quote-email">Email *</Label>
                    <Input
                      id="quote-email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={quoteData.email}
                      onChange={handleQuoteChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quote-phone">Téléphone *</Label>
                    <Input
                      id="quote-phone"
                      name="phone"
                      placeholder="+225 XX XX XX XX XX"
                      value={quoteData.phone}
                      onChange={handleQuoteChange}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quote-deadline">Date limite souhaitée *</Label>
                  <Input
                    id="quote-deadline"
                    name="deadline"
                    type="date"
                    value={quoteData.deadline}
                    onChange={handleQuoteChange}
                    required
                  />
                </div>

                {/* Liste des articles par catégories */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Sélectionnez vos articles</h3>
                  <div className="space-y-6">
                    {quoteCategories.map((category) => (
                      <div key={category.id} className="space-y-3">
                        <h4 className="text-md font-medium text-gray-700 border-b pb-2">
                          {category.name}
                        </h4>
                        <div className="grid gap-3 ml-4">
                          {category.items.map((item) => {
                            const itemData = quoteData.selectedItems[item.id] || { selected: false, quantity: 1 }
                            return (
                              <div key={item.id} className="border rounded-lg p-4 space-y-3">
                                <div className="flex items-center space-x-3">
                                  <Checkbox
                                    id={item.id}
                                    checked={itemData.selected}
                                    onCheckedChange={() => handleItemToggle(item.id)}
                                  />
                                  <Label htmlFor={item.id} className="flex-1 font-medium">
                                    {item.name}
                                  </Label>
                                  {itemData.selected && (
                                    <div className="flex items-center space-x-2">
                                      <Label htmlFor={`quantity-${item.id}`} className="text-sm">
                                        Quantité:
                                      </Label>
                                      <Input
                                        id={`quantity-${item.id}`}
                                        type="number"
                                        min="1"
                                        value={itemData.quantity}
                                        onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value) || 1)}
                                        className="w-20"
                                      />
                                    </div>
                                  )}
                                </div>
                                
                                {itemData.selected && item.needsColor && (
                                  <div className="ml-6">
                                    <Label htmlFor={`color-${item.id}`} className="text-sm">
                                      Couleur souhaitée:
                                    </Label>
                                    <Input
                                      id={`color-${item.id}`}
                                      placeholder="Ex: Rouge, Bleu, Noir..."
                                      value={itemData.color || ''}
                                      onChange={(e) => handleColorChange(item.id, e.target.value)}
                                      className="mt-1"
                                    />
                                  </div>
                                )}
                                
                                {itemData.selected && item.isCustom && (
                                  <div className="ml-6">
                                    <Label htmlFor={`custom-${item.id}`} className="text-sm">
                                      Nom du produit:
                                    </Label>
                                    <Input
                                      id={`custom-${item.id}`}
                                      placeholder="Précisez le nom du produit"
                                      value={itemData.customName || ''}
                                      onChange={(e) => handleCustomNameChange(item.id, e.target.value)}
                                      className="mt-1"
                                      required={itemData.selected}
                                    />
                                  </div>
                                )}
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    ))}
                    
                    {/* Section pour "Autre produit" */}
                    <div className="space-y-3">
                      <h4 className="text-md font-medium text-gray-700 border-b pb-2">
                        ➕ Autre
                      </h4>
                      <div className="ml-4">
                        {(() => {
                          const item = { id: 'autre-produit', name: 'Autre produit (préciser le nom)', needsColor: false, isCustom: true }
                          const itemData = quoteData.selectedItems[item.id] || { selected: false, quantity: 1 }
                          return (
                            <div className="border rounded-lg p-4 space-y-3">
                              <div className="flex items-center space-x-3">
                                <Checkbox
                                  id={item.id}
                                  checked={itemData.selected}
                                  onCheckedChange={() => handleItemToggle(item.id)}
                                />
                                <Label htmlFor={item.id} className="flex-1 font-medium">
                                  {item.name}
                                </Label>
                                {itemData.selected && (
                                  <div className="flex items-center space-x-2">
                                    <Label htmlFor={`quantity-${item.id}`} className="text-sm">
                                      Quantité:
                                    </Label>
                                    <Input
                                      id={`quantity-${item.id}`}
                                      type="number"
                                      min="1"
                                      value={itemData.quantity}
                                      onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value) || 1)}
                                      className="w-20"
                                    />
                                  </div>
                                )}
                              </div>
                              
                              {itemData.selected && item.isCustom && (
                                <div className="ml-6">
                                  <Label htmlFor={`custom-${item.id}`} className="text-sm">
                                    Nom du produit:
                                  </Label>
                                  <Input
                                    id={`custom-${item.id}`}
                                    placeholder="Précisez le nom du produit"
                                    value={itemData.customName || ''}
                                    onChange={(e) => handleCustomNameChange(item.id, e.target.value)}
                                    className="mt-1"
                                    required={itemData.selected}
                                  />
                                </div>
                              )}
                            </div>
                          )
                        })()}
                      </div>
                    </div>
                  </div>
                </div>

                <Button type="submit" className="w-full" disabled={isQuoteSubmitting}>
                  {isQuoteSubmitting ? "Envoi en cours..." : "Envoyer ma demande de devis"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
