-- Table pour stocker les codes de vérification
CREATE TABLE IF NOT EXISTS verification_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  code TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used BOOLEAN DEFAULT FALSE
);

-- Index pour accélérer les recherches par email
CREATE INDEX IF NOT EXISTS idx_verification_codes_email ON verification_codes(email);

-- Politique RLS pour sécuriser la table
ALTER TABLE verification_codes ENABLE ROW LEVEL SECURITY;

-- Seuls les administrateurs peuvent accéder à cette table
CREATE POLICY "Admin can manage verification codes" 
  ON verification_codes 
  USING (auth.role() = 'service_role');

-- Fonction pour nettoyer les codes expirés
CREATE OR REPLACE FUNCTION cleanup_expired_verification_codes()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM verification_codes
  WHERE expires_at < NOW();
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour exécuter la fonction de nettoyage
DROP TRIGGER IF EXISTS trigger_cleanup_expired_verification_codes ON verification_codes;
CREATE TRIGGER trigger_cleanup_expired_verification_codes
  AFTER INSERT ON verification_codes
  EXECUTE PROCEDURE cleanup_expired_verification_codes();

-- Commentaires sur la table et les colonnes
COMMENT ON TABLE verification_codes IS 'Stocke les codes de vérification pour l''authentification à deux facteurs';
COMMENT ON COLUMN verification_codes.id IS 'Identifiant unique du code de vérification';
COMMENT ON COLUMN verification_codes.email IS 'Email de l''utilisateur';
COMMENT ON COLUMN verification_codes.code IS 'Code de vérification à 6 chiffres';
COMMENT ON COLUMN verification_codes.created_at IS 'Date et heure de création du code';
COMMENT ON COLUMN verification_codes.expires_at IS 'Date et heure d''expiration du code';
COMMENT ON COLUMN verification_codes.used IS 'Indique si le code a été utilisé';
