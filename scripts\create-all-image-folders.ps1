# Script pour créer tous les dossiers d'images nécessaires pour le site
# Ce script crée la structure complète des dossiers d'images

# Fonction pour créer un dossier s'il n'existe pas
function Create-Directory-If-Not-Exists {
    param (
        [string]$path
    )

    if (-not (Test-Path -Path $path)) {
        New-Item -Path $path -ItemType Directory -Force | Out-Null
        Write-Host "Créé: $path"
    } else {
        Write-Host "Existe déjà: $path"
    }
}

# Dossier racine des images
$imagesRoot = "public\images"

# 1. Bannières
$bannerTypes = @(
    "accueil",
    "promotions",
    "categories",
    "produits",
    "evenements",
    "saisonniers",
    "about",
    "contact",
    "models",
    "shop",
    "customize",
    "gallery",
    "account"
)

foreach ($type in $bannerTypes) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\banners\$type"
}

# 2. Designs
$designTypes = @(
    "abstraits",
    "animaux",
    "fleurs",
    "geometriques",
    "personnages",
    "sports",
    "marques",
    "personnalises",
    "abstract",
    "nature",
    "animals",
    "religious"
)

foreach ($type in $designTypes) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\designs\$type"
}

# 3. Coques de téléphone
# 3.1 iPhone
$iphoneModels = @(
    "iphone-15-pro-max",
    "iphone-15-pro",
    "iphone-15-plus",
    "iphone-15",
    "iphone-14-pro-max",
    "iphone-14-pro",
    "iphone-14-plus",
    "iphone-14",
    "iphone-13-pro-max",
    "iphone-13-pro",
    "iphone-13",
    "iphone-12-pro-max",
    "iphone-12-pro",
    "iphone-12",
    "iphone-se-2022"
)

foreach ($model in $iphoneModels) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\phone-cases\iphone\$model"
}

# 3.2 Samsung
$samsungModels = @(
    "galaxy-s24-ultra",
    "galaxy-s24-plus",
    "galaxy-s24",
    "galaxy-s23-ultra",
    "galaxy-s23-plus",
    "galaxy-s23",
    "galaxy-s22-ultra",
    "galaxy-s22-plus",
    "galaxy-s22",
    "galaxy-a54",
    "galaxy-a53",
    "galaxy-a34"
)

foreach ($model in $samsungModels) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\phone-cases\samsung\$model"
}

# 3.3 Google
$googleModels = @(
    "pixel-8-pro",
    "pixel-8",
    "pixel-7-pro",
    "pixel-7",
    "pixel-6-pro",
    "pixel-6"
)

foreach ($model in $googleModels) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\phone-cases\google\$model"
}

# 3.4 Types de coques
$caseTypes = @(
    "transparente",
    "silicone",
    "rigide",
    "antichoc",
    "portefeuille",
    "magnetique"
)

foreach ($type in $caseTypes) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\phone-cases\types\$type"
}

# 4. Modèles de téléphone
Create-Directory-If-Not-Exists -path "$imagesRoot\phone-models"

# 5. Produits
$productTypes = @(
    "coques",
    "accessoires",
    "personnalisation",
    "nouveautes",
    "promotions",
    "mugs",
    "tshirts",
    "mousepads",
    "cushions",
    "keychains"
)

foreach ($type in $productTypes) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\products\$type"
}

# 6. Promotions
$promoTypes = @(
    "seasonal",
    "bundle",
    "new"
)

foreach ($type in $promoTypes) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\promos\$type"
}

# 7. Témoignages
$testimonialTypes = @(
    "clients",
    "entreprises",
    "influenceurs"
)

foreach ($type in $testimonialTypes) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\testimonials\$type"
}

# 8. Galerie
Create-Directory-If-Not-Exists -path "$imagesRoot\gallery"

# 9. Équipe
Create-Directory-If-Not-Exists -path "$imagesRoot\team"

# 10. Logos
Create-Directory-If-Not-Exists -path "$imagesRoot\logos"

# 11. Icônes
Create-Directory-If-Not-Exists -path "$imagesRoot\icons"

# 12. Compte utilisateur
$accountTypes = @(
    "profile",
    "orders",
    "designs",
    "favorites"
)

foreach ($type in $accountTypes) {
    Create-Directory-If-Not-Exists -path "$imagesRoot\account\$type"
}

Write-Host "Création des dossiers terminée !"
