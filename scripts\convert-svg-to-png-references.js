/**
 * Script pour convertir les références aux images SVG en PNG/JPG dans le code
 * Ce script parcourt tous les fichiers .tsx, .jsx, .ts et .js du projet
 * et remplace les références aux images .svg par des références à des images .png ou .jpg
 */

const fs = require('fs');
const path = require('path');

// Fonction pour parcourir récursivement un répertoire
function walkDir(dir, callback) {
  if (!fs.existsSync(dir)) {
    console.log(`Le répertoire ${dir} n'existe pas.`);
    return;
  }
  
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    isDirectory ? walkDir(dirPath, callback) : callback(path.join(dir, f));
  });
}

// Fonction pour remplacer les références aux images SVG par des références à des images PNG/JPG
function replaceSvgReferences(filePath) {
  // Ignorer les fichiers node_modules, .git, etc.
  if (filePath.includes('node_modules') || filePath.includes('.git') || filePath.includes('.next')) {
    return;
  }
  
  // Vérifier l'extension du fichier
  const ext = path.extname(filePath).toLowerCase();
  if (!['.tsx', '.jsx', '.ts', '.js', '.md'].includes(ext)) {
    return;
  }
  
  console.log(`Traitement du fichier ${filePath}...`);
  
  try {
    // Lire le contenu du fichier
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Compter les occurrences avant le remplacement
    const svgCount = (content.match(/\.svg/g) || []).length;
    
    if (svgCount === 0) {
      console.log(`  Aucune référence SVG trouvée dans ${filePath}`);
      return;
    }
    
    // Remplacer les références aux images SVG par des références à des images PNG
    // Mais seulement dans les chemins d'images, pas dans les imports de composants SVG
    let modifiedContent = content.replace(
      /(["'])(\/images\/[^"']*?)\.svg(["'])/g, 
      (match, quote1, path, quote2) => {
        console.log(`  Remplacement de ${path}.svg par ${path}.png`);
        return `${quote1}${path}.png${quote2}`;
      }
    );
    
    // Compter les occurrences après le remplacement
    const pngCount = (modifiedContent.match(/\.png/g) || []).length;
    const remainingSvgCount = (modifiedContent.match(/\.svg/g) || []).length;
    
    // Écrire le contenu modifié dans le fichier
    fs.writeFileSync(filePath, modifiedContent, 'utf8');
    
    console.log(`  ${filePath}: ${svgCount} références SVG trouvées, ${pngCount - (modifiedContent.match(/\.png/g) || []).length + svgCount} remplacées par PNG, ${remainingSvgCount} références SVG restantes`);
  } catch (error) {
    console.error(`Erreur lors du traitement du fichier ${filePath}:`, error);
  }
}

// Fonction principale
function main() {
  console.log('Conversion des références aux images SVG en PNG/JPG...');
  
  // Parcourir les répertoires app et components
  walkDir('app', replaceSvgReferences);
  walkDir('components', replaceSvgReferences);
  walkDir('lib', replaceSvgReferences);
  walkDir('hooks', replaceSvgReferences);
  walkDir('services', replaceSvgReferences);
  
  console.log('Conversion terminée!');
}

// Exécuter la fonction principale
main();
