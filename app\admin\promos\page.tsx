"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { Calendar, Pencil, Trash2, Plus, Tag, Percent } from "lucide-react"

// Type pour les promotions
interface Promotion {
  id: string
  title: string
  description: string
  imageUrl: string
  discount: number
  startDate: string
  endDate: string
  code: string
  category: string
  active: boolean
}

export default function AdminPromosPage() {
  const router = useRouter()
  const [promotions, setPromotions] = useState<Promotion[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [currentPromo, setCurrentPromo] = useState<Promotion | null>(null)
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    imageUrl: "",
    discount: 0,
    startDate: "",
    endDate: "",
    code: "",
    category: "seasonal",
    active: true
  })

  // Simuler le chargement des promotions depuis une API
  useEffect(() => {
    // Dans une version réelle, ceci serait remplacé par un appel à l'API
    const mockPromotions: Promotion[] = [
      {
        id: "1",
        title: "Spécial Fête des Mères",
        description: "Offrez à votre mère une coque personnalisée avec une photo de famille et bénéficiez de 20% de réduction sur toute commande.",
        imageUrl: "/images/promos/fete-des-meres.jpg?v=1747269269099",
        discount: 20,
        startDate: "2023-05-15",
        endDate: "2023-05-31",
        code: "MAMAN2023",
        category: "seasonal",
        active: true
      },
      {
        id: "2",
        title: "Offre de Bienvenue",
        description: "15% de réduction sur votre première commande de coque personnalisée.",
        imageUrl: "/images/promos/welcome-offer.jpg?v=1747269269099",
        discount: 15,
        startDate: "2023-01-01",
        endDate: "2023-12-31",
        code: "BIENVENUE",
        category: "new",
        active: true
      },
      {
        id: "3",
        title: "Pack Famille",
        description: "Commandez 3 coques ou plus et bénéficiez de 25% de réduction sur le total.",
        imageUrl: "/images/promos/family-pack.jpg?v=1747269269099",
        discount: 25,
        startDate: "2023-04-01",
        endDate: "2023-06-30",
        code: "FAMILLE3",
        category: "bundle",
        active: true
      }
    ]

    setTimeout(() => {
      setPromotions(mockPromotions)
      setIsLoading(false)
    }, 1000)
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }))
  }

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      imageUrl: "",
      discount: 0,
      startDate: "",
      endDate: "",
      code: "",
      category: "seasonal",
      active: true
    })
    setCurrentPromo(null)
  }

  const openEditDialog = (promo: Promotion) => {
    setCurrentPromo(promo)
    setFormData({
      title: promo.title,
      description: promo.description,
      imageUrl: promo.imageUrl,
      discount: promo.discount,
      startDate: promo.startDate,
      endDate: promo.endDate,
      code: promo.code,
      category: promo.category,
      active: promo.active
    })
    setIsDialogOpen(true)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation de base
    if (!formData.title || !formData.code || !formData.startDate || !formData.endDate) {
      toast.error("Veuillez remplir tous les champs obligatoires")
      return
    }

    if (currentPromo) {
      // Mise à jour d'une promotion existante
      const updatedPromos = promotions.map(p => 
        p.id === currentPromo.id ? { ...formData, id: currentPromo.id } as Promotion : p
      )
      setPromotions(updatedPromos)
      toast.success("Promotion mise à jour avec succès")
    } else {
      // Création d'une nouvelle promotion
      const newPromo: Promotion = {
        ...formData,
        id: Date.now().toString()
      } as Promotion
      setPromotions([...promotions, newPromo])
      toast.success("Nouvelle promotion créée avec succès")
    }

    setIsDialogOpen(false)
    resetForm()
  }

  const handleDelete = (id: string) => {
    if (confirm("Êtes-vous sûr de vouloir supprimer cette promotion ?")) {
      setPromotions(promotions.filter(p => p.id !== id))
      toast.success("Promotion supprimée avec succès")
    }
  }

  const toggleActive = (id: string) => {
    setPromotions(promotions.map(p => 
      p.id === id ? { ...p, active: !p.active } : p
    ))
    toast.success("Statut de la promotion mis à jour")
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Gestion des Promotions</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="mr-2 h-4 w-4" /> Nouvelle Promotion
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{currentPromo ? "Modifier la promotion" : "Créer une nouvelle promotion"}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4 mt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre*</Label>
                  <Input 
                    id="title" 
                    name="title" 
                    value={formData.title} 
                    onChange={handleInputChange} 
                    required 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description" 
                    name="description" 
                    value={formData.description} 
                    onChange={handleInputChange} 
                    rows={3} 
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="discount">Réduction (%)*</Label>
                    <Input 
                      id="discount" 
                      name="discount" 
                      type="number" 
                      min="0" 
                      max="100" 
                      value={formData.discount} 
                      onChange={handleNumberChange} 
                      required 
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="code">Code Promo*</Label>
                    <Input 
                      id="code" 
                      name="code" 
                      value={formData.code} 
                      onChange={handleInputChange} 
                      required 
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startDate">Date de début*</Label>
                    <Input 
                      id="startDate" 
                      name="startDate" 
                      type="date" 
                      value={formData.startDate} 
                      onChange={handleInputChange} 
                      required 
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="endDate">Date de fin*</Label>
                    <Input 
                      id="endDate" 
                      name="endDate" 
                      type="date" 
                      value={formData.endDate} 
                      onChange={handleInputChange} 
                      required 
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Catégorie</Label>
                  <Select 
                    value={formData.category} 
                    onValueChange={(value) => handleSelectChange("category", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner une catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="seasonal">Saisonnière</SelectItem>
                      <SelectItem value="bundle">Pack</SelectItem>
                      <SelectItem value="new">Nouveaux clients</SelectItem>
                      <SelectItem value="special">Spéciale</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="imageUrl">URL de l'image</Label>
                  <Input 
                    id="imageUrl" 
                    name="imageUrl" 
                    value={formData.imageUrl} 
                    onChange={handleInputChange} 
                    placeholder="/images/promos/nom-image.jpg"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => {
                    setIsDialogOpen(false)
                    resetForm()
                  }}
                >
                  Annuler
                </Button>
                <Button type="submit">
                  {currentPromo ? "Mettre à jour" : "Créer"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des promotions</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="animate-pulse space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Titre</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Réduction</TableHead>
                  <TableHead>Période</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {promotions.map((promo) => (
                  <TableRow key={promo.id}>
                    <TableCell className="font-medium">{promo.title}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Tag className="h-4 w-4 mr-2 text-gray-500" />
                        {promo.code}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Percent className="h-4 w-4 mr-2 text-gray-500" />
                        {promo.discount}%
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="text-xs">
                          {new Date(promo.startDate).toLocaleDateString('fr-FR')} - {new Date(promo.endDate).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="capitalize">
                        {promo.category === "seasonal" && "Saisonnière"}
                        {promo.category === "bundle" && "Pack"}
                        {promo.category === "new" && "Nouveaux clients"}
                        {promo.category === "special" && "Spéciale"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={promo.active ? "bg-green-500" : "bg-gray-500"}>
                        {promo.active ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => toggleActive(promo.id)}
                        >
                          {promo.active ? "Désactiver" : "Activer"}
                        </Button>
                        <Button 
                          variant="outline" 
                          size="icon" 
                          onClick={() => openEditDialog(promo)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="icon" 
                          className="text-red-500" 
                          onClick={() => handleDelete(promo.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {promotions.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                      Aucune promotion disponible. Créez votre première promotion !
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}