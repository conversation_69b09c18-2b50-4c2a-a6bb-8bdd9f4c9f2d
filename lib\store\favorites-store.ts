"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"

// Type pour un produit favori
export interface FavoriteProduct {
  id: string
  name: string
  price: number
  image_url: string
}

// Interface pour le store de favoris
interface FavoritesStore {
  favorites: FavoriteProduct[]
  addFavorite: (product: FavoriteProduct) => void
  removeFavorite: (productId: string) => void
  toggleFavorite: (product: FavoriteProduct) => void
  clearFavorites: () => void
}

// Créer le store Zustand avec persistance
export const useFavoritesStore = create<FavoritesStore>()(
  persist(
    (set, get) => ({
      favorites: [],
      
      addFavorite: (product) => {
        try {
          console.log("Ajout du produit aux favoris:", product);
          set((state) => {
            // Vérifier si le produit existe déjà
            const exists = state.favorites.some(item => item.id === product.id);
            if (exists) return state; // Ne rien faire si le produit existe déjà
            
            return {
              favorites: [...state.favorites, product]
            };
          });
        } catch (error) {
          console.error("Erreur lors de l'ajout aux favoris:", error);
        }
      },
      
      removeFavorite: (productId) => {
        try {
          console.log("Suppression du produit des favoris:", productId);
          set((state) => ({
            favorites: state.favorites.filter((item) => item.id !== productId)
          }));
        } catch (error) {
          console.error("Erreur lors de la suppression des favoris:", error);
        }
      },
      
      toggleFavorite: (product) => {
        try {
          console.log("Toggle du produit dans les favoris:", product);
          const { favorites } = get();
          const exists = favorites.some((item) => item.id === product.id);
          
          if (exists) {
            get().removeFavorite(product.id);
          } else {
            get().addFavorite(product);
          }
        } catch (error) {
          console.error("Erreur lors du toggle des favoris:", error);
        }
      },
      
      clearFavorites: () => {
        try {
          console.log("Suppression de tous les favoris");
          set({ favorites: [] });
        } catch (error) {
          console.error("Erreur lors de la suppression de tous les favoris:", error);
        }
      }
    }),
    {
      name: "favorites-storage",
      // Utiliser localStorage au lieu de sessionStorage
      storage: {
        getItem: (name) => {
          try {
            // Vérifier si nous sommes côté client
            if (typeof window !== 'undefined') {
              const str = localStorage.getItem(name);
              if (!str) return null;
              return JSON.parse(str);
            }
            return null;
          } catch (error) {
            console.error("Erreur lors de la récupération des favoris:", error);
            return null;
          }
        },
        setItem: (name, value) => {
          try {
            // Vérifier si nous sommes côté client
            if (typeof window !== 'undefined') {
              localStorage.setItem(name, JSON.stringify(value));
            }
          } catch (error) {
            console.error("Erreur lors de l'enregistrement des favoris:", error);
          }
        },
        removeItem: (name) => {
          try {
            // Vérifier si nous sommes côté client
            if (typeof window !== 'undefined') {
              localStorage.removeItem(name);
            }
          } catch (error) {
            console.error("Erreur lors de la suppression des favoris:", error);
          }
        }
      }
    }
  )
)
