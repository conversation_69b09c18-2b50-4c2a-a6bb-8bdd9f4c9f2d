"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ShoppingCart, User, Search, Menu, X, Heart, Smartphone, LogOut, Package, Settings, FileText } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator, DropdownMenuLabel } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/hooks/use-auth"
import { useSimpleCart } from "@/hooks/use-simple-cart"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const { user, signOut } = useAuth()
  const { items } = useSimpleCart()

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)
  const toggleSearch = () => setIsSearchOpen(!isSearchOpen)

  return (
    <header className="sticky top-0 z-50 bg-white border-b border-gray-200 border-solid shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <img 
              src="/images/logos/hcp-design-logo.png" 
              alt="HCP-DESIGN CI Logo" 
              className="h-8 w-auto mr-2"
            />
            <span className="font-bold text-xl">HCP-DESIGN CI</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/promos" className="text-gray-700 hover:text-purple-600 font-medium">
              Promotions
            </Link>
            <Link href="/customize" className="text-gray-700 hover:text-purple-600 font-medium">
              Personnaliser
            </Link>
            <Link href="/gifts" className="text-gray-700 hover:text-purple-600 font-medium">
              🎁 Cadeaux & Gadgets
            </Link>
            <Link href="/products" className="text-gray-700 hover:text-purple-600 font-medium">
              Tous les produits
            </Link>
            <Link href="/models" className="text-gray-700 hover:text-purple-600 font-medium">
              Modèles
            </Link>
            <Link href="/about" className="text-gray-700 hover:text-purple-600 font-medium">
              À propos
            </Link>
            <Link href="/contact" className="text-gray-700 hover:text-purple-600 font-medium">
              Contact
            </Link>
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="ghost" size="icon" onClick={toggleSearch}>
              <Search className="h-5 w-5" />
            </Button>

            <Link href="/favorites">
              <Button variant="ghost" size="icon">
                <Heart className="h-5 w-5" />
              </Button>
            </Link>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className={user ? "text-purple-600" : ""}>
                  <User className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {user ? (
                  <>
                    <DropdownMenuLabel>Mon compte</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/account/profile" className="w-full flex items-center">
                        <User className="mr-2 h-4 w-4" /> Profil
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/account/orders" className="w-full flex items-center">
                        <Package className="mr-2 h-4 w-4" /> Mes commandes
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/account/designs" className="w-full flex items-center">
                        <FileText className="mr-2 h-4 w-4" /> Mes designs
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/account/settings" className="w-full flex items-center">
                        <Settings className="mr-2 h-4 w-4" /> Paramètres
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={signOut} className="text-red-500">
                      <LogOut className="mr-2 h-4 w-4" /> Déconnexion
                    </DropdownMenuItem>
                  </>
                ) : (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href="/login" className="w-full">
                        Connexion
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/register" className="w-full">
                        Créer un compte
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <Link href="/cart">
              <Button variant="ghost" size="icon" className="relative">
                <ShoppingCart className="h-5 w-5" />
                {items.length > 0 && (
                  <Badge className="absolute -top-2 -right-2 bg-purple-600 hover:bg-purple-700">
                    {items.length}
                  </Badge>
                )}
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <div className="flex md:hidden items-center space-x-4">
            <Link href="/cart">
              <Button variant="ghost" size="icon" className="relative">
                <ShoppingCart className="h-5 w-5" />
                {items.length > 0 && (
                  <Badge className="absolute -top-2 -right-2 bg-purple-600 hover:bg-purple-700">
                    {items.length}
                  </Badge>
                )}
              </Button>
            </Link>

            <Button variant="ghost" size="icon" onClick={toggleMenu}>
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        {isSearchOpen && (
          <div className="py-4 border-t border-gray-100 border-solid">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input placeholder="Rechercher..." className="pl-10 pr-10" autoFocus />
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 transform -translate-y-1/2"
                onClick={toggleSearch}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-100 border-solid">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex flex-col space-y-4">
              <Link
                href="/promos"
                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Promotions
              </Link>
              <Link
                href="/customize"
                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Personnaliser
              </Link>
              <Link
                href="/gifts"
                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                🎁 Cadeaux & Gadgets
              </Link>
              <Link
                href="/products"
                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Tous les produits
              </Link>
              <Link
                href="/models"
                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Modèles
              </Link>
              <Link
                href="/about"
                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                À propos
              </Link>
              <Link
                href="/contact"
                className="text-gray-700 hover:text-purple-600 font-medium py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Contact
              </Link>
              <div className="border-t border-gray-100 border-solid pt-4">
                {user ? (
                  <>
                    <Link
                      href="/account/profile"
                      className="text-gray-700 hover:text-purple-600 font-medium py-2 flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <User className="h-5 w-5 mr-2" /> Mon profil
                    </Link>
                    <Link
                      href="/account/orders"
                      className="text-gray-700 hover:text-purple-600 font-medium py-2 flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Package className="h-5 w-5 mr-2" /> Mes commandes
                    </Link>
                    <Link
                      href="/account/designs"
                      className="text-gray-700 hover:text-purple-600 font-medium py-2 flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <FileText className="h-5 w-5 mr-2" /> Mes designs
                    </Link>
                    <button
                      type="button"
                      onClick={() => {
                        signOut();
                        setIsMenuOpen(false);
                      }}
                      className="text-red-500 hover:text-red-600 font-medium py-2 flex items-center w-full"
                    >
                      <LogOut className="h-5 w-5 mr-2" /> Déconnexion
                    </button>
                  </>
                ) : (
                  <>
                    <Link
                      href="/login"
                      className="text-gray-700 hover:text-purple-600 font-medium py-2 flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <User className="h-5 w-5 mr-2" /> Connexion
                    </Link>
                    <Link
                      href="/register"
                      className="text-gray-700 hover:text-purple-600 font-medium py-2 flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <User className="h-5 w-5 mr-2" /> Créer un compte
                    </Link>
                  </>
                )}
                <Link
                  href="/favorites"
                  className="text-gray-700 hover:text-purple-600 font-medium py-2 flex items-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Heart className="h-5 w-5 mr-2" /> Favoris
                </Link>
              </div>
            </nav>
          </div>
        </div>
      )}
    </header>
  )
}



