#!/bin/bash

# Script pour créer les dossiers des variantes de produits

# Dossier de base pour les produits
BASE_DIR="./public/images/products"

# Créer les dossiers pour les variantes de tasses (mugs)
mkdir -p "$BASE_DIR/mugs/ceramique"
mkdir -p "$BASE_DIR/mugs/thermosensible"
mkdir -p "$BASE_DIR/mugs/isotherme"
mkdir -p "$BASE_DIR/mugs/emaillee"

# Créer les dossiers pour les variantes de t-shirts
mkdir -p "$BASE_DIR/tshirts/coton"
mkdir -p "$BASE_DIR/tshirts/premium"
mkdir -p "$BASE_DIR/tshirts/polo"
mkdir -p "$BASE_DIR/tshirts/manches-longues"

# Créer les dossiers pour les variantes de tapis de souris
mkdir -p "$BASE_DIR/mousepads/standard"
mkdir -p "$BASE_DIR/mousepads/xxl"
mkdir -p "$BASE_DIR/mousepads/ergonomique"
mkdir -p "$BASE_DIR/mousepads/rgb"

# Créer les dossiers pour les variantes de coussins
mkdir -p "$BASE_DIR/cushions/decoratif"
mkdir -p "$BASE_DIR/cushions/photo"
mkdir -p "$BASE_DIR/cushions/xxl"
mkdir -p "$BASE_DIR/cushions/cervical"

# Créer les dossiers pour les variantes de porte-clés
mkdir -p "$BASE_DIR/keychains/photo"
mkdir -p "$BASE_DIR/keychains/metal"
mkdir -p "$BASE_DIR/keychains/multifonction"
mkdir -p "$BASE_DIR/keychains/bois"

# Créer un fichier placeholder.png dans chaque dossier
for dir in $(find "$BASE_DIR" -type d); do
  if [ ! -f "$dir/placeholder.png" ]; then
    echo "Création du placeholder pour $dir"
    # Créer un fichier placeholder.png vide (1x1 pixel transparent)
    convert -size 1x1 xc:transparent "$dir/placeholder.png"
  fi
done

echo "Création des dossiers de variantes de produits terminée"
