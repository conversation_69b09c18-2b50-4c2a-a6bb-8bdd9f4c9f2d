"use client";

import { AdminLayout } from "@/components/admin/admin-layout";

interface AdminPageLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

export function AdminPageLayout({ children, title, description }: AdminPageLayoutProps) {
  return (
    <AdminLayout>
      {(title || description) && (
        <div className="mb-6">
          {title && <h1 className="text-2xl font-bold text-gray-900 mb-2">{title}</h1>}
          {description && <p className="text-gray-600">{description}</p>}
        </div>
      )}
      {children}
    </AdminLayout>
  );
}
