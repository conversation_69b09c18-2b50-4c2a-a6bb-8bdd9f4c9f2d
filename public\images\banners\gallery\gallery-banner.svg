<svg width="1200" height="400" viewBox="0 0 1200 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="phoneGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="400" fill="url(#bgGradient)"/>
  
  <!-- Decorative Elements -->
  <circle cx="100" cy="80" r="40" fill="rgba(255,255,255,0.1)"/>
  <circle cx="1100" cy="320" r="60" fill="rgba(255,255,255,0.1)"/>
  <circle cx="200" cy="350" r="25" fill="rgba(255,255,255,0.15)"/>
  <circle cx="1000" cy="100" r="35" fill="rgba(255,255,255,0.1)"/>
  
  <!-- Phone Mockups -->
  <g transform="translate(850, 50)">
    <!-- Phone 1 -->
    <rect x="0" y="0" width="80" height="140" rx="15" fill="url(#phoneGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <rect x="10" y="15" width="60" height="110" rx="8" fill="#667eea"/>
    <circle cx="40" cy="130" r="8" fill="rgba(255,255,255,0.8)"/>
    
    <!-- Phone 2 -->
    <rect x="100" y="20" width="80" height="140" rx="15" fill="url(#phoneGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <rect x="110" y="35" width="60" height="110" rx="8" fill="#764ba2"/>
    <circle cx="150" cy="150" r="8" fill="rgba(255,255,255,0.8)"/>
    
    <!-- Phone 3 -->
    <rect x="200" y="10" width="80" height="140" rx="15" fill="url(#phoneGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <rect x="210" y="25" width="60" height="110" rx="8" fill="#f093fb"/>
    <circle cx="250" cy="140" r="8" fill="rgba(255,255,255,0.8)"/>
  </g>
  
  <!-- Decorative content area - text will be handled by React component -->
  
  <!-- Decorative Patterns -->
  <g opacity="0.1">
    <path d="M0,300 Q300,250 600,300 T1200,300 L1200,400 L0,400 Z" fill="white"/>
  </g>
</svg>