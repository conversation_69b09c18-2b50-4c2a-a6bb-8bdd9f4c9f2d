"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft } from "lucide-react"
import BannerConfig from "@/components/admin/banner-config"
import { AdminAuthCheck } from "@/components/admin/admin-auth-check"

export default function AdminBannersPage() {
  const [activeTab, setActiveTab] = useState("promo")

  return (
    <AdminAuthCheck>
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <Link href="/admin">
              <Button variant="ghost" className="flex items-center gap-2 mb-2">
                <ArrowLeft className="h-4 w-4" /> Retour au tableau de bord
              </Button>
            </Link>
            <h1 className="text-3xl font-bold">Gestion des Bannières</h1>
            <p className="text-gray-500">Configurez les bannières animées du site</p>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-8">
          <TabsList className="mb-8">
            <TabsTrigger value="promo">Bannière Promotions</TabsTrigger>
            <TabsTrigger value="home">Bannière Accueil</TabsTrigger>
            <TabsTrigger value="custom">Bannière Personnalisation</TabsTrigger>
          </TabsList>

          <TabsContent value="promo">
            <BannerConfig />
          </TabsContent>

          <TabsContent value="home">
            <div className="bg-gray-100 p-8 rounded-lg text-center">
              <p className="text-gray-500">Configuration de la bannière d'accueil à venir</p>
            </div>
          </TabsContent>

          <TabsContent value="custom">
            <div className="bg-gray-100 p-8 rounded-lg text-center">
              <p className="text-gray-500">Configuration de la bannière de personnalisation à venir</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminAuthCheck>
  )
}
