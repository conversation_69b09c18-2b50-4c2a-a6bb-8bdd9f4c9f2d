import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth } from '@/lib/auth-server';

/**
 * Route API d'exemple protégée par authentification admin
 */
export async function GET(req: NextRequest) {
  return withAdminAuth(req, async (req, user) => {
    // Cette partie du code ne sera exécutée que si l'utilisateur est un admin
    return NextResponse.json({
      message: 'Vous êtes bien authentifié en tant qu\'administrateur',
      user: {
        id: user.id,
        email: user.email,
      }
    });
  });
}

/**
 * Route API d'exemple protégée par authentification admin
 */
export async function POST(req: NextRequest) {
  return withAdminAuth(req, async (req, user) => {
    // Récupérer les données de la requête
    const data = await req.json();
    
    // Cette partie du code ne sera exécutée que si l'utilisateur est un admin
    return NextResponse.json({
      message: 'Données reçues avec succès',
      data,
      user: {
        id: user.id,
        email: user.email,
      }
    });
  });
}
