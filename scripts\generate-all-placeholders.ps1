# Script pour générer des placeholders PNG pour tous les dossiers d'images
# Ce script utilise le nom du dossier comme nom de fichier

# Fonction pour créer un PNG placeholder
function New-PNGPlaceholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title,
        [string]$subtitle
    )

    # C<PERSON>er le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Vérifier si le fichier existe déjà
    if (Test-Path -Path $filePath) {
        Write-Host "Le fichier $filePath existe déjà."
        return
    }

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="600" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="500" height="300" rx="5" fill="#E5E7EB"/>
  <text x="300" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="300" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force

    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force

    Write-Host "Créé: $filePath"
}

# Fonction pour créer un PNG placeholder pour les bannières
function New-BannerPlaceholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title,
        [string]$subtitle
    )

    # Créer le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Vérifier si le fichier existe déjà
    if (Test-Path -Path $filePath) {
        Write-Host "Le fichier $filePath existe déjà."
        return
    }

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="1200" height="400" viewBox="0 0 1200 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="1200" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="1100" height="300" rx="5" fill="#E5E7EB"/>
  <text x="600" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="600" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force

    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force

    Write-Host "Créé: $filePath"
}

# Fonction pour créer un PNG placeholder pour les coques de téléphone
function New-PhoneCasePlaceholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title,
        [string]$subtitle
    )

    # Créer le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Vérifier si le fichier existe déjà
    if (Test-Path -Path $filePath) {
        Write-Host "Le fichier $filePath existe déjà."
        return
    }

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="300" height="600" viewBox="0 0 300 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="300" height="600" rx="40" fill="#F3F4F6"/>
  <rect x="30" y="30" width="240" height="540" rx="20" fill="#E5E7EB"/>
  <text x="150" y="300" font-family="Arial" font-size="24" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="150" y="330" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force

    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force

    Write-Host "Créé: $filePath"
}

# Fonction pour créer un PNG placeholder pour les témoignages
function New-TestimonialPlaceholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title,
        [string]$subtitle
    )

    # Créer le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Vérifier si le fichier existe déjà
    if (Test-Path -Path $filePath) {
        Write-Host "Le fichier $filePath existe déjà."
        return
    }

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="100" cy="100" r="100" fill="#F3F4F6"/>
  <circle cx="100" cy="100" r="80" fill="#E5E7EB"/>
  <text x="100" y="100" font-family="Arial" font-size="24" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="100" y="130" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force

    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force

    Write-Host "Créé: $filePath"
}

# Fonction pour créer un PNG placeholder pour les icônes
function New-IconPlaceholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title
    )

    # Créer le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Vérifier si le fichier existe déjà
    if (Test-Path -Path $filePath) {
        Write-Host "Le fichier $filePath existe déjà."
        return
    }

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" rx="8" fill="#F3F4F6"/>
  <rect x="8" y="8" width="48" height="48" rx="4" fill="#E5E7EB"/>
  <text x="32" y="36" font-family="Arial" font-size="12" text-anchor="middle" fill="#6B7280">$title</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force

    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force

    Write-Host "Créé: $filePath"
}

# Générer des placeholders pour les bannières
$bannerTypes = @(
    "accueil",
    "promotions",
    "categories",
    "produits",
    "evenements",
    "saisonniers",
    "about",
    "contact",
    "models",
    "shop",
    "customize",
    "gallery",
    "account"
)

foreach ($type in $bannerTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-BannerPlaceholder -folderPath "public\images\banners\$type" -fileName $type -title "Bannière $typeName" -subtitle "Format standard"
}

# Générer des placeholders pour les designs
$designTypes = @(
    "abstraits",
    "animaux",
    "fleurs",
    "geometriques",
    "personnages",
    "sports",
    "marques",
    "personnalises",
    "abstract",
    "nature",
    "animals",
    "religious"
)

foreach ($type in $designTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-PNGPlaceholder -folderPath "public\images\designs\$type" -fileName $type -title "Design $typeName" -subtitle "Motif personnalisable"
}

# Générer des placeholders pour les produits
$productTypes = @(
    "coques",
    "accessoires",
    "personnalisation",
    "nouveautes",
    "promotions",
    "mugs",
    "tshirts",
    "mousepads",
    "cushions",
    "keychains"
)

foreach ($type in $productTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-PNGPlaceholder -folderPath "public\images\products\$type" -fileName $type -title "Produit $typeName" -subtitle "Catégorie principale"
}

# Générer des placeholders pour les témoignages
$testimonialTypes = @(
    "clients",
    "entreprises",
    "influenceurs"
)

foreach ($type in $testimonialTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-TestimonialPlaceholder -folderPath "public\images\testimonials\$type" -fileName $type -title "Témoignage" -subtitle $typeName
}

# Générer des placeholders pour les sections du compte
$accountTypes = @(
    "profile",
    "orders",
    "designs",
    "favorites"
)

foreach ($type in $accountTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-PNGPlaceholder -folderPath "public\images\account\$type" -fileName $type -title "Compte" -subtitle $typeName
}

# Générer des placeholders pour la galerie
New-PNGPlaceholder -folderPath "public\images\gallery" -fileName "gallery" -title "Galerie" -subtitle "Images diverses"

# Générer des placeholders pour l'équipe
New-PNGPlaceholder -folderPath "public\images\team" -fileName "team" -title "Équipe" -subtitle "Photos de l'équipe"

# Générer des placeholders pour les logos
New-PNGPlaceholder -folderPath "public\images\logos" -fileName "logo" -title "Logo" -subtitle "HCP-DESIGN CI"

# Générer des placeholders pour les icônes
$iconTypes = @(
    "whatsapp",
    "facebook",
    "instagram",
    "cart",
    "user",
    "search",
    "menu",
    "close"
)

foreach ($type in $iconTypes) {
    New-IconPlaceholder -folderPath "public\images\icons" -fileName $type -title $type
}

Write-Host "Génération des placeholders terminée !"
