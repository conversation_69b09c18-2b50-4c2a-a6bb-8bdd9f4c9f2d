"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { getAnimatedBannerImagePaths } from "@/lib/image-paths"

interface AnimatedBannerProps {
  images: {
    src: string
    alt: string
  }[]
  title: string
  subtitle: string
  badge?: string
  code?: string
  interval?: number // en millisecondes
}

export default function AnimatedBanner({
  images,
  title,
  subtitle,
  badge,
  code,
  interval = 4000, // 4 secondes par défaut
}: AnimatedBannerProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isZooming, setIsZooming] = useState(false)

  useEffect(() => {
    // Fonction pour passer à l'image suivante avec effet de zoom
    const rotateImages = () => {
      setIsZooming(true)

      // Après l'animation de zoom, changer l'image
      setTimeout(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length)
        setIsZooming(false)
      }, 1000) // Durée de l'animation de zoom
    }

    // Configurer l'intervalle pour changer d'image
    const intervalId = setInterval(rotateImages, interval)

    // Nettoyer l'intervalle lors du démontage du composant
    return () => clearInterval(intervalId)
  }, [images.length, interval])

  return (
    <div className="relative w-full h-80 md:h-120 rounded-xl overflow-hidden">
      {/* Images avec effet de zoom */}
      {images.map((image, index) => (
        <div
          key={index}
          className={cn(
            "absolute inset-0 transition-all duration-1000 ease-in-out",
            index === currentImageIndex ? "opacity-100" : "opacity-0",
            isZooming && index === currentImageIndex ? "scale-110" : "scale-100"
          )}
        >
          <Image
            src={image.src}
            alt={image.alt}
            fill
            priority={index === 0}
            sizes="(max-width: 768px) 100vw, 1200px"
            quality={90}
            style={{ objectFit: "cover", objectPosition: "center" }}
          />
          {/* Overlay plus léger pour mieux voir l'image */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-900/50 to-pink-600/50"></div>
        </div>
      ))}

      {/* Contenu de la bannière */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-white p-6 z-10">
        {badge && <Badge className="bg-pink-600 mb-4">{badge}</Badge>}
        <h2 className="text-3xl md:text-5xl font-bold mb-4 text-center">{title}</h2>
        <p className="text-lg md:text-xl mb-6 text-center max-w-2xl">
          {subtitle}
        </p>
        {code && (
          <Badge className="text-lg px-4 py-2 bg-white text-pink-600">CODE: {code}</Badge>
        )}
      </div>

      {/* Indicateurs de navigation */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-10">
        {images.map((_, index) => (
          <button
            key={index}
            type="button"
            className={cn(
              "w-2 h-2 rounded-full transition-all",
              index === currentImageIndex ? "bg-white w-4" : "bg-white/50"
            )}
            onClick={() => setCurrentImageIndex(index)}
            aria-label={`Voir l'image ${index + 1}`}
          />
        ))}
      </div>
    </div>
  )
}
