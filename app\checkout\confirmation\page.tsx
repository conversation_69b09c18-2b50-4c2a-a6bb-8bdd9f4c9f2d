"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle2, Home, Package, Truck } from "lucide-react"

export default function OrderConfirmationPage() {
  // Données fictives pour la commande
  const orderDetails = {
    orderNumber: "CMD-12345",
    date: new Date().toLocaleDateString("fr-FR"),
    total: "9 980 FCFA",
    paymentMethod: "Carte bancaire",
    shippingAddress: "123 Rue Principale, Abidjan, Côte d'Ivoire",
    estimatedDelivery: "2-4 jours ouvrables"
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
            <CheckCircle2 className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold mb-2">Commande confirmée !</h1>
          <p className="text-gray-600">
            Merci pour votre commande. Nous vous enverrons un email de confirmation avec les détails de votre commande.
          </p>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Détails de la commande</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Numéro de commande</p>
                <p className="font-medium">{orderDetails.orderNumber}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Date</p>
                <p className="font-medium">{orderDetails.date}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Total</p>
                <p className="font-medium">{orderDetails.total}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Méthode de paiement</p>
                <p className="font-medium">{orderDetails.paymentMethod}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="mr-2 h-5 w-5" /> Informations de livraison
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500">Adresse de livraison</p>
              <p className="font-medium">{orderDetails.shippingAddress}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Délai de livraison estimé</p>
              <p className="font-medium">{orderDetails.estimatedDelivery}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Étapes suivantes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100">
                    <Package className="h-4 w-4 text-purple-600" />
                  </div>
                </div>
                <div>
                  <h3 className="font-medium">Préparation de votre commande</h3>
                  <p className="text-sm text-gray-600">
                    Nous préparons votre commande et vous enverrons un email lorsqu'elle sera expédiée.
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100">
                    <Truck className="h-4 w-4 text-purple-600" />
                  </div>
                </div>
                <div>
                  <h3 className="font-medium">Livraison</h3>
                  <p className="text-sm text-gray-600">
                    Votre commande sera livrée à l'adresse indiquée dans un délai de {orderDetails.estimatedDelivery}.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Link href="/">
              <Button className="bg-purple-600 hover:bg-purple-700">
                <Home className="mr-2 h-4 w-4" /> Retour à l'accueil
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </main>
  )
}
