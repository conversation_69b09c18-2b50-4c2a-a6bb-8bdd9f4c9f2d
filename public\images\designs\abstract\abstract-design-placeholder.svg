<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="#f9fafb"/>
  
  <!-- Abstract design -->
  <rect x="50" y="50" width="300" height="300" fill="#ffffff" stroke="#e5e7eb" stroke-width="2" rx="5" ry="5"/>
  
  <!-- Abstract shapes -->
  <circle cx="150" cy="150" r="80" fill="#6d28d9" opacity="0.1"/>
  <circle cx="250" cy="150" r="60" fill="#8b5cf6" opacity="0.2"/>
  <circle cx="150" cy="250" r="60" fill="#a78bfa" opacity="0.2"/>
  <circle cx="250" cy="250" r="80" fill="#c4b5fd" opacity="0.1"/>
  
  <rect x="100" y="100" width="200" height="10" fill="#6d28d9" opacity="0.3" transform="rotate(45, 200, 200)"/>
  <rect x="100" y="120" width="200" height="10" fill="#8b5cf6" opacity="0.3" transform="rotate(45, 200, 200)"/>
  <rect x="100" y="140" width="200" height="10" fill="#a78bfa" opacity="0.3" transform="rotate(45, 200, 200)"/>
  
  <rect x="100" y="100" width="200" height="10" fill="#6d28d9" opacity="0.3" transform="rotate(-45, 200, 200)"/>
  <rect x="100" y="120" width="200" height="10" fill="#8b5cf6" opacity="0.3" transform="rotate(-45, 200, 200)"/>
  <rect x="100" y="140" width="200" height="10" fill="#a78bfa" opacity="0.3" transform="rotate(-45, 200, 200)"/>
  
  <text x="200" y="370" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#6d28d9">Design Abstrait</text>
  <text x="200" y="390" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#6b7280">Dimensions recommandées: 400 x 400 pixels</text>
</svg>
