"use client"

import { useState, useEffect } from "react"
import AnimatedBanner from "@/components/animated-banner"
import { getAnimatedBannerImagePaths } from "@/lib/image-paths"

interface TypedAnimatedBannerProps {
  type: string;
  title: string;
  subtitle: string;
  badge?: string;
  code?: string;
  interval?: number; // en millisecondes
}

/**
 * Composant de bannière animée qui utilise le type pour récupérer les images
 * @param type - Type de bannière (ex: "promotions")
 * @param title - Titre de la bannière
 * @param subtitle - Sous-titre de la bannière
 * @param badge - Badge optionnel à afficher
 * @param code - Code promo optionnel à afficher
 * @param interval - Intervalle en millisecondes entre les transitions (défaut: 4000ms)
 */
export default function TypedAnimatedBanner({
  type,
  title,
  subtitle,
  badge,
  code,
  interval = 4000, // 4 secondes par défaut
}: TypedAnimatedBannerProps) {
  const [images, setImages] = useState<{src: string, alt: string}[]>([]);
  
  // Récupérer les chemins des images pour ce type de bannière
  useEffect(() => {
    const imagePaths = getAnimatedBannerImagePaths(type);
    
    // Convertir les chemins en objets d'images
    const imageObjects = imagePaths.map((path, index) => ({
      src: path,
      alt: `Bannière ${type} ${index + 1}`
    }));
    
    setImages(imageObjects);
  }, [type]);
  
  // Si aucune image n'est disponible, ne rien afficher
  if (images.length === 0) {
    return null;
  }

  return (
    <AnimatedBanner
      images={images}
      title={title}
      subtitle={subtitle}
      badge={badge}
      code={code}
      interval={interval}
    />
  );
}
