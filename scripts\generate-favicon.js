const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Fonction pour créer un favicon simple
function createFavicon() {
  // Créer un canvas de 32x32 pixels
  const canvas = createCanvas(32, 32);
  const ctx = canvas.getContext('2d');

  // Dessiner un fond violet (couleur principale du site)
  ctx.fillStyle = '#6d28d9';
  ctx.fillRect(0, 0, 32, 32);

  // Ajouter le texte "HCP"
  ctx.fillStyle = 'white';
  ctx.font = 'bold 16px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('HCP', 16, 16);

  // Convertir le canvas en buffer PNG
  const buffer = canvas.toBuffer('image/png');

  // Sauvegarder le fichier
  const publicDir = path.join(__dirname, '..', 'public');
  
  // C<PERSON>er le répertoire s'il n'existe pas
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }

  // Sauvegarder le favicon.png
  fs.writeFileSync(path.join(publicDir, 'favicon.png'), buffer);
  console.log('Favicon PNG créé avec succès!');

  // Sauvegarder aussi en tant que favicon.ico (même contenu pour l'instant)
  fs.writeFileSync(path.join(publicDir, 'favicon.ico'), buffer);
  console.log('Favicon ICO créé avec succès!');

  // Créer les différentes tailles d'icônes pour les appareils mobiles
  const sizes = [16, 32, 48, 64, 72, 96, 120, 128, 144, 152, 180, 192, 384, 512];
  
  // Créer le répertoire pour les icônes
  const iconsDir = path.join(publicDir, 'icons');
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
  }

  // Générer les icônes de différentes tailles
  sizes.forEach(size => {
    const sizeCanvas = createCanvas(size, size);
    const sizeCtx = sizeCanvas.getContext('2d');
    
    // Dessiner un fond violet
    sizeCtx.fillStyle = '#6d28d9';
    sizeCtx.fillRect(0, 0, size, size);
    
    // Ajouter le texte "HCP"
    sizeCtx.fillStyle = 'white';
    sizeCtx.font = `bold ${size / 2}px Arial`;
    sizeCtx.textAlign = 'center';
    sizeCtx.textBaseline = 'middle';
    sizeCtx.fillText('HCP', size / 2, size / 2);
    
    // Sauvegarder l'icône
    const sizeBuffer = sizeCanvas.toBuffer('image/png');
    fs.writeFileSync(path.join(iconsDir, `icon-${size}.png`), sizeBuffer);
    console.log(`Icône ${size}x${size} créée avec succès!`);
  });
}

// Exécuter la fonction
createFavicon();
