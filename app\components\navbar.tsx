"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { ShoppingCart } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function Navbar() {
  const [cartCount, setCartCount] = useState(0)

  useEffect(() => {
    const fetchCartCount = async () => {
      try {
        const response = await fetch('/api/cart')
        if (!response.ok) throw new Error('Erreur lors de la récupération du panier')
        const data = await response.json()
        setCartCount(data.items.length)
      } catch (error) {
        console.error('Erreur:', error)
      }
    }

    fetchCartCount()
  }, [])

  return (
    <nav className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-purple-600">
            HCP Design
          </Link>

          <div className="flex items-center gap-4">
            <Link href="/products">
              <Button variant="ghost">Produits</Button>
            </Link>
            <Link href="/cart">
              <Button variant="ghost" className="relative">
                <ShoppingCart className="w-5 h-5" />
                {cartCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-purple-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {cartCount}
                  </span>
                )}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
} 