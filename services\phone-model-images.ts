/**
 * Service pour gérer les images des modèles de téléphone
 * Ce service centralise l'accès aux images des différents modèles de téléphone
 * et sera utilisé par les composants qui ont besoin d'afficher ces images
 *
 * Note: Ce service utilise à la fois les fichiers locaux et Supabase pour stocker et récupérer les images
 * des modèles de téléphone. Les fonctions sont asynchrones et retournent des Promises.
 */

import {
  getDefaultImageForModelFromSupabase,
  getImagesForModelFromSupabase,
  getAllModelImagesFromSupabase,
  addModelImageToSupabase,
  updateModelImageInSupabase,
  deleteModelImageFromSupabase
} from './phone-model-images-supabase';
import { supabase } from '@/lib/supabase';
import { phoneModelImagesData } from '@/data/phone-model-images-data';

// Interface pour les images de modèles de téléphone
export interface PhoneModelImage {
  id: string;
  modelId: string;
  imageUrl: string;
  isDefault: boolean;
  name: string;
}

// Les données sont stockées à la fois localement et dans Supabase
// et sont récupérées via les fonctions asynchrones

// Image par défaut si le modèle n'est pas trouvé
// Cette valeur est également définie dans le service Supabase
const defaultImage = "/images/phone-cases/phone-case-placeholder.png";

/**
 * Convertit un ID de modèle en chemin de dossier local
 * @param modelId - L'identifiant du modèle de téléphone (ex: iphone15pro)
 * @returns Le chemin du dossier local pour ce modèle (ex: iphone/iphone-15-pro)
 */
function modelIdToFolderPath(modelId: string): { brand: string, model: string } {
  // Extraire la marque et le modèle de l'ID
  let brand = '';
  let model = '';

  if (modelId.includes('iphone')) {
    brand = 'iphone';
    // Convertir iphone15pro en iphone-15-pro
    model = modelId.replace('iphone', 'iphone-');
    // Insérer des tirets avant les chiffres et avant "pro", "plus", "max", etc.
    model = model.replace(/(\d+)/, '-$1-');
    // Nettoyer les doubles tirets
    model = model.replace(/--/g, '-');
    // Supprimer le tiret à la fin si présent
    model = model.endsWith('-') ? model.slice(0, -1) : model;
  } else if (modelId.includes('samsung') || modelId.includes('galaxy')) {
    brand = 'samsung';
    // Convertir samsungs23ultra ou galaxys23ultra en galaxy-s23-ultra
    model = modelId.replace('samsung', 'galaxy-').replace('galaxy', 'galaxy-');
    // Insérer des tirets avant les chiffres
    model = model.replace(/(\d+)/, '-$1-');
    // Nettoyer les doubles tirets
    model = model.replace(/--/g, '-');
    // Supprimer le tiret à la fin si présent
    model = model.endsWith('-') ? model.slice(0, -1) : model;
  } else if (modelId.includes('pixel')) {
    brand = 'google';
    // Convertir pixel7pro en pixel-7-pro
    model = modelId.replace(/(\d+)/, '-$1-');
    // Nettoyer les doubles tirets
    model = model.replace(/--/g, '-');
    // Supprimer le tiret à la fin si présent
    model = model.endsWith('-') ? model.slice(0, -1) : model;
  }

  return { brand, model };
}

/**
 * Récupère les images locales pour un modèle de téléphone donné
 * @param modelId - L'identifiant du modèle de téléphone
 * @returns Promise avec un tableau des images disponibles localement pour ce modèle
 */
async function getLocalImagesForModel(modelId: string): Promise<PhoneModelImage[]> {
  try {
    console.log(`Recherche d'images locales pour le modèle: ${modelId}`);

    // Utiliser les données statiques au lieu de lire le système de fichiers
    if (phoneModelImagesData[modelId]) {
      console.log(`Images trouvées dans les données statiques pour ${modelId}:`, phoneModelImagesData[modelId].images);

      // Ajouter un timestamp aux URLs des images pour éviter la mise en cache
      return phoneModelImagesData[modelId].images.map(img => ({
        ...img,
        imageUrl: `${img.imageUrl}?v=${Date.now()}`
      }));
    }

    // Si le modèle n'est pas trouvé dans les données statiques, retourner un tableau vide
    console.warn(`Aucune donnée statique trouvée pour le modèle ${modelId}`);
    return [];
  } catch (error) {
    console.error('Erreur lors de la récupération des images locales:', error);
    return [];
  }
}

/**
 * Récupère l'image par défaut pour un modèle de téléphone donné
 * @param modelId - L'identifiant du modèle de téléphone
 * @returns Promise avec l'URL de l'image par défaut pour ce modèle, ou l'image par défaut générique si non trouvée
 */
export async function getDefaultImageForModel(modelId: string): Promise<string> {
  try {
    // Essayer d'abord de récupérer l'image par défaut localement
    const localImages = await getLocalImagesForModel(modelId);
    const defaultLocalImage = localImages.find(img => img.isDefault);

    if (defaultLocalImage) {
      return defaultLocalImage.imageUrl;
    }

    // Si aucune image locale n'est trouvée, essayer Supabase
    return await getDefaultImageForModelFromSupabase(modelId);
  } catch (error) {
    console.warn('Erreur lors de la récupération de l\'image par défaut:', error);
    return defaultImage;
  }
}

/**
 * Récupère toutes les images disponibles pour un modèle de téléphone donné
 * @param modelId - L'identifiant du modèle de téléphone
 * @returns Promise avec un tableau des images disponibles pour ce modèle
 */
export async function getImagesForModel(modelId: string): Promise<PhoneModelImage[]> {
  try {
    console.log(`Récupération des images pour le modèle: ${modelId}`);

    // Récupérer les images locales
    const localImages = await getLocalImagesForModel(modelId);
    console.log(`Images locales trouvées pour ${modelId}:`, localImages);

    // Récupérer les images de Supabase
    const supabaseImages = await getImagesForModelFromSupabase(modelId);
    console.log(`Images Supabase trouvées pour ${modelId}:`, supabaseImages);

    // Combiner les deux sources d'images (en évitant les doublons)
    const allImages = [...localImages];

    // Ajouter les images Supabase qui n'ont pas le même nom que les images locales
    for (const supabaseImage of supabaseImages) {
      const exists = localImages.some(localImg =>
        localImg.name.toLowerCase() === supabaseImage.name.toLowerCase()
      );

      if (!exists) {
        allImages.push(supabaseImage);
      }
    }

    console.log(`Total des images trouvées pour ${modelId}:`, allImages);

    // Vérifier si nous avons au moins une image
    if (allImages.length === 0) {
      console.warn(`Aucune image trouvée pour le modèle ${modelId}. Vérifiez les chemins et les noms de fichiers.`);
    }

    return allImages;
  } catch (error) {
    console.error('Erreur lors de la récupération des images:', error);
    return [];
  }
}

/**
 * Récupère toutes les images locales de tous les modèles
 * @returns Promise avec un tableau de toutes les images locales
 */
async function getAllLocalModelImages(): Promise<PhoneModelImage[]> {
  try {
    // Utiliser les données statiques au lieu de lire le système de fichiers
    const allImages: PhoneModelImage[] = [];

    // Parcourir tous les modèles dans les données statiques
    Object.keys(phoneModelImagesData).forEach(modelId => {
      allImages.push(...phoneModelImagesData[modelId].images);
    });

    return allImages;
  } catch (error) {
    console.warn('Erreur lors de la récupération de toutes les images locales:', error);
    return [];
  }
}

/**
 * Récupère toutes les images de modèles disponibles
 * @returns Promise avec un tableau de toutes les images de modèles
 */
export async function getAllModelImages(): Promise<PhoneModelImage[]> {
  try {
    // Récupérer les images locales
    const localImages = await getAllLocalModelImages();

    // Récupérer les images de Supabase
    const supabaseImages = await getAllModelImagesFromSupabase();

    // Combiner les deux sources d'images (en évitant les doublons)
    const allImages = [...localImages];

    // Ajouter les images Supabase qui n'ont pas le même nom que les images locales
    for (const supabaseImage of supabaseImages) {
      const exists = localImages.some(localImg =>
        localImg.modelId === supabaseImage.modelId &&
        localImg.name.toLowerCase() === supabaseImage.name.toLowerCase()
      );

      if (!exists) {
        allImages.push(supabaseImage);
      }
    }

    return allImages;
  } catch (error) {
    console.warn('Erreur lors de la récupération de toutes les images:', error);
    return [];
  }
}

/**
 * Ajoute une nouvelle image de modèle
 * @param image - L'image de modèle à ajouter
 * @returns Promise avec l'image ajoutée avec un ID généré, ou null en cas d'erreur
 */
export async function addModelImage(image: Omit<PhoneModelImage, 'id'>): Promise<PhoneModelImage | null> {
  return await addModelImageToSupabase(image);
}

/**
 * Met à jour une image de modèle existante
 * @param image - L'image de modèle à mettre à jour
 * @returns Promise avec l'image mise à jour, ou null en cas d'erreur
 */
export async function updateModelImage(image: PhoneModelImage): Promise<PhoneModelImage | null> {
  return await updateModelImageInSupabase(image);
}

/**
 * Supprime une image de modèle
 * @param id - L'ID de l'image à supprimer
 * @returns Promise avec true si l'image a été supprimée, false sinon
 */
export async function deleteModelImage(id: string): Promise<boolean> {
  return await deleteModelImageFromSupabase(id);
}