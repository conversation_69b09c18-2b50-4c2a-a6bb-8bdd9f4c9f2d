# Script pour générer des placeholders PNG pour tous les modèles de téléphones
# Ce script utilise le nom du dossier comme nom de fichier

# Modèles iPhone
$iphoneModels = @(
    "iphone-15-pro-max",
    "iphone-15-pro",
    "iphone-15-plus",
    "iphone-15",
    "iphone-14-pro-max",
    "iphone-14-pro",
    "iphone-14-plus",
    "iphone-14",
    "iphone-13-pro-max",
    "iphone-13-pro",
    "iphone-13",
    "iphone-12-pro-max",
    "iphone-12-pro",
    "iphone-12",
    "iphone-se-2022"
)

# Modèles Samsung
$samsungModels = @(
    "galaxy-s24-ultra",
    "galaxy-s24-plus",
    "galaxy-s24",
    "galaxy-s23-ultra",
    "galaxy-s23-plus",
    "galaxy-s23",
    "galaxy-s22-ultra",
    "galaxy-s22-plus",
    "galaxy-s22",
    "galaxy-a54",
    "galaxy-a53",
    "galaxy-a34"
)

# Mod<PERSON>les Google
$googleModels = @(
    "pixel-8-pro",
    "pixel-8",
    "pixel-7-pro",
    "pixel-7",
    "pixel-6-pro",
    "pixel-6"
)

# Types de coques
$caseTypes = @(
    "transparente",
    "silicone",
    "rigide",
    "antichoc",
    "portefeuille",
    "magnetique"
)

# Types de bannières
$bannerTypes = @(
    "accueil",
    "promotions",
    "categories",
    "produits",
    "evenements",
    "saisonniers"
)

# Types de témoignages
$testimonialTypes = @(
    "clients",
    "entreprises",
    "influenceurs"
)

# Types de designs
$designTypes = @(
    "abstraits",
    "animaux",
    "fleurs",
    "geometriques",
    "personnages",
    "sports",
    "marques",
    "personnalises"
)

# Types de produits
$productTypes = @(
    "coques",
    "accessoires",
    "personnalisation",
    "nouveautes",
    "promotions"
)

# Fonction pour créer un PNG placeholder
function New-PNGPlaceholder {
    param (
        [string]$folderPath,
        [string]$fileName,
        [string]$title,
        [string]$subtitle
    )

    # Créer le dossier parent s'il n'existe pas
    if (-not (Test-Path -Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
    }

    # Chemin complet du fichier
    $filePath = Join-Path -Path $folderPath -ChildPath "$fileName.png"

    # Créer un fichier SVG temporaire
    $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
    $svgContent = @"
<svg width="300" height="600" viewBox="0 0 300 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="300" height="600" rx="40" fill="#F3F4F6"/>
  <rect x="30" y="30" width="240" height="540" rx="20" fill="#E5E7EB"/>
  <text x="150" y="300" font-family="Arial" font-size="24" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="150" y="330" font-family="Arial" font-size="16" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
    Set-Content -Path $tempSvgPath -Value $svgContent -Force

    # Copier le fichier SVG avec l'extension PNG
    Copy-Item -Path $tempSvgPath -Destination $filePath -Force

    # Supprimer le fichier temporaire
    Remove-Item -Path $tempSvgPath -Force

    Write-Host "Créé: $filePath"
}

# Créer des placeholders pour les modèles iPhone
foreach ($model in $iphoneModels) {
    $modelName = $model -replace "iphone-", "iPhone " -replace "-", " " -replace "pro max", "Pro Max" -replace "pro", "Pro" -replace "plus", "Plus"
    New-PNGPlaceholder -folderPath "public\images\phone-cases\iphone\$model" -fileName $model -title $modelName -subtitle "Coque transparente"
}

# Créer des placeholders pour les modèles Samsung
foreach ($model in $samsungModels) {
    $modelName = $model -replace "galaxy-", "Galaxy " -replace "-", " " -replace "ultra", "Ultra" -replace "plus", "Plus"
    New-PNGPlaceholder -folderPath "public\images\phone-cases\samsung\$model" -fileName $model -title $modelName -subtitle "Coque transparente"
}

# Créer des placeholders pour les modèles Google
foreach ($model in $googleModels) {
    $modelName = $model -replace "pixel-", "Pixel " -replace "-", " " -replace "pro", "Pro"
    New-PNGPlaceholder -folderPath "public\images\phone-cases\google\$model" -fileName $model -title $modelName -subtitle "Coque transparente"
}

# Créer des placeholders pour les types de coques
foreach ($type in $caseTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-PNGPlaceholder -folderPath "public\images\phone-cases\types\$type" -fileName $type -title "Coque $typeName" -subtitle "Pour tous les modèles"
}

# Créer des placeholders pour les types de bannières
foreach ($type in $bannerTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-PNGPlaceholder -folderPath "public\images\banners\$type" -fileName $type -title "Bannière $typeName" -subtitle "Format standard"
}

# Créer des placeholders pour les types de témoignages
foreach ($type in $testimonialTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-PNGPlaceholder -folderPath "public\images\testimonials\$type" -fileName $type -title "Témoignage $typeName" -subtitle "Photo de profil"
}

# Créer des placeholders pour les types de designs
foreach ($type in $designTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-PNGPlaceholder -folderPath "public\images\designs\$type" -fileName $type -title "Design $typeName" -subtitle "Motif personnalisable"
}

# Créer des placeholders pour les types de produits
foreach ($type in $productTypes) {
    $typeName = $type.Substring(0,1).ToUpper() + $type.Substring(1)
    New-PNGPlaceholder -folderPath "public\images\products\$type" -fileName $type -title "Produit $typeName" -subtitle "Catégorie principale"
}

# Créer des bannières animées pour les promotions
function New-AnimatedBannerImages {
    param (
        [string]$bannerType,
        [int]$count = 3
    )

    $directory = "public\images\banners\$bannerType"

    # Vérifier si le dossier existe
    if (-not (Test-Path -Path $directory)) {
        Write-Host "Le dossier $directory n'existe pas."
        return
    }

    # Créer les images de la bannière animée
    for ($i = 1; $i -le $count; $i++) {
        $filePath = Join-Path -Path $directory -ChildPath "$bannerType-$i.png"

        # Vérifier si le fichier existe déjà
        if (Test-Path -Path $filePath) {
            Write-Host "Le fichier $filePath existe déjà."
            continue
        }

        # Créer une image pour la bannière animée
        $title = "Bannière $($bannerType.Substring(0,1).ToUpper() + $bannerType.Substring(1)) $i"
        $subtitle = "Image $i pour animation"

        # Créer un fichier SVG temporaire
        $tempSvgPath = [System.IO.Path]::GetTempFileName() + ".svg"
        $svgContent = @"
<svg width="1200" height="400" viewBox="0 0 1200 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="1200" height="400" rx="10" fill="#F3F4F6"/>
  <rect x="50" y="50" width="1100" height="300" rx="5" fill="#E5E7EB"/>
  <text x="600" y="180" font-family="Arial" font-size="36" text-anchor="middle" fill="#6B7280">$title</text>
  <text x="600" y="230" font-family="Arial" font-size="24" text-anchor="middle" fill="#9CA3AF">$subtitle</text>
</svg>
"@
        Set-Content -Path $tempSvgPath -Value $svgContent -Force

        # Copier le fichier SVG avec l'extension PNG
        Copy-Item -Path $tempSvgPath -Destination $filePath -Force

        # Supprimer le fichier temporaire
        Remove-Item -Path $tempSvgPath -Force

        Write-Host "Créé: $filePath"
    }
}

# Créer des bannières animées pour les promotions
New-AnimatedBannerImages -bannerType "promotions"

Write-Host "Génération des placeholders terminée !"

