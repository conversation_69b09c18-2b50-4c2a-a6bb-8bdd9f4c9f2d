// Script pour initialiser la table site_settings dans Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Récupérer les variables d'environnement
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Erreur: Variables d\'environnement manquantes.');
  console.error('Assurez-vous que NEXT_PUBLIC_SUPABASE_URL et SUPABASE_SERVICE_KEY sont définis dans .env.local');
  process.exit(1);
}

// Créer le client Supabase avec la clé de service
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Paramètres par défaut du site
const defaultSettings = {
  site_name: "HCP Design CI",
  site_description: "Coques de téléphone personnalisées",
  primary_color: "#6d28d9",
  secondary_color: "#8b5cf6",
  show_whatsapp_button: true,
  whatsapp_number: "+2250709495849",
  navigation: [
    { name: "Accueil", path: "/" },
    { name: "Produits", path: "/produits" },
    { name: "Promo", path: "/promo" },
    { name: "Personnaliser", path: "/customize" },
    { name: "Contact", path: "/contact" },
  ],
  home_banner: {
    enabled: true,
    images: [
      "/images/home/<USER>",
      "/images/home/<USER>",
      "/images/home/<USER>",
    ],
    transition_time: 4000,
    transition_effect: "zoom",
  },
};

// Fonction pour créer la table site_settings si elle n'existe pas
async function createSiteSettingsTable() {
  try {
    // Vérifier si la table existe déjà
    const { error: checkError } = await supabase
      .from('site_settings')
      .select('count')
      .limit(1);
    
    if (checkError && checkError.code === '42P01') {
      // La table n'existe pas, la créer
      console.log('La table site_settings n\'existe pas. Création de la table...');
      
      // Exécuter le SQL pour créer la table
      const { error: createError } = await supabase.rpc('exec_sql', {
        sql_query: `
          CREATE TABLE IF NOT EXISTS public.site_settings (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            key TEXT NOT NULL UNIQUE,
            value JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          CREATE INDEX IF NOT EXISTS idx_site_settings_key ON public.site_settings(key);
        `
      });
      
      if (createError) {
        console.error('Erreur lors de la création de la table site_settings:', createError);
        return false;
      }
      
      console.log('Table site_settings créée avec succès!');
      return true;
    } else if (checkError) {
      console.error('Erreur lors de la vérification de la table site_settings:', checkError);
      return false;
    } else {
      console.log('La table site_settings existe déjà.');
      return true;
    }
  } catch (error) {
    console.error('Exception lors de la création de la table site_settings:', error);
    return false;
  }
}

// Fonction pour insérer les paramètres par défaut
async function insertDefaultSettings() {
  try {
    console.log('Insertion des paramètres par défaut...');
    
    // Convertir les paramètres par défaut en tableau d'objets {key, value}
    const settingsArray = Object.entries(defaultSettings).map(([key, value]) => ({
      key,
      value: typeof value === 'object' ? JSON.stringify(value) : value
    }));
    
    // Insérer les paramètres
    const { error } = await supabase
      .from('site_settings')
      .upsert(settingsArray, { onConflict: 'key' });
    
    if (error) {
      console.error('Erreur lors de l\'insertion des paramètres par défaut:', error);
      return false;
    }
    
    console.log('Paramètres par défaut insérés avec succès!');
    return true;
  } catch (error) {
    console.error('Exception lors de l\'insertion des paramètres par défaut:', error);
    return false;
  }
}

// Fonction principale
async function main() {
  try {
    // Créer la table si elle n'existe pas
    const tableCreated = await createSiteSettingsTable();
    
    if (!tableCreated) {
      console.error('Impossible de créer la table site_settings. Arrêt du script.');
      process.exit(1);
    }
    
    // Insérer les paramètres par défaut
    const settingsInserted = await insertDefaultSettings();
    
    if (!settingsInserted) {
      console.error('Impossible d\'insérer les paramètres par défaut. Arrêt du script.');
      process.exit(1);
    }
    
    console.log('Initialisation de la table site_settings terminée avec succès!');
    process.exit(0);
  } catch (error) {
    console.error('Erreur lors de l\'initialisation de la table site_settings:', error);
    process.exit(1);
  }
}

// Exécuter le script
main();
