/**
 * Service pour gérer les images des modèles de téléphone avec Supabase
 * Ce service centralise l'accès aux images des différents modèles de téléphone
 * et sera utilisé par les composants qui ont besoin d'afficher ces images
 */

import { supabase } from '@/lib/supabase';
import { PhoneModelImage } from './phone-model-images';

// Image par défaut si le modèle n'est pas trouvé
const defaultImage = "/images/phone-cases/phone-case-placeholder.png";

/**
 * Récupère l'image par défaut pour un modèle de téléphone donné
 * @param modelId - L'identifiant du modèle de téléphone
 * @returns L'URL de l'image par défaut pour ce modèle, ou l'image par défaut générique si non trouvée
 */
export async function getDefaultImageForModelFromSupabase(modelId: string): Promise<string> {
  try {
    // Vérifier si modelId est valide
    if (!modelId) {
      console.warn('Avertissement: modelId non fourni pour la récupération de l\'image par défaut');
      return defaultImage;
    }

    const { data, error } = await supabase
      .from('phone_model_images')
      .select('image_url')
      .eq('model_id', modelId)
      .eq('is_default', true)
      .single();

    if (error) {
      // Vérifier si l'erreur est due à une table inexistante
      if (error.code === '42P01') {
        console.warn('La table phone_model_images n\'existe pas encore dans la base de données');
      } else if (error.code === 'PGRST116') { // Code pour "Results contain 0 rows"
        console.warn(`Aucune image par défaut trouvée pour le modèle: ${modelId}`);
      } else {
        console.warn('Avertissement lors de la récupération de l\'image par défaut:', error.message || 'Erreur inconnue');
      }
      return defaultImage;
    }

    if (!data || !data.image_url) {
      return defaultImage;
    }

    return data.image_url;
  } catch (error) {
    console.warn('Problème lors de la récupération de l\'image par défaut:', error instanceof Error ? error.message : 'Erreur inconnue');
    return defaultImage;
  }
}

/**
 * Récupère toutes les images disponibles pour un modèle de téléphone donné
 * @param modelId - L'identifiant du modèle de téléphone
 * @returns Un tableau des images disponibles pour ce modèle
 */
export async function getImagesForModelFromSupabase(modelId: string): Promise<PhoneModelImage[]> {
  try {
    // Vérifier si modelId est valide
    if (!modelId) {
      console.warn('Avertissement: modelId non fourni pour la récupération des images');
      return [];
    }

    const { data, error } = await supabase
      .from('phone_model_images')
      .select('*')
      .eq('model_id', modelId);

    if (error) {
      // Vérifier si l'erreur est due à une table inexistante
      if (error.code === '42P01') { // Code PostgreSQL pour "relation does not exist"
        console.warn('La table phone_model_images n\'existe pas encore dans la base de données');
      } else {
        console.warn('Avertissement lors de la récupération des images:', error.message || 'Erreur inconnue');
      }
      return [];
    }

    if (!data || data.length === 0) {
      // Pas d'erreur mais aucune donnée trouvée
      return [];
    }

    return data.map(item => ({
      id: item.id,
      modelId: item.model_id,
      imageUrl: item.image_url,
      isDefault: item.is_default,
      name: item.name
    }));
  } catch (error) {
    // Utiliser console.warn au lieu de console.error pour éviter l'affichage dans la console d'erreurs
    console.warn('Problème lors de la récupération des images:', error instanceof Error ? error.message : 'Erreur inconnue');
    return [];
  }
}

/**
 * Récupère toutes les images de modèles disponibles
 * @returns Un tableau de toutes les images de modèles
 */
export async function getAllModelImagesFromSupabase(): Promise<PhoneModelImage[]> {
  try {
    const { data, error } = await supabase
      .from('phone_model_images')
      .select('*');

    if (error) {
      // Vérifier si l'erreur est due à une table inexistante
      if (error.code === '42P01') {
        console.warn('La table phone_model_images n\'existe pas encore dans la base de données');
      } else {
        console.warn('Avertissement lors de la récupération de toutes les images:', error.message || 'Erreur inconnue');
      }
      return [];
    }

    if (!data || data.length === 0) {
      // Pas d'erreur mais aucune donnée trouvée
      return [];
    }

    return data.map(item => ({
      id: item.id,
      modelId: item.model_id,
      imageUrl: item.image_url,
      isDefault: item.is_default,
      name: item.name
    }));
  } catch (error) {
    console.warn('Problème lors de la récupération de toutes les images:', error instanceof Error ? error.message : 'Erreur inconnue');
    return [];
  }
}

/**
 * Ajoute une nouvelle image de modèle
 * @param image - L'image de modèle à ajouter
 * @returns L'image ajoutée avec un ID généré
 */
export async function addModelImageToSupabase(image: Omit<PhoneModelImage, 'id'>): Promise<PhoneModelImage | null> {
  try {
    // Si la nouvelle image est définie comme par défaut, mettre à jour les autres images du même modèle
    if (image.isDefault) {
      await supabase
        .from('phone_model_images')
        .update({ is_default: false })
        .eq('model_id', image.modelId);
    }

    const { data, error } = await supabase
      .from('phone_model_images')
      .insert({
        model_id: image.modelId,
        image_url: image.imageUrl,
        is_default: image.isDefault,
        name: image.name
      })
      .select()
      .single();

    if (error || !data) {
      console.error('Erreur lors de l\'ajout de l\'image:', error);
      return null;
    }

    return {
      id: data.id,
      modelId: data.model_id,
      imageUrl: data.image_url,
      isDefault: data.is_default,
      name: data.name
    };
  } catch (error) {
    console.error('Erreur lors de l\'ajout de l\'image:', error);
    return null;
  }
}

/**
 * Met à jour une image de modèle existante
 * @param image - L'image de modèle à mettre à jour
 * @returns L'image mise à jour
 */
export async function updateModelImageInSupabase(image: PhoneModelImage): Promise<PhoneModelImage | null> {
  try {
    // Si l'image est définie comme par défaut, mettre à jour les autres images du même modèle
    if (image.isDefault) {
      await supabase
        .from('phone_model_images')
        .update({ is_default: false })
        .eq('model_id', image.modelId)
        .neq('id', image.id);
    }

    const { data, error } = await supabase
      .from('phone_model_images')
      .update({
        model_id: image.modelId,
        image_url: image.imageUrl,
        is_default: image.isDefault,
        name: image.name
      })
      .eq('id', image.id)
      .select()
      .single();

    if (error || !data) {
      console.error('Erreur lors de la mise à jour de l\'image:', error);
      return null;
    }

    return {
      id: data.id,
      modelId: data.model_id,
      imageUrl: data.image_url,
      isDefault: data.is_default,
      name: data.name
    };
  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'image:', error);
    return null;
  }
}

/**
 * Supprime une image de modèle
 * @param id - L'ID de l'image à supprimer
 * @returns true si l'image a été supprimée, false sinon
 */
export async function deleteModelImageFromSupabase(id: string): Promise<boolean> {
  try {
    // Vérifier si l'image est définie comme par défaut
    const { data: imageData } = await supabase
      .from('phone_model_images')
      .select('is_default')
      .eq('id', id)
      .single();

    if (imageData?.is_default) {
      throw new Error("Vous ne pouvez pas supprimer l'image par défaut d'un modèle. Veuillez d'abord définir une autre image comme par défaut.");
    }

    const { error } = await supabase
      .from('phone_model_images')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Erreur lors de la suppression de l\'image:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'image:', error);
    return false;
  }
}

/**
 * Télécharge une image dans le stockage Supabase et retourne l'URL
 * @param file - Le fichier à télécharger
 * @param modelId - L'ID du modèle de téléphone
 * @returns L'URL de l'image téléchargée
 */
export async function uploadModelImage(file: File, modelId: string): Promise<string | null> {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${modelId}_${Date.now()}.${fileExt}`;
    const filePath = `phone-models/${fileName}`;

    const { data, error } = await supabase.storage
      .from('images')
      .upload(filePath, file);

    if (error || !data) {
      console.error('Erreur lors du téléchargement de l\'image:', error);
      return null;
    }

    const { data: publicUrlData } = supabase.storage
      .from('images')
      .getPublicUrl(filePath);

    return publicUrlData.publicUrl;
  } catch (error) {
    console.error('Erreur lors du téléchargement de l\'image:', error);
    return null;
  }
}