/**
 * Script pour corriger les chemins d'accès aux images dans le code
 */

const fs = require('fs');
const path = require('path');

// Fonction pour lire un fichier
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier ${filePath}:`, error);
    return null;
  }
}

// Fonction pour écrire dans un fichier
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'écriture dans le fichier ${filePath}:`, error);
    return false;
  }
}

// Fonction pour vérifier si un fichier existe
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Fonction pour corriger les références aux images placeholder
function fixPlaceholderReferences(filePath) {
  console.log(`Vérification des références aux images placeholder dans ${filePath}...`);
  
  const content = readFile(filePath);
  if (!content) return false;
  
  // Recherche des références aux images placeholder
  const placeholderRegex = /(?:src|image|imageUrl|thumbnail):\s*["'](\/placeholder\.svg|placeholder\.svg)(?:\?[^"']*)?["']/g;
  let match;
  let modified = false;
  let newContent = content;
  
  const replacements = [];
  
  while ((match = placeholderRegex.exec(content)) !== null) {
    const fullMatch = match[0];
    const placeholderPath = match[1];
    
    // Déterminer le type d'image et proposer une alternative
    let alternativePath = '';
    
    // Déterminer le contexte pour choisir une image appropriée
    if (filePath.includes('gallery')) {
      alternativePath = '/images/gallery/gallery-placeholder.svg';
    } else if (filePath.includes('promos')) {
      alternativePath = '/images/banners/promotions/banner1.png';
    } else if (filePath.includes('brands')) {
      alternativePath = '/images/brands/apple.svg';
    } else if (filePath.includes('phone-models')) {
      alternativePath = '/images/phone-models/iphone15.png';
    } else {
      alternativePath = '/images/placeholder.svg';
    }
    
    // Remplacer la référence
    const newReference = fullMatch.replace(placeholderPath, alternativePath);
    replacements.push({ from: fullMatch, to: newReference });
    
    modified = true;
  }
  
  // Appliquer les remplacements
  for (const replacement of replacements) {
    newContent = newContent.replace(replacement.from, replacement.to);
  }
  
  // Enregistrer les modifications
  if (modified) {
    console.log(`Correction de ${replacements.length} références aux images placeholder dans ${filePath}`);
    return writeFile(filePath, newContent);
  }
  
  return false;
}

// Fonction pour parcourir récursivement un répertoire
function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    isDirectory ? walkDir(dirPath, callback) : callback(path.join(dir, f));
  });
}

// Fonction principale
function main() {
  console.log("Début de la correction des chemins d'accès aux images...");
  
  let totalFixed = 0;
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire app
  walkDir('app', (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const fixed = fixPlaceholderReferences(filePath);
      if (fixed) totalFixed++;
    }
  });
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire components
  walkDir('components', (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const fixed = fixPlaceholderReferences(filePath);
      if (fixed) totalFixed++;
    }
  });
  
  console.log(`Correction des chemins d'accès aux images terminée. ${totalFixed} fichiers modifiés.`);
}

// Exécuter la fonction principale
main();
