"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { supabase, User, supabase<PERSON>pi } from "@/lib/supabase";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (
    email: string,
    password: string,
    userData: { firstName: string; lastName: string; whatsapp: string }
  ) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const router = useRouter();

  useEffect(() => {
    // Vérifier si l'utilisateur est déjà connecté
    const checkUser = async () => {
      setIsLoading(true);
      try {
        // Vérifie d'abord la session
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          setUser(null);
          setIsLoading(false);
          return;
        }
        // Utiliser directement l'API Supabase pour récupérer l'utilisateur actuel
        const { data: { user: authUser }, error } = await supabase.auth.getUser();
        if (error) {
          console.error("Erreur lors de la récupération de l'utilisateur:", error);
          setUser(null);
          setIsLoading(false);
          return;
        }
        if (!authUser) {
          setUser(null);
          setIsLoading(false);
          return;
        }
        // Récupérer les informations supplémentaires de l'utilisateur depuis la table profiles
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", authUser.id)
          .single();
        if (profileError) {
          console.error("Erreur lors de la récupération du profil:", profileError);
          // Si nous ne pouvons pas récupérer le profil, utiliser au moins les données de base de l'utilisateur
          setUser({
            id: authUser.id,
            email: authUser.email || '',
            first_name: '',
            last_name: '',
            whatsapp: '',
            created_at: authUser.created_at || ''
          });
        } else if (profileData) {
          setUser(profileData as User);
        }
      } catch (error) {
        console.error("Erreur inattendue:", error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkUser();

    // Écouter les changements d'authentification
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("Événement d'authentification:", event);

      if (event === "SIGNED_IN" && session?.user) {
        console.log("Utilisateur connecté:", session.user);

        // Récupérer les informations supplémentaires de l'utilisateur
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", session.user.id)
          .single();

        if (profileError) {
          console.error("Erreur lors de la récupération du profil:", profileError);
          // Si nous ne pouvons pas récupérer le profil, utiliser au moins les données de base de l'utilisateur
          setUser({
            id: session.user.id,
            email: session.user.email || '',
            first_name: '',
            last_name: '',
            whatsapp: '',
            created_at: session.user.created_at || ''
          });
        } else if (profileData) {
          setUser(profileData as User);
        }

        // Ne pas faire de redirection automatique depuis le hook use-auth
        // La redirection sera gérée par les composants de page individuels
        console.log('Utilisateur connecté, session établie');
      } else if (event === "SIGNED_OUT") {
        console.log("Utilisateur déconnecté");
        setUser(null);
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      console.log('[useAuth] Début de la connexion pour:', email);
      
      // Effacer d'abord tout état d'authentification existant
      console.log('[useAuth] Déconnexion préalable');
      await supabase.auth.signOut();
      
      // Puis tenter la connexion
      console.log('[useAuth] Tentative de connexion');
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error("[useAuth] Erreur de connexion:", error.message);
        return { success: false, error: error.message };
      }

      console.log('[useAuth] Connexion réussie, données:', {
        user: data.user ? { id: data.user.id, email: data.user.email } : null,
        session: data.session ? 'Présent' : 'Absent'
      });

      if (data?.user) {
        // Récupérer les informations supplémentaires de l'utilisateur depuis la table profiles
        console.log('[useAuth] Récupération du profil utilisateur');
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", data.user.id)
          .single();
        
        if (profileError) {
          console.error("[useAuth] Erreur lors de la récupération du profil:", profileError);
          // Si nous ne pouvons pas récupérer le profil, utiliser au moins les données de base de l'utilisateur
          setUser({
            id: data.user.id,
            email: data.user.email || '',
            first_name: '',
            last_name: '',
            whatsapp: '',
            created_at: data.user.created_at || ''
          });
        } else if (profileData) {
          console.log('[useAuth] Profil récupéré avec succès');
          setUser(profileData as User);
        } else {
          console.log('[useAuth] Aucun profil trouvé, utilisation des données de base');
          setUser({
            id: data.user.id,
            email: data.user.email || '',
            first_name: '',
            last_name: '',
            whatsapp: '',
            created_at: data.user.created_at || ''
          });
        }
        
        return { success: true };
      } else {
        console.error("[useAuth] Aucun utilisateur retourné après connexion réussie");
        return { success: false, error: "Aucun utilisateur retourné" };
      }
    } catch (err: any) {
      console.error("[useAuth] Exception lors de la connexion:", err);
      return { success: false, error: err.message };
    }
  };

  // Suppression des marqueurs de conflit de fusion <<<<<<< HEAD et >>>>>>> main
  // Correction des références à 'data' non défini
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);

      if (error) {
        console.error("Erreur de réinitialisation du mot de passe:", error.message);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (err: any) {
      console.error("Exception lors de la réinitialisation du mot de passe:", err);
      return { success: false, error: err.message };
    }
  };

  const signUp = async (
    email: string,
    password: string,
    userData: { firstName: string; lastName: string; whatsapp: string }
  ) => {
    try {
      // Créer un nouvel utilisateur en utilisant directement l'API Supabase
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: userData.firstName,
            last_name: userData.lastName,
            whatsapp: userData.whatsapp,
          }
        }
      });

      if (error) {
        console.error("Erreur d'inscription:", error);
        toast({
          title: "Erreur d'inscription",
          description: error.message,
          variant: "destructive",
        });
        return { success: false, error: error.message };
      }

      console.log("Inscription réussie:", data.user);
      toast({
        title: "Inscription réussie",
        description: "Votre compte a été créé avec succès.",
      });

      return { success: true };
    } catch (error: any) {
      console.error("Exception lors de l'inscription:", error);
      toast({
        title: "Erreur d'inscription",
        description: error.message || "Une erreur s'est produite lors de l'inscription.",
        variant: "destructive",
      });
      return { success: false, error: error.message };
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error("Erreur lors de la déconnexion:", error);
        toast({
          title: "Erreur de déconnexion",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Déconnexion réussie",
        description: "Vous êtes maintenant déconnecté.",
      });

      setUser(null);
    } catch (error: any) {
      console.error("Exception lors de la déconnexion:", error);
      toast({
        title: "Erreur de déconnexion",
        description: error.message || "Une erreur s'est produite lors de la déconnexion.",
        variant: "destructive",
      });
    }
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, signIn, signUp, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth doit être utilisé à l'intérieur d'un AuthProvider");
  }
  return context;
}
