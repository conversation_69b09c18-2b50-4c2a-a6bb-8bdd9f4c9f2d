import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createClient } from '@supabase/supabase-js'

// Initialiser le client Supabase
function getSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase environment variables')
  }

  return createClient(supabaseUrl, supabaseKey)
}

export async function GET() {
  try {
    const supabase = getSupabaseClient()
    const cookieStore = await cookies()
    const sessionId = cookieStore.get('session_id')?.value

    if (!sessionId) {
      return NextResponse.json({ items: [] })
    }

    // Récupérer les items du panier directement avec user_id
    const { data: cartItems, error } = await supabase
      .from('cart_items')
      .select(`
        id,
        product_id,
        quantity,
        customized,
        customization_data,
        products (
          name,
          price,
          image_url
        )
      `)
      .eq('user_id', sessionId)

    if (error) throw error

    const formattedItems = cartItems?.map(item => ({
      id: item.id,
      productId: item.product_id,
      name: (item.products as any)?.name || '',
      image: (item.products as any)?.image_url || '',
      quantity: item.quantity,
      price: (item.products as any)?.price || 0,
      customized: item.customized,
      customization_data: item.customization_data
    })) || []

    return NextResponse.json({ items: formattedItems })
  } catch (error) {
    console.error('Erreur lors de la récupération du panier:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération du panier' },
      { status: 500 }
    )
  }
}