"use client";

import { useState, useEffect, useRef } from "react";
import <PERSON>ript from "next/script";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, RefreshCw, ShieldCheck } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface CaptchaProps {
  onVerify: (token: string) => void;
  onExpire?: () => void;
  onError?: () => void;
}

/**
 * Composant pour intégrer Google reCAPTCHA v3
 * Adapté pour fonctionner avec les clés v3 existantes
 */
export function Captcha({ onVerify, onExpire, onError }: CaptchaProps) {
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [scriptError, setScriptError] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [token, setToken] = useState<string | null>(null);

  // Clé de site reCAPTCHA v3
  const SITE_KEY = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || "6LcZ3TgrAAAAAOnap0r_cX5yFApJZwCux0rvz-T4";

  // Fonction pour exécuter reCAPTCHA v3
  const executeReCaptcha = async () => {
    if (!window.grecaptcha || !window.grecaptcha.execute) {
      console.error("grecaptcha.execute n'est pas disponible");
      setScriptError(true);
      return;
    }

    try {
      setIsVerifying(true);
      setIsVerified(false);

      // Exécuter reCAPTCHA avec l'action 'login'
      const newToken = await window.grecaptcha.execute(SITE_KEY, { action: 'login' });
      console.log("reCAPTCHA exécuté avec succès");

      setToken(newToken);
      setIsVerified(true);
      onVerify(newToken);
    } catch (error) {
      console.error("Erreur lors de l'exécution de reCAPTCHA:", error);
      setScriptError(true);
      if (onError) onError();
    } finally {
      setIsVerifying(false);
    }
  };

  // Gérer le chargement du script
  const handleScriptLoad = () => {
    console.log("Script reCAPTCHA v3 chargé");
    setScriptLoaded(true);
    setScriptError(false);

    // Attendre que grecaptcha soit disponible
    if (window.grecaptcha) {
      if (window.grecaptcha.ready) {
        window.grecaptcha.ready(() => {
          console.log("reCAPTCHA prêt à être utilisé");
          executeReCaptcha();
        });
      } else {
        // Fallback si ready n'est pas disponible
        setTimeout(executeReCaptcha, 1000);
      }
    } else {
      // Fallback si grecaptcha n'est pas disponible immédiatement
      setTimeout(() => {
        if (window.grecaptcha) {
          executeReCaptcha();
        } else {
          setScriptError(true);
        }
      }, 2000);
    }
  };

  // Gérer l'erreur de chargement du script
  const handleScriptError = () => {
    console.error("Erreur lors du chargement du script reCAPTCHA");
    setScriptError(true);
    setScriptLoaded(false);
  };

  // Initialiser le captcha lorsque le script est chargé
  useEffect(() => {
    // Exposer la fonction de réinitialisation
    window.resetCaptcha = executeReCaptcha;

    // Configurer un timer pour rafraîchir le token toutes les 90 secondes
    // (les tokens reCAPTCHA v3 expirent après 2 minutes)
    const refreshInterval = setInterval(() => {
      if (scriptLoaded && !scriptError) {
        executeReCaptcha();
      }
    }, 90000);

    return () => {
      // Nettoyer
      clearInterval(refreshInterval);
      if (window.resetCaptcha) {
        delete window.resetCaptcha;
      }
    };
  }, [scriptLoaded, scriptError]);

  // Tenter de recharger le captcha
  const reloadCaptcha = () => {
    setScriptError(false);
    setIsVerified(false);
    setToken(null);

    // Recharger le script
    const recaptchaScript = document.querySelector('script[src*="recaptcha/api.js"]');
    if (recaptchaScript) {
      recaptchaScript.remove();
    }

    // Réinitialiser l'état
    setScriptLoaded(false);

    // Ajouter un délai avant de recharger
    setTimeout(() => {
      handleScriptLoad();
    }, 500);
  };

  return (
    <>
      <Script
        src={`https://www.google.com/recaptcha/api.js?render=${SITE_KEY}`}
        async
        defer
        onLoad={handleScriptLoad}
        onError={handleScriptError}
        strategy="afterInteractive"
      />

      <div className="flex flex-col items-center my-4">
        {scriptError ? (
          <div className="w-full">
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Impossible de charger le captcha. Veuillez vérifier votre connexion internet ou désactiver les bloqueurs de publicités.
              </AlertDescription>
            </Alert>
            <Button
              onClick={reloadCaptcha}
              variant="outline"
              className="w-full"
            >
              <RefreshCw className="mr-2 h-4 w-4" /> Recharger le captcha
            </Button>
          </div>
        ) : (
          <div className="w-full text-center">
            {isVerifying ? (
              <div className="flex items-center justify-center space-x-2 py-2">
                <svg className="animate-spin h-5 w-5 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-sm text-gray-600">Vérification de sécurité en cours...</span>
              </div>
            ) : isVerified ? (
              <div className="flex items-center justify-center space-x-2 py-2 text-green-600">
                <ShieldCheck className="h-5 w-5" />
                <span className="text-sm">Vérification de sécurité réussie</span>
              </div>
            ) : (
              <Button
                onClick={executeReCaptcha}
                variant="outline"
                className="w-full"
              >
                <ShieldCheck className="mr-2 h-4 w-4" /> Vérifier que vous n'êtes pas un robot
              </Button>
            )}
            <div className="text-xs text-gray-500 mt-2">
              Ce site est protégé par reCAPTCHA et les{" "}
              <a href="https://policies.google.com/privacy" target="_blank" rel="noopener noreferrer" className="text-purple-600 hover:underline">
                règles de confidentialité
              </a>{" "}
              et{" "}
              <a href="https://policies.google.com/terms" target="_blank" rel="noopener noreferrer" className="text-purple-600 hover:underline">
                conditions d'utilisation
              </a>{" "}
              de Google s'appliquent.
            </div>
          </div>
        )}
      </div>
    </>
  );
}

// Déclarer les types pour window
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
      render?: (
        element: HTMLElement,
        options: {
          sitekey: string;
          theme: string;
          callback: (token: string) => void;
          "expired-callback"?: () => void;
          "error-callback"?: () => void;
        }
      ) => number;
      reset?: (id: number) => void;
    };
    resetCaptcha?: () => void;
  }
}
