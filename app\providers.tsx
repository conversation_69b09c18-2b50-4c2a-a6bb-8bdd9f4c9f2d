"use client";

import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import { SiteSettingsProvider } from "@/hooks/use-site-settings";
import { GalleryVariantsProvider } from "@/hooks/use-gallery-variants";
import { PaymentMethodsProvider } from "@/hooks/use-payment-methods";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider attribute="class" defaultTheme="light">
      <SiteSettingsProvider>
        <GalleryVariantsProvider>
          <PaymentMethodsProvider>
            {children}
            <Toaster />
          </PaymentMethodsProvider>
        </GalleryVariantsProvider>
      </SiteSettingsProvider>
    </ThemeProvider>
  );
}
