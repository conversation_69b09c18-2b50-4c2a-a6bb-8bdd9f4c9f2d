"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { supabaseApi, CartItem, Product } from "@/lib/supabase";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/components/ui/use-toast";
import { v4 as uuidv4 } from 'uuid';

type CartContextType = {
  items: CartItem[];
  isLoading: boolean;
  addItem: (product: Product, quantity: number, customized?: boolean, customizationData?: any) => Promise<void>;
  updateItem: (id: string, quantity: number) => Promise<void>;
  removeItem: (id: string) => Promise<void>;
  clearCart: () => Promise<void>;
  subtotal: number;
  shippingCost: number;
  total: number;
};

const CartContext = createContext<CartContextType | undefined>(undefined);

// Coût de livraison par défaut (2000 FCFA)
const DEFAULT_SHIPPING_COST = 2000;
// Seuil pour la livraison gratuite (50000 FCFA)
const FREE_SHIPPING_THRESHOLD = 50000;

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  // Calculer le sous-total
  const subtotal = items.reduce((total, item) => {
    const price = item.product?.price || 0;
    return total + price * item.quantity;
  }, 0);

  // Déterminer le coût de livraison
  const shippingCost = subtotal >= FREE_SHIPPING_THRESHOLD ? 0 : DEFAULT_SHIPPING_COST;

  // Calculer le total
  const total = subtotal + shippingCost;

  // Charger les articles du panier depuis Supabase
  useEffect(() => {
    const loadCartItems = async () => {
      setIsLoading(true);
      try {
        console.log("useCart - loadCartItems - Début du chargement du panier");

        // Déterminer l'ID à utiliser (utilisateur connecté ou session anonyme)
        let userId;

        if (user) {
          userId = user.id;
          console.log("useCart - loadCartItems - Utilisateur connecté, ID:", userId);
        } else {
          // Récupérer l'ID de session anonyme du localStorage (côté client uniquement)
          userId = typeof window !== 'undefined' ? localStorage.getItem('anonymous_session_id') : null;
          console.log("useCart - loadCartItems - Utilisateur anonyme, ID de session:", userId);

          // Si aucun ID n'existe encore, créer un ID de session et un panier vide
          if (!userId) {
            userId = getSessionId(); // Générer un nouvel ID de session
            console.log("useCart - loadCartItems - Nouvel ID de session créé:", userId);
            setItems([]);
            setIsLoading(false);
            return;
          }
        }

        // Vérifier si l'ID utilisateur est valide
        if (!userId || userId === 'null' || userId === 'undefined') {
          console.warn("useCart - loadCartItems - ID utilisateur invalide:", userId);
          setItems([]);
          setIsLoading(false);
          return;
        }

        console.log("useCart - loadCartItems - Récupération des articles du panier pour l'utilisateur:", userId);
        const { data, error } = await supabaseApi.cart.getItems(userId);

        if (error) {
          console.error("useCart - loadCartItems - Erreur lors du chargement du panier:", error);

          // Afficher les détails de l'erreur pour le débogage
          if (error.details) console.error("Détails de l'erreur:", error.details);
          if ('hint' in error && error.hint) console.error("Indice:", error.hint);
          if ('code' in error && error.code) console.error("Code d'erreur:", error.code);

          // Essayer de récupérer les articles du localStorage en cas d'erreur de connexion
          const localItems = getLocalCartItems(userId);
          if (localItems && localItems.length > 0) {
            console.log("useCart - loadCartItems - Utilisation des articles du panier local:", localItems.length);
            setItems(localItems);
          } else {
            toast({
              title: "Erreur",
              description: "Impossible de charger votre panier.",
              variant: "destructive",
            });
            setItems([]);
          }
        } else {
          console.log("useCart - loadCartItems - Articles récupérés avec succès:", data?.length || 0);
          setItems(data || []);

          // Sauvegarder les articles dans le localStorage
          if (data && data.length > 0) {
            saveLocalCartItems(userId, data);
          }
        }
      } catch (error) {
        console.error("useCart - loadCartItems - Erreur inattendue:", error);

        // Essayer de récupérer les articles du localStorage en cas d'erreur
        try {
          const userId = user ? user.id : (typeof window !== 'undefined' ? localStorage.getItem('anonymous_session_id') : null);
          if (userId) {
            const localItems = getLocalCartItems(userId);
            if (localItems && localItems.length > 0) {
              console.log("useCart - loadCartItems - Utilisation des articles du panier local après erreur:", localItems.length);
              setItems(localItems);
            } else {
              setItems([]);
            }
          } else {
            setItems([]);
          }
        } catch (localError) {
          console.error("useCart - loadCartItems - Erreur lors de la récupération des articles locaux:", localError);
          setItems([]);
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadCartItems();
  }, [user, toast]);

  // Fonctions pour gérer le stockage local des articles du panier
  const saveLocalCartItems = (userId: string, items: CartItem[]) => {
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem(`cart_items_${userId}`, JSON.stringify(items));
      }
    } catch (error) {
      console.error("useCart - saveLocalCartItems - Erreur:", error);
    }
  };

  const getLocalCartItems = (userId: string): CartItem[] | null => {
    try {
      if (typeof window !== 'undefined') {
        const storedItems = localStorage.getItem(`cart_items_${userId}`);
        return storedItems ? JSON.parse(storedItems) : null;
      }
      return null;
    } catch (error) {
      console.error("useCart - getLocalCartItems - Erreur:", error);
      return null;
    }
  };

  // Générer un ID de session anonyme si l'utilisateur n'est pas connecté
  const getSessionId = () => {
    // Vérifier si nous sommes côté client
    if (typeof window === 'undefined') {
      return 'server-side-session';
    }

    // Vérifier si un ID de session existe déjà dans localStorage
    let sessionId = localStorage.getItem('anonymous_session_id');

    // Si aucun ID n'existe, en créer un nouveau
    if (!sessionId) {
      sessionId = `anonymous_${uuidv4()}`;
      localStorage.setItem('anonymous_session_id', sessionId);
    }

    return sessionId;
  };

  // Ajouter un article au panier
  const addItem = async (
    product: Product,
    quantity: number,
    customized: boolean = false,
    customizationData?: any
  ) => {
    try {
      // Déterminer l'ID à utiliser (utilisateur connecté ou session anonyme)
      const userId = user ? user.id : getSessionId();

      // Vérifier si le produit est déjà dans le panier
      const existingItem = items.find(
        (item) =>
          item.product_id === product.id &&
          item.customized === customized &&
          JSON.stringify(item.customization_data) === JSON.stringify(customizationData)
      );

      if (existingItem) {
        // Mettre à jour la quantité si le produit existe déjà
        await updateItem(existingItem.id, existingItem.quantity + quantity);
      } else {
        // Ajouter un nouvel article
        const { data, error } = await supabaseApi.cart.addItem({
          user_id: userId,
          product_id: product.id,
          quantity,
          customized,
          customization_data: customizationData,
        });

        if (error) {
          throw error;
        }

        if (data) {
          // Récupérer les détails du produit
          const { data: productData } = await supabaseApi.products.getById(product.id);

          // Ajouter l'article au state local
          setItems([...items, { ...data[0], product: productData }]);

          toast({
            title: "Produit ajouté",
            description: `${product.name} a été ajouté à votre panier.`,
          });
        }
      }
    } catch (error: any) {
      console.error("Erreur lors de l'ajout au panier:", error);
      toast({
        title: "Erreur",
        description: error.message || "Impossible d'ajouter ce produit au panier.",
        variant: "destructive",
      });
    }
  };

  // Mettre à jour la quantité d'un article
  const updateItem = async (id: string, quantity: number) => {
    if (quantity < 1) {
      await removeItem(id);
      return;
    }

    try {
      const { data, error } = await supabaseApi.cart.updateItem(id, { quantity });

      if (error) {
        throw error;
      }

      // Mettre à jour le state local
      setItems(
        items.map((item) => (item.id === id ? { ...item, quantity } : item))
      );
    } catch (error: any) {
      console.error("Erreur lors de la mise à jour du panier:", error);
      toast({
        title: "Erreur",
        description: error.message || "Impossible de mettre à jour ce produit.",
        variant: "destructive",
      });
    }
  };

  // Supprimer un article du panier
  const removeItem = async (id: string) => {
    try {
      const { error } = await supabaseApi.cart.removeItem(id);

      if (error) {
        throw error;
      }

      // Mettre à jour le state local
      setItems(items.filter((item) => item.id !== id));

      toast({
        title: "Produit supprimé",
        description: "Le produit a été retiré de votre panier.",
      });
    } catch (error: any) {
      console.error("Erreur lors de la suppression du panier:", error);
      toast({
        title: "Erreur",
        description: error.message || "Impossible de supprimer ce produit.",
        variant: "destructive",
      });
    }
  };

  // Vider le panier
  const clearCart = async () => {
    try {
      // Déterminer l'ID à utiliser (utilisateur connecté ou session anonyme)
      const userId = user ? user.id : (typeof window !== 'undefined' ? localStorage.getItem('anonymous_session_id') : null);

      if (!userId) return;

      const { error } = await supabaseApi.cart.clearCart(userId);

      if (error) {
        throw error;
      }

      // Mettre à jour le state local
      setItems([]);

      toast({
        title: "Panier vidé",
        description: "Votre panier a été vidé avec succès.",
      });
    } catch (error: any) {
      console.error("Erreur lors du vidage du panier:", error);
      toast({
        title: "Erreur",
        description: error.message || "Impossible de vider votre panier.",
        variant: "destructive",
      });
    }
  };

  return (
    <CartContext.Provider
      value={{
        items,
        isLoading,
        addItem,
        updateItem,
        removeItem,
        clearCart,
        subtotal,
        shippingCost,
        total,
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCart doit être utilisé à l'intérieur d'un CartProvider");
  }
  return context;
}
