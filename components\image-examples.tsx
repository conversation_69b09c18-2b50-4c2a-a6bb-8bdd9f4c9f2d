"use client"

import { useState } from 'react';
import { AdaptiveImage } from '@/components/ui/adaptive-image';
import { 
  getPhoneCaseImagePath, 
  getBannerImagePath, 
  getTestimonialImagePath,
  phoneCasePaths,
  bannerPaths,
  testimonialPaths
} from '@/lib/image-paths';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

/**
 * Composant d'exemple pour démontrer l'utilisation des images
 * Ce composant montre comment les images sont chargées à partir des dossiers
 * et comment elles peuvent être mises à jour facilement
 */
export function ImageExamples() {
  const [selectedBrand, setSelectedBrand] = useState('iphone');
  const [selectedModel, setSelectedModel] = useState('15ProMax');
  const [selectedBannerType, setSelectedBannerType] = useState('accueil');
  const [selectedTestimonialType, setSelectedTestimonialType] = useState('clients');

  // Obtenir le chemin de l'image de coque sélectionnée
  const phoneCaseImagePath = getPhoneCaseImagePath(selectedBrand, selectedModel);
  
  // Obtenir le chemin de la bannière sélectionnée
  const bannerImagePath = getBannerImagePath(selectedBannerType);
  
  // Obtenir le chemin du témoignage sélectionné
  const testimonialImagePath = getTestimonialImagePath(selectedTestimonialType);

  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold">Exemples d'utilisation des images</h2>
      <p className="text-gray-500">
        Ce composant montre comment les images sont chargées à partir des dossiers et comment elles peuvent être mises à jour facilement.
        Pour mettre à jour une image, il suffit de remplacer le fichier dans le dossier correspondant.
      </p>

      <Tabs defaultValue="phone-cases">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="phone-cases">Coques de téléphone</TabsTrigger>
          <TabsTrigger value="banners">Bannières</TabsTrigger>
          <TabsTrigger value="testimonials">Témoignages</TabsTrigger>
        </TabsList>

        {/* Onglet Coques de téléphone */}
        <TabsContent value="phone-cases" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Coques de téléphone</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone-brand">Marque</Label>
                    <Select
                      value={selectedBrand}
                      onValueChange={setSelectedBrand}
                    >
                      <SelectTrigger id="phone-brand">
                        <SelectValue placeholder="Sélectionner une marque" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="iphone">iPhone</SelectItem>
                        <SelectItem value="samsung">Samsung</SelectItem>
                        <SelectItem value="google">Google</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone-model">Modèle</Label>
                    <Select
                      value={selectedModel}
                      onValueChange={setSelectedModel}
                    >
                      <SelectTrigger id="phone-model">
                        <SelectValue placeholder="Sélectionner un modèle" />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedBrand === 'iphone' && (
                          <>
                            <SelectItem value="15ProMax">iPhone 15 Pro Max</SelectItem>
                            <SelectItem value="15Pro">iPhone 15 Pro</SelectItem>
                            <SelectItem value="15Plus">iPhone 15 Plus</SelectItem>
                            <SelectItem value="15">iPhone 15</SelectItem>
                            <SelectItem value="14ProMax">iPhone 14 Pro Max</SelectItem>
                            <SelectItem value="14Pro">iPhone 14 Pro</SelectItem>
                          </>
                        )}
                        {selectedBrand === 'samsung' && (
                          <>
                            <SelectItem value="S24Ultra">Galaxy S24 Ultra</SelectItem>
                            <SelectItem value="S24Plus">Galaxy S24 Plus</SelectItem>
                            <SelectItem value="S24">Galaxy S24</SelectItem>
                            <SelectItem value="S23Ultra">Galaxy S23 Ultra</SelectItem>
                          </>
                        )}
                        {selectedBrand === 'google' && (
                          <>
                            <SelectItem value="Pixel8Pro">Pixel 8 Pro</SelectItem>
                            <SelectItem value="Pixel8">Pixel 8</SelectItem>
                            <SelectItem value="Pixel7Pro">Pixel 7 Pro</SelectItem>
                          </>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="pt-4">
                    <p className="text-sm text-gray-500">
                      Chemin de l'image : <code className="bg-gray-100 p-1 rounded">{phoneCaseImagePath}</code>
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-center bg-gray-50 rounded-lg p-4">
                  <AdaptiveImage
                    src={phoneCaseImagePath}
                    alt={`Coque ${selectedBrand} ${selectedModel}`}
                    width={200}
                    height={400}
                    style={{ objectFit: "contain" }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Bannières */}
        <TabsContent value="banners" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bannières</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="banner-type">Type de bannière</Label>
                    <Select
                      value={selectedBannerType}
                      onValueChange={setSelectedBannerType}
                    >
                      <SelectTrigger id="banner-type">
                        <SelectValue placeholder="Sélectionner un type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="accueil">Accueil</SelectItem>
                        <SelectItem value="promotions">Promotions</SelectItem>
                        <SelectItem value="categories">Catégories</SelectItem>
                        <SelectItem value="produits">Produits</SelectItem>
                        <SelectItem value="evenements">Événements</SelectItem>
                        <SelectItem value="saisonniers">Saisonniers</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="pt-4">
                    <p className="text-sm text-gray-500">
                      Chemin de l'image : <code className="bg-gray-100 p-1 rounded">{bannerImagePath}</code>
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-center bg-gray-50 rounded-lg p-4">
                  <AdaptiveImage
                    src={bannerImagePath}
                    alt={`Bannière ${selectedBannerType}`}
                    width={300}
                    height={150}
                    style={{ objectFit: "contain" }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Témoignages */}
        <TabsContent value="testimonials" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Témoignages</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="testimonial-type">Type de témoignage</Label>
                    <Select
                      value={selectedTestimonialType}
                      onValueChange={setSelectedTestimonialType}
                    >
                      <SelectTrigger id="testimonial-type">
                        <SelectValue placeholder="Sélectionner un type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="clients">Clients</SelectItem>
                        <SelectItem value="entreprises">Entreprises</SelectItem>
                        <SelectItem value="influenceurs">Influenceurs</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="pt-4">
                    <p className="text-sm text-gray-500">
                      Chemin de l'image : <code className="bg-gray-100 p-1 rounded">{testimonialImagePath}</code>
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-center bg-gray-50 rounded-lg p-4">
                  <AdaptiveImage
                    src={testimonialImagePath}
                    alt={`Témoignage ${selectedTestimonialType}`}
                    width={200}
                    height={200}
                    style={{ objectFit: "contain" }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
