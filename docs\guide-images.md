# Guide pour résoudre les problèmes d'images sur le site HCP Design CI

Ce guide vous aidera à résoudre les problèmes courants liés aux images sur le site HCP Design CI.

## Problèmes courants

### 1. Les images ne s'affichent pas sur le site en ligne

Si les images s'affichent correctement en développement local mais pas sur le site en ligne, voici les causes possibles et leurs solutions :

#### Problème de chemin d'accès

**Symptôme** : Les images sont introuvables (erreur 404).

**Solutions** :
- Vérifiez que les chemins d'accès commencent par `/images/` et non par `images/`
- Vérifiez que la casse (majuscules/minuscules) des noms de fichiers correspond exactement
- Assurez-vous que les dossiers et sous-dossiers existent dans le dépôt Git

#### Problème de déploiement

**Symptôme** : Les images existent mais ne sont pas déployées.

**Solutions** :
- Assurez-vous que les dossiers d'images contiennent des fichiers `.gitkeep` pour être inclus dans Git
- Vérifiez que les images sont bien committées et poussées vers GitHub
- Exécutez le script de préparation avant le déploiement : `node scripts/prepare-for-deployment.js`

#### Problème de cache

**Symptôme** : Les anciennes versions des images persistent.

**Solutions** :
- Modifiez la configuration dans `next.config.js` pour désactiver le cache (`minimumCacheTTL: 0`)
- Ajoutez un paramètre de version aux URLs des images (ex: `/images/logo.png?v=2`)
- Demandez aux utilisateurs de vider leur cache de navigateur

### 2. Les images sont déformées ou de mauvaise qualité

**Symptôme** : Les images s'affichent mais sont étirées, compressées ou floues.

**Solutions** :
- Utilisez des images de dimensions appropriées pour chaque usage
- Désactivez l'optimisation automatique dans `next.config.js` (`unoptimized: true`)
- Utilisez des formats d'image modernes comme WebP ou AVIF pour une meilleure qualité

## Scripts utilitaires

Le projet inclut plusieurs scripts pour vous aider à gérer les images :

### 1. Vérification des images

```bash
node scripts/verify-images.js
```

Ce script vérifie que toutes les images référencées dans le code existent et crée des images de remplacement si nécessaire.

### 2. Préparation pour le déploiement

```bash
node scripts/prepare-for-deployment.js
```

Ce script prépare les images pour le déploiement en :
- Vérifiant que tous les dossiers d'images existent
- Créant des fichiers `.gitkeep` dans les dossiers vides
- Générant un rapport sur l'état des images

### 3. Correction des références d'images

```bash
node scripts/fix-image-references.js
```

Ce script corrige les références d'images dans le code en :
- Recherchant les références aux images manquantes
- Remplaçant les références incorrectes par des alternatives
- Corrigeant les chemins d'accès

## Structure des dossiers d'images

```
public/images/
├── banners/              # Bannières pour différentes sections du site
│   └── promotions/       # Bannières pour les promotions
├── brands/               # Logos des marques de téléphones
├── gallery/              # Images pour la galerie de designs
│   └── variants/         # Différentes variantes de designs
│       ├── vagues-abstraites/
│       ├── fleurs-tropicales/
│       └── ...
├── phone-models/         # Images des modèles de téléphones
├── promos/               # Images pour les promotions
│   ├── seasonal/         # Promotions saisonnières
│   ├── bundle/           # Promotions de packs
│   └── new/              # Promotions pour nouveaux clients
└── products/             # Images des produits
```

## Bonnes pratiques

1. **Nommage cohérent** : Utilisez des noms de fichiers descriptifs et cohérents
2. **Format approprié** : Utilisez PNG pour les images avec transparence, JPG pour les photos
3. **Dimensions optimales** : Utilisez des dimensions appropriées pour chaque usage
4. **Compression** : Compressez les images avant de les ajouter au dépôt
5. **Vérification avant déploiement** : Exécutez les scripts de vérification avant chaque déploiement

## Configuration de Next.js pour les images

La configuration optimale dans `next.config.js` pour les images est :

```javascript
images: {
  unoptimized: true, // Désactiver l'optimisation pour éviter les problèmes
  formats: ['image/avif', 'image/webp'],
  domains: ['placeholder.svg', 's3.amazonaws.com', 'localhost'],
  remotePatterns: [
    {
      protocol: 'https',
      hostname: '**',
    },
    {
      protocol: 'http',
      hostname: 'localhost',
    },
  ],
  minimumCacheTTL: 0, // Désactiver le cache pour le développement
  dangerouslyAllowSVG: true, // Permettre les SVG
  contentDispositionType: 'attachment', // Forcer le téléchargement des images
  contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;", // Sécurité pour les SVG
}
```

## Résolution des problèmes spécifiques

### Images de la galerie

Si les images de la galerie ne s'affichent pas :
1. Vérifiez que les dossiers de variantes existent dans `public/images/gallery/variants/`
2. Assurez-vous que chaque dossier de variante contient une image avec le même nom que le dossier
3. Vérifiez que les références dans le code utilisent le bon chemin

### Images des promotions

Si les images des promotions ne s'affichent pas :
1. Vérifiez que les dossiers `seasonal`, `bundle` et `new` existent dans `public/images/promos/`
2. Assurez-vous que chaque dossier contient les images nécessaires
3. Vérifiez que les bannières existent dans `public/images/banners/promotions/`

## Besoin d'aide supplémentaire ?

Si vous rencontrez toujours des problèmes avec les images après avoir suivi ce guide, contactez l'équipe de développement pour obtenir de l'aide.
