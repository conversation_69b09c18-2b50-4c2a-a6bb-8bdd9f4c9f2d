/**
 * Script pour préparer les images pour le déploiement
 * 
 * Ce script vérifie que toutes les images nécessaires existent,
 * crée des fichiers .gitkeep dans les dossiers vides,
 * et génère un rapport sur l'état des images.
 */

const fs = require('fs');
const path = require('path');

// Fonction pour vérifier si un fichier existe
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Fonction pour créer un répertoire s'il n'existe pas
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`Création du répertoire: ${dirPath}`);
    fs.mkdirSync(dirPath, { recursive: true });
    return true;
  }
  return false;
}

// Fonction pour créer un fichier .gitkeep dans un répertoire
function createGitKeep(dirPath) {
  const gitkeepPath = path.join(dirPath, '.gitkeep');
  if (!fs.existsSync(gitkeepPath)) {
    fs.writeFileSync(gitkeepPath, '');
    console.log(`Fichier .gitkeep créé dans: ${dirPath}`);
  }
}

// Fonction pour créer un fichier README.md dans un répertoire
function createReadme(dirPath, content) {
  const readmePath = path.join(dirPath, 'README.md');
  if (!fs.existsSync(readmePath)) {
    fs.writeFileSync(readmePath, content);
    console.log(`Fichier README.md créé dans: ${dirPath}`);
  }
}

// Fonction pour vérifier les dossiers d'images
function checkImageDirectories() {
  console.log("Vérification des dossiers d'images...");
  
  // Liste des dossiers principaux
  const mainDirs = [
    'banners',
    'brands',
    'gallery',
    'phone-models',
    'promos',
    'products'
  ];
  
  // Vérifier chaque dossier principal
  mainDirs.forEach(dir => {
    const dirPath = path.join(process.cwd(), 'public', 'images', dir);
    ensureDirectoryExists(dirPath);
    createGitKeep(dirPath);
    
    // Vérifier les sous-dossiers spécifiques
    if (dir === 'banners') {
      const promotionsBannersDir = path.join(dirPath, 'promotions');
      ensureDirectoryExists(promotionsBannersDir);
      createGitKeep(promotionsBannersDir);
    } else if (dir === 'gallery') {
      const variantsDir = path.join(dirPath, 'variants');
      ensureDirectoryExists(variantsDir);
      createGitKeep(variantsDir);
      
      // Liste des variantes
      const variants = [
        'vagues-abstraites',
        'fleurs-tropicales',
        'galaxie-cosmique',
        'marbre-elegant',
        'retro-synthwave',
        'montagnes-minimalistes',
        'motif-geometrique',
        'neon-urbain',
        'mandala-zen',
        'animaux-polygonaux',
        'typographie-creative'
      ];
      
      variants.forEach(variant => {
        const variantDir = path.join(variantsDir, variant);
        ensureDirectoryExists(variantDir);
        createGitKeep(variantDir);
      });
    } else if (dir === 'promos') {
      const subfolders = ['seasonal', 'bundle', 'new'];
      
      subfolders.forEach(folder => {
        const folderPath = path.join(dirPath, folder);
        ensureDirectoryExists(folderPath);
        createGitKeep(folderPath);
      });
    }
  });
}

// Fonction pour générer un rapport sur l'état des images
function generateImageReport() {
  console.log("Génération du rapport sur l'état des images...");
  
  const report = {
    totalDirectories: 0,
    totalImages: 0,
    missingImages: [],
    imagesByType: {}
  };
  
  // Fonction pour compter les images dans un répertoire
  function countImagesInDirectory(dirPath, relativePath = '') {
    if (!fs.existsSync(dirPath)) return;
    
    report.totalDirectories++;
    
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const itemRelativePath = path.join(relativePath, item);
      
      if (fs.statSync(itemPath).isDirectory()) {
        // Récursion pour les sous-répertoires
        countImagesInDirectory(itemPath, itemRelativePath);
      } else {
        // Vérifier si c'est une image
        const ext = path.extname(item).toLowerCase();
        if (['.png', '.jpg', '.jpeg', '.svg', '.gif', '.webp'].includes(ext)) {
          report.totalImages++;
          
          // Compter par type
          if (!report.imagesByType[ext]) {
            report.imagesByType[ext] = 0;
          }
          report.imagesByType[ext]++;
        }
      }
    });
  }
  
  // Compter les images dans le répertoire public/images
  countImagesInDirectory(path.join(process.cwd(), 'public', 'images'));
  
  // Générer le rapport
  const reportContent = `# Rapport sur l'état des images

## Résumé
- Total des répertoires: ${report.totalDirectories}
- Total des images: ${report.totalImages}

## Images par type
${Object.entries(report.imagesByType)
  .map(([ext, count]) => `- ${ext}: ${count}`)
  .join('\n')}

## Recommandations
1. Assurez-vous que toutes les images sont au format PNG pour une meilleure compatibilité
2. Vérifiez que chaque dossier contient un fichier .gitkeep
3. Exécutez le script \`prepare-for-deployment.js\` avant chaque déploiement
4. Vérifiez que les chemins d'accès aux images dans le code commencent par \`/images/\`

Rapport généré le ${new Date().toLocaleString()}
`;
  
  // Enregistrer le rapport
  const reportPath = path.join(process.cwd(), 'scripts', 'image-report.md');
  fs.writeFileSync(reportPath, reportContent);
  console.log(`Rapport enregistré dans: ${reportPath}`);
}

// Fonction principale
function main() {
  console.log("Début de la préparation pour le déploiement...");
  
  // Vérifier les dossiers d'images
  checkImageDirectories();
  
  // Générer un rapport sur l'état des images
  generateImageReport();
  
  console.log("Préparation pour le déploiement terminée.");
}

// Exécuter la fonction principale
main();
