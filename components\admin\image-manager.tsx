"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, Alert<PERSON><PERSON>ogHeader, <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { 
  Upload, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2, 
  Download, 
  Copy,
  Image as ImageIcon,
  FileImage,
  Calendar,
  HardDrive
} from "lucide-react";

interface ImageFile {
  id: string;
  name: string;
  url: string;
  alt: string;
  category: string;
  size: number;
  format: string;
  uploadDate: string;
  usedIn: string[];
  description?: string;
}

const mockImages: ImageFile[] = [
  {
    id: "1",
    name: "hero-banner.jpg",
    url: "/images/banners/hero-banner.jpg",
    alt: "Bannière principale",
    category: "banners",
    size: 245760,
    format: "JPEG",
    uploadDate: "2024-01-15",
    usedIn: ["Page d'accueil"],
    description: "Bannière principale de la page d'accueil"
  },
  {
    id: "2",
    name: "iphone-15-pro.png",
    url: "/images/phones/iphone-15-pro.png",
    alt: "iPhone 15 Pro",
    category: "phones",
    size: 156432,
    format: "PNG",
    uploadDate: "2024-01-14",
    usedIn: ["Galerie", "Produits"],
    description: "Image du modèle iPhone 15 Pro"
  },
  {
    id: "3",
    name: "design-floral.svg",
    url: "/images/designs/design-floral.svg",
    alt: "Design floral",
    category: "designs",
    size: 12456,
    format: "SVG",
    uploadDate: "2024-01-13",
    usedIn: ["Designs prédéfinis"],
    description: "Motif floral pour coques"
  },
  {
    id: "4",
    name: "logo-hcp.png",
    url: "/images/brand/logo-hcp.png",
    alt: "Logo HCP Design",
    category: "brand",
    size: 45123,
    format: "PNG",
    uploadDate: "2024-01-12",
    usedIn: ["Header", "Footer"],
    description: "Logo principal de la marque"
  },
  {
    id: "5",
    name: "promo-winter.jpg",
    url: "/images/promotions/promo-winter.jpg",
    alt: "Promotion hiver",
    category: "promotions",
    size: 198765,
    format: "JPEG",
    uploadDate: "2024-01-11",
    usedIn: ["Page promotions"],
    description: "Bannière pour la promotion d'hiver"
  }
];

const categories = [
  { value: "all", label: "Toutes les catégories" },
  { value: "banners", label: "Bannières" },
  { value: "phones", label: "Téléphones" },
  { value: "designs", label: "Designs" },
  { value: "brand", label: "Marque" },
  { value: "promotions", label: "Promotions" },
  { value: "gallery", label: "Galerie" },
  { value: "products", label: "Produits" }
];

const formats = [
  { value: "all", label: "Tous les formats" },
  { value: "JPEG", label: "JPEG" },
  { value: "PNG", label: "PNG" },
  { value: "SVG", label: "SVG" },
  { value: "WebP", label: "WebP" },
  { value: "GIF", label: "GIF" }
];

export function ImageManager() {
  const [images, setImages] = useState<ImageFile[]>(mockImages);
  const [filteredImages, setFilteredImages] = useState<ImageFile[]>(mockImages);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedFormat, setSelectedFormat] = useState("all");
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ImageFile | null>(null);
  const [newImage, setNewImage] = useState<Partial<ImageFile>>({
    name: "",
    alt: "",
    category: "gallery",
    description: ""
  });

  // Filtrer les images
  useEffect(() => {
    let filtered = images;

    if (searchTerm) {
      filtered = filtered.filter(image => 
        image.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        image.alt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        image.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory !== "all") {
      filtered = filtered.filter(image => image.category === selectedCategory);
    }

    if (selectedFormat !== "all") {
      filtered = filtered.filter(image => image.format === selectedFormat);
    }

    setFilteredImages(filtered);
  }, [images, searchTerm, selectedCategory, selectedFormat]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleUpload = () => {
    if (!newImage.name || !newImage.alt) {
      toast.error("Veuillez remplir tous les champs obligatoires");
      return;
    }

    const imageToAdd: ImageFile = {
      id: Date.now().toString(),
      name: newImage.name!,
      url: `/images/${newImage.category}/${newImage.name}`,
      alt: newImage.alt!,
      category: newImage.category!,
      size: Math.floor(Math.random() * 500000) + 10000,
      format: newImage.name!.split('.').pop()?.toUpperCase() || "JPEG",
      uploadDate: new Date().toISOString().split('T')[0],
      usedIn: [],
      description: newImage.description
    };

    setImages([...images, imageToAdd]);
    setNewImage({ name: "", alt: "", category: "gallery", description: "" });
    setIsUploadDialogOpen(false);
    toast.success("Image ajoutée avec succès");
  };

  const handleEdit = () => {
    if (!selectedImage) return;

    const updatedImages = images.map(img => 
      img.id === selectedImage.id ? selectedImage : img
    );
    setImages(updatedImages);
    setIsEditDialogOpen(false);
    setSelectedImage(null);
    toast.success("Image modifiée avec succès");
  };

  const handleDelete = (imageId: string) => {
    setImages(images.filter(img => img.id !== imageId));
    toast.success("Image supprimée avec succès");
  };

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    toast.success("URL copiée dans le presse-papiers");
  };

  const openPreview = (image: ImageFile) => {
    setSelectedImage(image);
    setIsPreviewDialogOpen(true);
  };

  const openEdit = (image: ImageFile) => {
    setSelectedImage({ ...image });
    setIsEditDialogOpen(true);
  };

  const totalSize = images.reduce((acc, img) => acc + img.size, 0);
  const totalImages = images.length;

  return (
    <div className="space-y-6">
      {/* Statistiques */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Images</CardTitle>
            <ImageIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalImages}</div>
            <p className="text-xs text-muted-foreground">
              {filteredImages.length} affichées
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Espace utilisé</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatFileSize(totalSize)}</div>
            <p className="text-xs text-muted-foreground">
              Stockage total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Catégories</CardTitle>
            <Filter className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length - 1}</div>
            <p className="text-xs text-muted-foreground">
              Types d'images
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Formats</CardTitle>
            <FileImage className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formats.length - 1}</div>
            <p className="text-xs text-muted-foreground">
              Types supportés
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Barre d'outils */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher des images..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Catégorie" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedFormat} onValueChange={setSelectedFormat}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Format" />
            </SelectTrigger>
            <SelectContent>
              {formats.map((format) => (
                <SelectItem key={format.value} value={format.value}>
                  {format.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Ajouter une image
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Ajouter une nouvelle image</DialogTitle>
              <DialogDescription>
                Téléchargez une nouvelle image pour votre site web.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Nom du fichier *</Label>
                <Input
                  id="name"
                  value={newImage.name}
                  onChange={(e) => setNewImage({ ...newImage, name: e.target.value })}
                  placeholder="ex: hero-banner.jpg"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="alt">Texte alternatif *</Label>
                <Input
                  id="alt"
                  value={newImage.alt}
                  onChange={(e) => setNewImage({ ...newImage, alt: e.target.value })}
                  placeholder="Description de l'image"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="category">Catégorie</Label>
                <Select 
                  value={newImage.category} 
                  onValueChange={(value) => setNewImage({ ...newImage, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.slice(1).map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newImage.description}
                  onChange={(e) => setNewImage({ ...newImage, description: e.target.value })}
                  placeholder="Description optionnelle"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleUpload}>
                Ajouter l'image
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Table des images */}
      <Card>
        <CardHeader>
          <CardTitle>Images ({filteredImages.length})</CardTitle>
          <CardDescription>
            Gérez toutes les images de votre site web
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Aperçu</TableHead>
                <TableHead>Nom</TableHead>
                <TableHead>Catégorie</TableHead>
                <TableHead>Format</TableHead>
                <TableHead>Taille</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Utilisée dans</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredImages.map((image) => (
                <TableRow key={image.id}>
                  <TableCell>
                    <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden">
                      <ImageIcon className="h-6 w-6 text-gray-400" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{image.name}</div>
                      <div className="text-sm text-muted-foreground">{image.alt}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {categories.find(c => c.value === image.category)?.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{image.format}</Badge>
                  </TableCell>
                  <TableCell>{formatFileSize(image.size)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {new Date(image.uploadDate).toLocaleDateString('fr-FR')}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {image.usedIn.slice(0, 2).map((usage, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {usage}
                        </Badge>
                      ))}
                      {image.usedIn.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{image.usedIn.length - 2}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openPreview(image)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEdit(image)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopyUrl(image.url)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                      >
                        <a href={image.url} download={image.name} title={`Télécharger ${image.name}`}>
                          <Download className="h-4 w-4" />
                        </a>
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Supprimer l'image</AlertDialogTitle>
                            <AlertDialogDescription>
                              Êtes-vous sûr de vouloir supprimer cette image ? Cette action est irréversible.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Annuler</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(image.id)}>
                              Supprimer
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Dialog de prévisualisation */}
      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Aperçu de l'image</DialogTitle>
          </DialogHeader>
          {selectedImage && (
            <div className="space-y-4">
              <div className="bg-gray-100 rounded-lg p-4 flex items-center justify-center min-h-[200px]">
                <ImageIcon className="h-16 w-16 text-gray-400" />
              </div>
              <div className="grid gap-2">
                <div><strong>Nom:</strong> {selectedImage.name}</div>
                <div><strong>Alt:</strong> {selectedImage.alt}</div>
                <div><strong>URL:</strong> <code className="text-sm bg-gray-100 px-1 rounded">{selectedImage.url}</code></div>
                <div><strong>Catégorie:</strong> {categories.find(c => c.value === selectedImage.category)?.label}</div>
                <div><strong>Format:</strong> {selectedImage.format}</div>
                <div><strong>Taille:</strong> {formatFileSize(selectedImage.size)}</div>
                <div><strong>Date:</strong> {new Date(selectedImage.uploadDate).toLocaleDateString('fr-FR')}</div>
                {selectedImage.description && (
                  <div><strong>Description:</strong> {selectedImage.description}</div>
                )}
                <div><strong>Utilisée dans:</strong> {selectedImage.usedIn.join(', ') || 'Aucune utilisation'}</div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Dialog d'édition */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Modifier l'image</DialogTitle>
            <DialogDescription>
              Modifiez les informations de cette image.
            </DialogDescription>
          </DialogHeader>
          {selectedImage && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Nom du fichier</Label>
                <Input
                  id="edit-name"
                  value={selectedImage.name}
                  onChange={(e) => setSelectedImage({ ...selectedImage, name: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-alt">Texte alternatif</Label>
                <Input
                  id="edit-alt"
                  value={selectedImage.alt}
                  onChange={(e) => setSelectedImage({ ...selectedImage, alt: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-category">Catégorie</Label>
                <Select 
                  value={selectedImage.category} 
                  onValueChange={(value) => setSelectedImage({ ...selectedImage, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.slice(1).map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={selectedImage.description || ""}
                  onChange={(e) => setSelectedImage({ ...selectedImage, description: e.target.value })}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button type="submit" onClick={handleEdit}>
              Sauvegarder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}