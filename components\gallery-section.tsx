"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Heart, Share2, Search, ArrowRight, MessageSquare, Eye, Award, Sparkles } from "lucide-react"
import Link from "next/link"
import PhoneModelSelector from "@/components/gallery/phone-model-selector"

// Interface pour les designs
interface Design {
  id: number;
  title: string;
  image: string;
  thumbnail: string;
  category: string;
  tags: string[];
  likes: number;
  views: number;
  comments: number;
  featured: boolean;
  trending: boolean;
  creator: {
    name: string;
    avatar: string;
    isVerified: boolean;
  };
  description: string;
  phoneModel: string;
  createdAt: string;
}

// Note: Les données de designs généraux sont maintenant chargées via l'API /api/designs

// Produits Gospel - Collection complète de produits personnalisés pour chrétiens
const gospelProducts = [
  // 📝 Papeterie personnalisée
  {
    id: 101,
    title: "Carnet de Prière Personnalisé",
    image: "/images/gallery/gospel/carnet-priere.png",
    thumbnail: "/images/gallery/gospel/carnet-priere.png",
    category: "gospel",
    tags: ["carnet", "prière", "journal", "foi", "papeterie"],
    likes: 167,
    views: 445,
    comments: 11,
    featured: true,
    trending: false,
    creator: {
      name: "Spiritual Stationery",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Carnet de prière personnalisé avec votre prénom. Idéal pour noter vos prières, réflexions et témoignages quotidiens.",
    phoneModel: "Papeterie",
    createdAt: "2023-09-30",
  },
  {
    id: 102,
    title: "Bloc-notes Versets Inspirants",
    image: "/images/gallery/gospel/bloc-notes-versets.png",
    thumbnail: "/images/gallery/gospel/bloc-notes-versets.png",
    category: "gospel",
    tags: ["bloc-notes", "versets", "inspiration", "papeterie"],
    likes: 134,
    views: 389,
    comments: 8,
    featured: false,
    trending: true,
    creator: {
      name: "Faith Design",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Bloc-notes avec versets bibliques inspirants sur chaque page. Parfait pour vos notes quotidiennes avec une touche spirituelle.",
    phoneModel: "Papeterie",
    createdAt: "2023-10-15",
  },
  // ☕ Accessoires de bureau et usage quotidien
  {
    id: 103,
    title: "Tasse Blanche 'Béni Soit l'Éternel'",
    image: "/images/gallery/gospel/tasse-blanche.png",
    thumbnail: "/images/gallery/gospel/tasse-blanche.png",
    category: "gospel",
    tags: ["tasse", "blanche", "béni", "éternel", "bureau"],
    likes: 89,
    views: 324,
    comments: 7,
    featured: false,
    trending: true,
    creator: {
      name: "Faith Design",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Tasse blanche élégante avec le message 'Béni Soit l'Éternel'. Parfait pour vos moments de méditation matinale.",
    phoneModel: "Accessoire",
    createdAt: "2023-10-10",
  },
  {
    id: 104,
    title: "Tasse Magique 'Révélation Divine'",
    image: "/images/gallery/gospel/tasse-magique.png",
    thumbnail: "/images/gallery/gospel/tasse-magique.png",
    category: "gospel",
    tags: ["tasse", "magique", "révélation", "divine", "surprise"],
    likes: 156,
    views: 542,
    comments: 12,
    featured: true,
    trending: false,
    creator: {
      name: "Miracle Mugs",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Tasse magique qui révèle un verset biblique quand vous versez une boisson chaude. Une surprise divine à chaque utilisation.",
    phoneModel: "Accessoire",
    createdAt: "2023-10-01",
  },
  {
    id: 105,
    title: "Stylo Standard 'Écris ta Foi'",
    image: "/images/gallery/gospel/stylo-standard.png",
    thumbnail: "/images/gallery/gospel/stylo-standard.png",
    category: "gospel",
    tags: ["stylo", "standard", "foi", "écriture", "bureau"],
    likes: 67,
    views: 234,
    comments: 5,
    featured: false,
    trending: false,
    creator: {
      name: "Office Faith",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: false,
    },
    description:
      "Stylo standard avec gravure 'Écris ta Foi'. Un outil d'écriture quotidien qui vous rappelle votre mission spirituelle.",
    phoneModel: "Papeterie",
    createdAt: "2023-10-08",
  },
  {
    id: 106,
    title: "Stylo VIP 'Plume de l'Esprit'",
    image: "/images/gallery/gospel/stylo-vip.png",
    thumbnail: "/images/gallery/gospel/stylo-vip.png",
    category: "gospel",
    tags: ["stylo", "vip", "plume", "esprit", "luxe"],
    likes: 198,
    views: 687,
    comments: 18,
    featured: true,
    trending: true,
    creator: {
      name: "Premium Faith",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Stylo VIP haut de gamme avec gravure dorée 'Plume de l'Esprit'. Élégance et spiritualité pour vos écrits importants.",
    phoneModel: "Papeterie",
    createdAt: "2023-09-25",
  },
  // 📢 Supports de communication visuelle
  {
    id: 107,
    title: "Kakémono Petite Base 'Évangélisation'",
    image: "/images/gallery/gospel/kakemono-petit.png",
    thumbnail: "/images/gallery/gospel/kakemono-petit.png",
    category: "gospel",
    tags: ["kakémono", "petite", "base", "évangélisation", "communication"],
    likes: 143,
    views: 456,
    comments: 9,
    featured: false,
    trending: false,
    creator: {
      name: "Visual Gospel",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: false,
    },
    description:
      "Kakémono compact pour vos événements d'évangélisation. Design attractif avec messages bibliques percutants.",
    phoneModel: "Communication",
    createdAt: "2023-09-20",
  },
  {
    id: 108,
    title: "Kakémono Grande Base 'Conférence Chrétienne'",
    image: "/images/gallery/gospel/kakemono-grand.png",
    thumbnail: "/images/gallery/gospel/kakemono-grand.png",
    category: "gospel",
    tags: ["kakémono", "grande", "base", "conférence", "chrétienne"],
    likes: 201,
    views: 612,
    comments: 13,
    featured: true,
    trending: false,
    creator: {
      name: "Event Gospel",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Grand kakémono professionnel pour conférences et événements chrétiens. Impact visuel maximal pour votre message.",
    phoneModel: "Communication",
    createdAt: "2023-09-28",
  },
  // 👕 Textiles personnalisés
  {
    id: 109,
    title: "T-shirt VIP 'Fille/Fils du Roi'",
    image: "/images/gallery/gospel/tshirt-vip.png",
    thumbnail: "/images/gallery/gospel/tshirt-vip.png",
    category: "gospel",
    tags: ["tshirt", "vip", "roi", "identité", "textile"],
    likes: 234,
    views: 789,
    comments: 15,
    featured: true,
    trending: true,
    creator: {
      name: "Kingdom Apparel",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "T-shirt VIP premium 'Fille/Fils du Roi' avec finitions de qualité supérieure. Affirmez votre identité royale en Christ.",
    phoneModel: "Vêtement",
    createdAt: "2023-10-05",
  },
  {
    id: 110,
    title: "T-shirt Standard 'Témoin de Christ'",
    image: "/images/gallery/gospel/tshirt-standard.png",
    thumbnail: "/images/gallery/gospel/tshirt-standard.png",
    category: "gospel",
    tags: ["tshirt", "standard", "témoin", "christ", "textile"],
    likes: 178,
    views: 523,
    comments: 11,
    featured: false,
    trending: true,
    creator: {
      name: "Witness Wear",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: false,
    },
    description:
      "T-shirt confortable avec message 'Témoin de Christ'. Design simple et efficace pour témoigner au quotidien.",
    phoneModel: "Vêtement",
    createdAt: "2023-10-12",
  },
  // 🔑 Accessoires
  {
    id: 111,
    title: "Porte-clé Plastique 'Croix de Vie'",
    image: "/images/gallery/gospel/porte-cle-plastique.png",
    thumbnail: "/images/gallery/gospel/porte-cle-plastique.png",
    category: "gospel",
    tags: ["porte-clé", "plastique", "croix", "vie", "accessoire"],
    likes: 92,
    views: 287,
    comments: 6,
    featured: false,
    trending: false,
    creator: {
      name: "Key Faith",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: false,
    },
    description:
      "Porte-clé en plastique résistant avec croix colorée. Un rappel quotidien de votre foi à portée de main.",
    phoneModel: "Accessoire",
    createdAt: "2023-10-18",
  },
  {
    id: 112,
    title: "Porte-clé Métallique 'Ichthus Doré'",
    image: "/images/gallery/gospel/porte-cle-metal.png",
    thumbnail: "/images/gallery/gospel/porte-cle-metal.png",
    category: "gospel",
    tags: ["porte-clé", "métallique", "ichthus", "doré", "premium"],
    likes: 145,
    views: 398,
    comments: 8,
    featured: true,
    trending: false,
    creator: {
      name: "Metal Faith",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Porte-clé métallique doré avec symbole Ichthus gravé. Élégance et durabilité pour ce symbole chrétien ancestral.",
    phoneModel: "Accessoire",
    createdAt: "2023-10-14",
  },
  // 🥤 Hydratation
  {
    id: 113,
    title: "Gourde 600ml 'Source de Vie'",
    image: "/images/gallery/gospel/gourde-600ml.png",
    thumbnail: "/images/gallery/gospel/gourde-600ml.png",
    category: "gospel",
    tags: ["gourde", "600ml", "source", "vie", "hydratation"],
    likes: 167,
    views: 445,
    comments: 11,
    featured: false,
    trending: true,
    creator: {
      name: "Living Water",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Gourde 600ml avec inscription 'Source de Vie' et verset Jean 4:14. Restez hydraté tout en méditant la Parole.",
    phoneModel: "Accessoire",
    createdAt: "2023-10-11",
  },
  {
    id: 114,
    title: "Gourde 700ml 'Eau Vive'",
    image: "/images/gallery/gospel/gourde-700ml.png",
    thumbnail: "/images/gallery/gospel/gourde-700ml.png",
    category: "gospel",
    tags: ["gourde", "700ml", "eau", "vive", "grande"],
    likes: 189,
    views: 567,
    comments: 14,
    featured: true,
    trending: false,
    creator: {
      name: "Living Water",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Grande gourde 700ml 'Eau Vive' avec design moderne. Capacité optimale pour vos journées actives dans la foi.",
    phoneModel: "Accessoire",
    createdAt: "2023-10-09",
  },
  // 🛋️ Décoration
  {
    id: 115,
    title: "Coussin VIP 30x30cm 'Paix du Seigneur'",
    image: "/images/gallery/gospel/coussin-vip-30.png",
    thumbnail: "/images/gallery/gospel/coussin-vip-30.png",
    category: "gospel",
    tags: ["coussin", "vip", "30x30", "paix", "seigneur"],
    likes: 156,
    views: 423,
    comments: 9,
    featured: false,
    trending: true,
    creator: {
      name: "Home Blessing",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Coussin VIP 30x30cm avec broderie 'Paix du Seigneur'. Confort et spiritualité pour votre intérieur.",
    phoneModel: "Décoration",
    createdAt: "2023-10-07",
  },
  {
    id: 116,
    title: "Coussin VIP 40x40cm 'Grâce et Vérité'",
    image: "/images/gallery/gospel/coussin-vip-40.png",
    thumbnail: "/images/gallery/gospel/coussin-vip-40.png",
    category: "gospel",
    tags: ["coussin", "vip", "40x40", "grâce", "vérité"],
    likes: 201,
    views: 612,
    comments: 13,
    featured: true,
    trending: false,
    creator: {
      name: "Home Blessing",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Grand coussin VIP 40x40cm 'Grâce et Vérité'. Élégance et message biblique pour transformer votre salon.",
    phoneModel: "Décoration",
    createdAt: "2023-09-28",
  },
  {
    id: 117,
    title: "Coussin Royal 30x30cm 'Roi des Rois'",
    image: "/images/gallery/gospel/coussin-royal-30.png",
    thumbnail: "/images/gallery/gospel/coussin-royal-30.png",
    category: "gospel",
    tags: ["coussin", "royal", "30x30", "roi", "rois"],
    likes: 178,
    views: 489,
    comments: 12,
    featured: true,
    trending: true,
    creator: {
      name: "Royal Decor",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Coussin Royal 30x30cm avec finitions dorées 'Roi des Rois'. Luxe et dévotion pour votre espace de prière.",
    phoneModel: "Décoration",
    createdAt: "2023-10-03",
  },
  {
    id: 118,
    title: "Coussin Royal 40x40cm 'Seigneur des Seigneurs'",
    image: "/images/gallery/gospel/coussin-royal-40.png",
    thumbnail: "/images/gallery/gospel/coussin-royal-40.png",
    category: "gospel",
    tags: ["coussin", "royal", "40x40", "seigneur", "luxe"],
    likes: 223,
    views: 678,
    comments: 16,
    featured: true,
    trending: false,
    creator: {
      name: "Royal Decor",
      avatar: "/placeholder.svg?height=100&width=100",
      isVerified: true,
    },
    description:
      "Grand coussin Royal 40x40cm 'Seigneur des Seigneurs' avec broderies dorées. Le summum du luxe spirituel.",
    phoneModel: "Décoration",
    createdAt: "2023-10-01",
  },
]



// Catégories supprimées - plus de filtrage par catégorie

export default function GallerySection() {
  const [searchTerm, setSearchTerm] = useState("")
  const [likedDesigns, setLikedDesigns] = useState<number[]>([])
  const [galleryDesigns, setGalleryDesigns] = useState<Design[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedDesign, setSelectedDesign] = useState<number | null>(null)
  const [activeTab, setActiveTab] = useState("gospel")

  // Fonction pour charger les designs depuis l'API
  const loadDesigns = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch('/api/designs')
      if (!response.ok) {
        throw new Error('Erreur lors du chargement des designs')
      }
      const data = await response.json()
      setGalleryDesigns(data)
    } catch (err) {
      console.error('Erreur lors du chargement des designs:', err)
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
      // En cas d'erreur, utiliser des données par défaut
      setGalleryDesigns([])
    } finally {
      setLoading(false)
    }
  }

  // Charger les designs au montage du composant
  useEffect(() => {
    loadDesigns()
  }, [])

  // Combiner tous les designs (API + données statiques Gospel)
  const allDesigns = [...galleryDesigns, ...gospelProducts]

  // Filtrer les designs
  const filteredDesigns = allDesigns.filter((design) => {
    const matchesSearch = design.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      design.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesSearch
  })

  // Limiter à 8 designs pour la page d'accueil
  const displayedDesigns = filteredDesigns.slice(0, 8)

  const toggleLike = (id: number, e?: React.MouseEvent) => {
    e?.stopPropagation()
    if (likedDesigns.includes(id)) {
      setLikedDesigns(likedDesigns.filter((designId) => designId !== id))
    } else {
      setLikedDesigns([...likedDesigns, id])
    }
  }

  const getSelectedDesign = () => {
    return allDesigns.find((design) => design.id === selectedDesign)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <div>
      {/* Search and Filter */}
      <div className="mb-8 space-y-4">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Rechercher un design..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Boutons de filtrage supprimés */}
      </div>

      {/* Tabs Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
        <TabsList className="grid w-full grid-cols-2 bg-gray-100 p-1 rounded-lg">
          <TabsTrigger 
            value="gospel" 
            className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=inactive]:bg-white data-[state=inactive]:text-purple-600 data-[state=inactive]:border data-[state=inactive]:border-purple-200 font-semibold py-3 px-6 rounded-md transition-all duration-200 hover:bg-purple-50 hover:text-purple-700"
          >
            🙏 Produits Gospel
          </TabsTrigger>
          <TabsTrigger 
            value="general" 
            className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=inactive]:bg-white data-[state=inactive]:text-blue-600 data-[state=inactive]:border data-[state=inactive]:border-blue-200 font-semibold py-3 px-6 rounded-md transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            🎨 Designs Généraux
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Loading et Error States */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des designs...</p>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-600">Erreur: {error}</p>
          <Button 
            onClick={loadDesigns} 
            variant="outline" 
            size="sm" 
            className="mt-2"
          >
            Réessayer
          </Button>
        </div>
      )}

      {/* Tab Content */}
      {!loading && activeTab === "gospel" && (
        <div className="mb-12">
          <div className="mb-6">
            <p className="text-gray-500">
              {allDesigns.filter(design => design.category === 'gospel').length} produit{allDesigns.filter(design => design.category === 'gospel').length !== 1 ? "s" : ""} Gospel
            </p>
          </div>
          {allDesigns.filter(design => design.category === 'gospel').length > 0 ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6">
              {allDesigns.filter(design => design.category === 'gospel').map((design) => (
                <Card
                  key={design.id}
                  className="overflow-hidden hover:shadow-lg transition-all cursor-pointer"
                  onClick={() => setSelectedDesign(design.id)}
                >
                  <CardContent className="p-0">
                    <div className="relative">
                      <img
                        src={design.thumbnail || "/placeholder.svg"}
                        alt={design.title}
                        className="w-full aspect-[3/4] object-cover"
                      />
                      {design.featured && (
                        <Badge className="absolute top-2 left-2 bg-purple-600">
                          <Award className="h-3 w-3 mr-1" /> À la une
                        </Badge>
                      )}
                      {design.trending && (
                        <Badge className="absolute top-2 left-2 bg-orange-500">
                          <Sparkles className="h-3 w-3 mr-1" /> Tendance
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="icon"
                        className={`absolute top-2 right-2 rounded-full bg-white/80 hover:bg-white ${
                          likedDesigns.includes(design.id) ? "text-red-500" : "text-gray-500"
                        }`}
                        onClick={(e) => toggleLike(design.id, e)}
                      >
                        <Heart
                          className="h-5 w-5"
                          fill={likedDesigns.includes(design.id) ? "currentColor" : "none"}
                        />
                      </Button>
                    </div>
                    <div className="p-3">
                      <h3 className="font-medium text-sm truncate">{design.title}</h3>
                      <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                        <div className="flex items-center">
                          <Heart className="h-3 w-3 mr-1" />
                          {design.likes}
                        </div>
                        <div className="flex items-center">
                          <Eye className="h-3 w-3 mr-1" />
                          {design.views}
                        </div>
                        <div className="flex items-center">
                          <MessageSquare className="h-3 w-3 mr-1" />
                          {design.comments}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <Search className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Aucun produit Gospel trouvé</p>
            </div>
          )}
        </div>
      )}

      {!loading && activeTab === "general" && (
        <div className="mb-12">
          <div className="mb-6">
            <p className="text-gray-500">
              {allDesigns.filter(design => design.category !== 'gospel').length} design{allDesigns.filter(design => design.category !== 'gospel').length !== 1 ? "s" : ""} généraux
            </p>
          </div>
          {allDesigns.filter(design => design.category !== 'gospel').length > 0 ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6">
              {allDesigns.filter(design => design.category !== 'gospel').slice(0, 8).map((design) => (
                <Card
                  key={design.id}
                  className="overflow-hidden hover:shadow-lg transition-all cursor-pointer"
                  onClick={() => setSelectedDesign(design.id)}
                >
                  <CardContent className="p-0">
                    <div className="relative">
                      <img
                        src={design.thumbnail || "/placeholder.svg"}
                        alt={design.title}
                        className="w-full aspect-[3/4] object-cover"
                      />
                      {design.featured && (
                        <Badge className="absolute top-2 left-2 bg-purple-600">
                          <Award className="h-3 w-3 mr-1" /> À la une
                        </Badge>
                      )}
                      {design.trending && (
                        <Badge className="absolute top-2 left-2 bg-orange-500">
                          <Sparkles className="h-3 w-3 mr-1" /> Tendance
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="icon"
                        className={`absolute top-2 right-2 rounded-full bg-white/80 hover:bg-white ${
                          likedDesigns.includes(design.id) ? "text-red-500" : "text-gray-500"
                        }`}
                        onClick={(e) => toggleLike(design.id, e)}
                      >
                        <Heart
                          className="h-5 w-5"
                          fill={likedDesigns.includes(design.id) ? "currentColor" : "none"}
                        />
                      </Button>
                    </div>
                    <div className="p-3">
                      <h3 className="font-medium text-sm truncate">{design.title}</h3>
                      <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                        <div className="flex items-center">
                          <Heart className="h-3 w-3 mr-1" />
                          {design.likes}
                        </div>
                        <div className="flex items-center">
                          <Eye className="h-3 w-3 mr-1" />
                          {design.views}
                        </div>
                        <div className="flex items-center">
                          <MessageSquare className="h-3 w-3 mr-1" />
                          {design.comments}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <Search className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Aucun design général trouvé</p>
            </div>
          )}
        </div>
      )}

      {/* Voir plus de designs */}
      <div className="text-center mt-8">
        <Link href="/customize">
          <Button size="lg" className="bg-purple-600 hover:bg-purple-700">
            Voir plus de designs <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </Link>
      </div>

      {/* Design Detail Dialog */}
      <Dialog open={selectedDesign !== null} onOpenChange={(open) => !open && setSelectedDesign(null)}>
        <DialogContent className="max-w-4xl">
          {getSelectedDesign() && (
            <>
              <DialogHeader>
                <DialogTitle>{getSelectedDesign()?.title}</DialogTitle>
                <DialogDescription>
                  Créé par {getSelectedDesign()?.creator.name} le {formatDate(getSelectedDesign()?.createdAt || "")}
                </DialogDescription>
              </DialogHeader>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                {/* Design Image */}
                <div className="relative">
                  <img
                    src={getSelectedDesign()?.image || "/placeholder.svg"}
                    alt={getSelectedDesign()?.title}
                    className="w-full rounded-lg object-cover aspect-[3/4]"
                  />
                </div>

                {/* Design Details */}
                <div className="space-y-6">
                  {/* Creator Info */}
                  <div className="flex items-center">
                    <Avatar className="h-10 w-10 mr-3">
                      <AvatarImage
                        src={getSelectedDesign()?.creator.avatar || "/placeholder.svg"}
                        alt={getSelectedDesign()?.creator.name}
                      />
                      <AvatarFallback>{getSelectedDesign()?.creator.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center">
                        <p className="font-medium">{getSelectedDesign()?.creator.name}</p>
                        {getSelectedDesign()?.creator.isVerified && (
                          <Badge className="ml-2 bg-blue-500">Vérifié</Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">Designer</p>
                    </div>
                  </div>

                  {/* Description */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Description</h4>
                    <p>{getSelectedDesign()?.description}</p>
                  </div>

                  {/* Tags */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {getSelectedDesign()?.tags.map((tag) => (
                        <Badge key={tag} variant="secondary">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 py-2">
                    <div className="text-center">
                      <div className="flex items-center justify-center text-purple-600">
                        <Heart className="h-5 w-5 mr-1" />
                        <span className="font-bold">{getSelectedDesign()?.likes}</span>
                      </div>
                      <p className="text-xs text-gray-500">J'aime</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center text-purple-600">
                        <Eye className="h-5 w-5 mr-1" />
                        <span className="font-bold">{getSelectedDesign()?.views}</span>
                      </div>
                      <p className="text-xs text-gray-500">Vues</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center text-purple-600">
                        <MessageSquare className="h-5 w-5 mr-1" />
                        <span className="font-bold">{getSelectedDesign()?.comments}</span>
                      </div>
                      <p className="text-xs text-gray-500">Commentaires</p>
                    </div>
                  </div>

                  {/* Sélecteur de modèle et ajout au panier */}
                  <PhoneModelSelector
                    designId={getSelectedDesign()?.id || 0}
                    designTitle={getSelectedDesign()?.title || ""}
                    designImage={getSelectedDesign()?.image || ""}
                  />

                  {/* Action Buttons */}
                  <div className="flex gap-4 pt-4">
                    <Button
                      variant="outline"
                      className={`flex-1 ${
                        likedDesigns.includes(getSelectedDesign()?.id || 0) ? "text-red-500" : ""
                      }`}
                      onClick={(e) => toggleLike(getSelectedDesign()?.id || 0, e)}
                    >
                      <Heart
                        className="h-5 w-5 mr-2"
                        fill={likedDesigns.includes(getSelectedDesign()?.id || 0) ? "currentColor" : "none"}
                      />
                      Ajouter aux favoris
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <Share2 className="h-5 w-5 mr-2" />
                      Partager
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}