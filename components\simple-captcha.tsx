"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RefreshCw } from "lucide-react";

interface SimpleCaptchaProps {
  onVerify: (isValid: boolean) => void;
}

/**
 * Composant de captcha simple qui ne dépend pas de services externes
 */
export function SimpleCaptcha({ onVerify }: SimpleCaptchaProps) {
  const [captchaText, setCaptchaText] = useState("");
  const [userInput, setUserInput] = useState("");
  const [isValid, setIsValid] = useState(false);

  // Générer un nouveau texte de captcha
  const generateCaptcha = () => {
    // Générer une chaîne aléatoire de 6 caractères (lettres et chiffres)
    const characters = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789";
    let result = "";
    for (let i = 0; i < 6; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    setCaptchaText(result);
    setUserInput("");
    setIsValid(false);
    onVerify(false);
  };

  // Vérifier l'entrée de l'utilisateur
  const checkCaptcha = (input: string) => {
    setUserInput(input);
    const valid = input === captchaText;
    setIsValid(valid);
    onVerify(valid);
  };

  // Générer le captcha au chargement du composant
  useEffect(() => {
    generateCaptcha();
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <Label htmlFor="captcha">Captcha de sécurité</Label>
        <div className="flex items-center space-x-2">
          <div 
            className="captcha-display"
          >
            {captchaText}
          </div>
          <Button 
            variant="outline" 
            size="icon" 
            onClick={generateCaptcha}
            type="button"
            aria-label="Générer un nouveau captcha"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="captcha-input">Saisissez le texte ci-dessus</Label>
        <Input
          id="captcha-input"
          type="text"
          value={userInput}
          onChange={(e) => checkCaptcha(e.target.value)}
          placeholder="Entrez le texte du captcha"
          className={isValid ? "border-green-500" : ""}
          autoComplete="off"
        />
      </div>
      
      {isValid && (
        <p className="text-xs text-green-600">Captcha validé ✓</p>
      )}
    </div>
  );
}

