import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

// Remove the Product type definition here if it already exists elsewhere
// import type { Product } from '@/types/product'; // Example if you have a types directory

export function useProducts(category?: string) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchProducts() {
      setLoading(true);
      try {
        const fetcher = async () => {
          let query = supabase.from('products').select('*');
          if (category) {
            query = query.eq('category', category);
          }
          const { data, error } = await query;
          if (error) throw error;
          return data || [];
        };
        const data = await fetcher();
        setProducts(data);
      } catch (err) {
        console.error("Erreur lors de la récupération des produits:", err);
        if (err instanceof Error) {
          setError(err.message);
        } else if (typeof err === 'object' && err !== null && 'message' in err) {
          setError((err as any).message);
        } else {
          setError('Unknown error');
        }
        setProducts([]);
      } finally {
        setLoading(false);
      }
    }
    fetchProducts();
  }, [category]);
  return { products, loading, error };
}

export type Product = {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  subcategory: string;
  image_url: string;
  is_new: boolean;
  is_bestseller: boolean;
  type: string;
  colors: string[];
  collections: string[];
  created_at: string;
};