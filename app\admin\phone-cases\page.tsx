"use client";

import { useState } from "react";
import { AdminLayout } from "@/components/admin/admin-layout";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Plus, Edit, Trash2, Upload, Smartphone } from "lucide-react";
import Image from "next/image";

// Types pour les coques et modèles de téléphone
interface PhoneModel {
  id: string;
  brand: string;
  name: string;
}

interface PhoneCase {
  id: string;
  name: string;
  category: string;
  basePrice: number;
  stock: number;
  modelId: string;
  imageUrl: string;
}

// Données d'exemple pour les modèles de téléphone
const phoneModels: PhoneModel[] = [
  { id: "iphone15pro", brand: "Apple", name: "iPhone 15 Pro" },
  { id: "iphone15", brand: "Apple", name: "iPhone 15" },
  { id: "iphone14pro", brand: "Apple", name: "iPhone 14 Pro" },
  { id: "iphone14", brand: "Apple", name: "iPhone 14" },
  { id: "samsungs23ultra", brand: "Samsung", name: "Galaxy S23 Ultra" },
  { id: "samsungs23", brand: "Samsung", name: "Galaxy S23" },
  { id: "pixel8pro", brand: "Google", name: "Pixel 8 Pro" },
  { id: "pixel8", brand: "Google", name: "Pixel 8" },
];

// Données d'exemple pour les coques
const initialCases: PhoneCase[] = [
  {
    id: "1",
    name: "Coque Transparente iPhone",
    category: "VIP-1",
    basePrice: 3500,
    stock: 50,
    modelId: "iphone15pro",
    imageUrl: "/images/placeholder.svg?v=1747269269097",
  },
  {
    id: "2",
    name: "Coque Premium Samsung",
    category: "Premium",
    basePrice: 4000,
    stock: 40,
    modelId: "samsungs23ultra",
    imageUrl: "/images/placeholder.svg?v=1747269269097",
  },
  {
    id: "3",
    name: "Coque Full Picture",
    category: "Fullpicture",
    basePrice: 4500,
    stock: 30,
    modelId: "iphone14pro",
    imageUrl: "/images/placeholder.svg?v=1747269269097",
  },
  {
    id: "4",
    name: "Coque MagSafe",
    category: "Vip Premium Magsafe",
    basePrice: 5000,
    stock: 20,
    modelId: "iphone15",
    imageUrl: "/images/placeholder.svg?v=1747269269097",
  },
];

// Catégories de coques
const caseCategories = [
  "Standard",
  "Premium",
  "VIP-1",
  "Fullpicture",
  "Vip Premium Magsafe",
  "Antichoc",
  "Écologique",
];

export default function PhoneCasesPage() {
  const [cases, setCases] = useState<PhoneCase[]>(initialCases);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedModel, setSelectedModel] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [editingCase, setEditingCase] = useState<PhoneCase | null>(null);
  
  // État pour le formulaire d'ajout/modification
  const [formData, setFormData] = useState<PhoneCase>({
    id: "",
    name: "",
    category: "",
    basePrice: 0,
    stock: 0,
    modelId: "",
    imageUrl: "/images/placeholder.svg?height=300&width=150",
  });

  // Filtrer les coques en fonction de la recherche, catégorie et modèle
  const filteredCases = cases.filter(phoneCase => {
    const matchesSearch = 
      phoneCase.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      phoneCase.category.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === "all" || phoneCase.category === selectedCategory;
    const matchesModel = selectedModel === "all" || phoneCase.modelId === selectedModel;
    
    return matchesSearch && matchesCategory && matchesModel;
  });

  // Gérer l'ouverture du formulaire d'édition
  const handleEditCase = (phoneCase: PhoneCase) => {
    setEditingCase(phoneCase);
    setFormData(phoneCase);
    setIsAddDialogOpen(true);
  };

  // Gérer la soumission du formulaire
  const handleSubmitForm = () => {
    if (editingCase) {
      // Mise à jour d'une coque existante
      setCases(cases.map(c => 
        c.id === editingCase.id ? formData : c
      ));
    } else {
      // Ajout d'une nouvelle coque
      const newCase: PhoneCase = {
        ...formData,
        id: Date.now().toString(), // Générer un ID unique
      };
      setCases([...cases, newCase]);
    }
    
    // Réinitialiser le formulaire et fermer le dialogue
    setFormData({
      id: "",
      name: "",
      category: "",
      basePrice: 0,
      stock: 0,
      modelId: "",
      imageUrl: "/images/placeholder.svg?height=300&width=150",
    });
    setEditingCase(null);
    setIsAddDialogOpen(false);
  };

  // Gérer la suppression d'une coque
  const handleDeleteCase = (id: string) => {
    setCases(cases.filter(c => c.id !== id));
  };

  // Gérer l'importation CSV
  const handleImportCSV = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Logique pour traiter le fichier CSV
      console.log("Importing file:", file.name);
      
      // Dans une application réelle, vous utiliseriez FileReader pour lire le contenu
      // et le traiter ligne par ligne
      
      // Fermer le dialogue après l'importation
      setIsImportDialogOpen(false);
    }
  };

  // Obtenir le nom du modèle à partir de l'ID
  const getModelName = (modelId: string) => {
    const model = phoneModels.find(m => m.id === modelId);
    return model ? `${model.brand} ${model.name}` : "Inconnu";
  };

  return (
    <AdminLayout>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Gestion des coques</h1>
        <div className="flex gap-2">
          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="mr-2 h-4 w-4" /> Importer CSV
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Importer des coques</DialogTitle>
                <DialogDescription>
                  Téléchargez un fichier CSV contenant les coques à importer.
                  Le fichier doit contenir les colonnes suivantes: id, nom, categorie, prix_base, stock, modele_id.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <Label htmlFor="csv-file">Fichier CSV</Label>
                <Input 
                  id="csv-file" 
                  type="file" 
                  accept=".csv" 
                  onChange={handleImportCSV}
                />
                <div className="text-xs text-muted-foreground">
                  <a href="#" className="text-primary underline">Télécharger un modèle de fichier CSV</a>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>Annuler</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une coque
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>{editingCase ? "Modifier une coque" : "Ajouter une coque"}</DialogTitle>
                <DialogDescription>
                  {editingCase 
                    ? "Modifiez les informations de la coque." 
                    : "Ajoutez une nouvelle coque au catalogue."}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nom de la coque</Label>
                  <Input 
                    id="name" 
                    value={formData.name} 
                    onChange={(e) => setFormData({...formData, name: e.target.value})} 
                    placeholder="ex: Coque Transparente iPhone"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Catégorie</Label>
                    <Select 
                      value={formData.category} 
                      onValueChange={(value) => setFormData({...formData, category: value})}
                    >
                      <SelectTrigger id="category">
                        <SelectValue placeholder="Sélectionner une catégorie" />
                      </SelectTrigger>
                      <SelectContent>
                        {caseCategories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="modelId">Modèle de téléphone</Label>
                    <Select 
                      value={formData.modelId} 
                      onValueChange={(value) => setFormData({...formData, modelId: value})}
                    >
                      <SelectTrigger id="modelId">
                        <SelectValue placeholder="Sélectionner un modèle" />
                      </SelectTrigger>
                      <SelectContent>
                        {phoneModels.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.brand} {model.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="basePrice">Prix de base (FCFA)</Label>
                    <Input 
                      id="basePrice" 
                      type="number" 
                      value={formData.basePrice} 
                      onChange={(e) => setFormData({...formData, basePrice: parseInt(e.target.value)})} 
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="stock">Stock</Label>
                    <Input 
                      id="stock" 
                      type="number" 
                      value={formData.stock} 
                      onChange={(e) => setFormData({...formData, stock: parseInt(e.target.value)})} 
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="imageUrl">Image (URL)</Label>
                  <Input 
                    id="imageUrl" 
                    value={formData.imageUrl} 
                    onChange={(e) => setFormData({...formData, imageUrl: e.target.value})} 
                  />
                </div>
                
                {formData.imageUrl && (
                  <div className="flex justify-center p-2 bg-gray-50 rounded-md">
                    <img 
                      src={formData.imageUrl} 
                      alt="Aperçu de la coque" 
                      className="h-40 object-contain"
                    />
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsAddDialogOpen(false);
                  setEditingCase(null);
                }}>Annuler</Button>
                <Button onClick={handleSubmitForm}>Enregistrer</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="grid gap-2 flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Rechercher une coque..." 
                  className="w-full pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full md:w-auto">
              <div className="w-full md:w-[180px]">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Toutes les catégories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les catégories</SelectItem>
                    {caseCategories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-[180px]">
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Tous les modèles" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les modèles</SelectItem>
                    {phoneModels.map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        {model.brand} {model.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Tabs defaultValue="grid" className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="grid">Grille</TabsTrigger>
            <TabsTrigger value="table">Tableau</TabsTrigger>
          </TabsList>
          <div className="text-sm text-muted-foreground">
            {filteredCases.length} coque(s) trouvée(s)
          </div>
        </div>
        
        <TabsContent value="grid" className="mt-0">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {filteredCases.map((phoneCase) => (
              <Card key={phoneCase.id} className="overflow-hidden">
                <div className="relative">
                  <div className="h-40 flex items-center justify-center p-4 bg-gray-50">
                    <img 
                      src={phoneCase.imageUrl} 
                      alt={phoneCase.name} 
                      className="h-full object-contain"
                    />
                  </div>
                  <div className="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                    {phoneCase.category}
                  </div>
                </div>
                <CardContent className="p-4">
                  <div className="font-bold mb-1 truncate" title={phoneCase.name}>
                    {phoneCase.name}
                  </div>
                  <div className="text-xs text-gray-500 mb-1">
                    {getModelName(phoneCase.modelId)}
                  </div>
                  <div className="text-sm font-medium mb-1">
                    {phoneCase.basePrice.toLocaleString()} FCFA
                  </div>
                  <div className="text-xs text-gray-500 mb-3">
                    Stock: {phoneCase.stock} unités
                  </div>
                  <div className="flex justify-end gap-2 mt-2">
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => handleEditCase(phoneCase)}
                    >
                      <Edit size={16} />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="text-destructive hover:text-destructive/80"
                      onClick={() => handleDeleteCase(phoneCase.id)}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="table" className="mt-0">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Image</TableHead>
                    <TableHead>Nom</TableHead>
                    <TableHead>Catégorie</TableHead>
                    <TableHead>Modèle</TableHead>
                    <TableHead>Prix (FCFA)</TableHead>
                    <TableHead>Stock</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCases.map((phoneCase) => (
                    <TableRow key={phoneCase.id}>
                      <TableCell className="!p-0">
                        <div className="!p-4">
                          <img 
                            src={phoneCase.imageUrl} 
                            alt={phoneCase.name} 
                            className="!h-20 !w-20 object-contain"
                          />
                        </div>
                      </TableCell>
                      <TableCell>{phoneCase.name}</TableCell>
                      <TableCell>{phoneCase.category}</TableCell>
                      <TableCell>{getModelName(phoneCase.modelId)}</TableCell>
                      <TableCell>{phoneCase.basePrice.toLocaleString()} FCFA</TableCell>
                      <TableCell>{phoneCase.stock} unités</TableCell>
                      <TableCell className="!p-0">
                        <div className="!p-4 flex justify-end gap-2">
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => handleEditCase(phoneCase)}
                          >
                            <Edit size={16} />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            className="text-destructive hover:text-destructive/80"
                            onClick={() => handleDeleteCase(phoneCase.id)}
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
}



