/* Dynamic styles for phone-preview component */

/* Container dimensions */
.phone-preview-dimensions {
  width: var(--phone-width, 280px);
  height: var(--phone-height, 580px);
}

/* Background color styles */
.phone-preview-background {
  background-color: var(--background-color, transparent);
}

/* Custom image transform styles */
.phone-preview-image-transform {
  transform: scale(var(--image-zoom, 1)) 
           rotate(var(--image-rotation, 0deg)) 
           translateX(var(--image-offset-x, 0%)) 
           translateY(var(--image-offset-y, 0%));
}

/* Custom text styles */
.phone-preview-text {
  color: var(--text-color, black);
  font-size: var(--text-size, 16px);
  transform: translateX(var(--text-pos-x, 0%))
             translateY(var(--text-pos-y, 0%))
             rotate(var(--text-rotation, 0deg));
}
