-- Schéma de base de données pour HCP-DESIGN CI

-- Activer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table des profils utilisateurs (extension de la table auth.users de Supabase)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  whatsapp TEXT NOT NULL UNIQUE,
  email TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger pour mettre à jour le timestamp updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON><PERSON> le trigger seulement s'il n'existe pas déjà
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_profiles_updated_at') THEN
    CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- Table des produits
CREATE TABLE IF NOT EXISTS public.products (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price INTEGER NOT NULL, -- Prix en centimes FCFA
  category TEXT NOT NULL, -- tasses, tshirts, tapis, coussins, portecles
  subcategory TEXT, -- Sous-catégorie spécifique
  image_url TEXT NOT NULL,
  is_new BOOLEAN DEFAULT FALSE,
  is_bestseller BOOLEAN DEFAULT FALSE,
  type TEXT, -- Type de produit (ex: Céramique, Coton, etc.)
  colors TEXT[] DEFAULT '{}', -- Tableau des couleurs disponibles
  collections TEXT[] DEFAULT '{}', -- Tableau des collections
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer le trigger seulement s'il n'existe pas déjà
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_products_updated_at') THEN
    CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON public.products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- Table des articles du panier
CREATE TABLE IF NOT EXISTS public.cart_items (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  customized BOOLEAN DEFAULT FALSE,
  customization_data JSONB DEFAULT NULL, -- Données de personnalisation (image, texte, etc.)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer le trigger seulement s'il n'existe pas déjà
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_cart_items_updated_at') THEN
    CREATE TRIGGER update_cart_items_updated_at
    BEFORE UPDATE ON public.cart_items
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- Table des commandes
CREATE TABLE IF NOT EXISTS public.orders (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  status TEXT NOT NULL DEFAULT 'pending', -- pending, processing, shipped, delivered, cancelled
  total INTEGER NOT NULL, -- Total en centimes FCFA
  shipping_address TEXT NOT NULL,
  shipping_city TEXT NOT NULL,
  shipping_notes TEXT,
  payment_method TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer le trigger seulement s'il n'existe pas déjà
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_orders_updated_at') THEN
    CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON public.orders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- Table des articles de commande
CREATE TABLE IF NOT EXISTS public.order_items (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  order_id UUID REFERENCES public.orders(id) ON DELETE CASCADE NOT NULL,
  product_id UUID REFERENCES public.products(id) ON DELETE SET NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  price INTEGER NOT NULL, -- Prix unitaire au moment de la commande
  customized BOOLEAN DEFAULT FALSE,
  customization_data JSONB DEFAULT NULL, -- Données de personnalisation
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des modèles de téléphone
CREATE TABLE IF NOT EXISTS public.phone_models (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  brand TEXT NOT NULL,
  name TEXT NOT NULL,
  width INTEGER NOT NULL, -- Largeur en mm
  height INTEGER NOT NULL, -- Hauteur en mm
  image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(brand, name)
);

-- Créer le trigger seulement s'il n'existe pas déjà
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_phone_models_updated_at') THEN
    CREATE TRIGGER update_phone_models_updated_at
    BEFORE UPDATE ON public.phone_models
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- Table des coques de téléphone
CREATE TABLE IF NOT EXISTS public.phone_cases (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  model_id UUID REFERENCES public.phone_models(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  image_url TEXT NOT NULL,
  price INTEGER NOT NULL, -- Prix en centimes FCFA
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer le trigger seulement s'il n'existe pas déjà
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_phone_cases_updated_at') THEN
    CREATE TRIGGER update_phone_cases_updated_at
    BEFORE UPDATE ON public.phone_cases
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- Table des administrateurs
CREATE TABLE IF NOT EXISTS public.admins (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Politiques de sécurité Row Level Security (RLS)

-- Activer RLS sur toutes les tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.phone_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.phone_cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admins ENABLE ROW LEVEL SECURITY;

-- Politiques pour profiles
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs peuvent voir leur propre profil') THEN
    CREATE POLICY "Les utilisateurs peuvent voir leur propre profil"
    ON public.profiles FOR SELECT
    USING (auth.uid() = id);
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs peuvent mettre à jour leur propre profil') THEN
    CREATE POLICY "Les utilisateurs peuvent mettre à jour leur propre profil"
    ON public.profiles FOR UPDATE
    USING (auth.uid() = id);
  END IF;
END
$$;

-- Politiques pour products (accessibles par tous)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Tout le monde peut voir les produits') THEN
    CREATE POLICY "Tout le monde peut voir les produits"
    ON public.products FOR SELECT
    USING (true);
  END IF;
END
$$;

-- Politiques pour cart_items
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs peuvent voir leurs propres articles du panier') THEN
    CREATE POLICY "Les utilisateurs peuvent voir leurs propres articles du panier"
    ON public.cart_items FOR SELECT
    USING (auth.uid() = user_id);
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs peuvent ajouter des articles à leur panier') THEN
    CREATE POLICY "Les utilisateurs peuvent ajouter des articles à leur panier"
    ON public.cart_items FOR INSERT
    WITH CHECK (auth.uid() = user_id);
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs peuvent mettre à jour leurs propres articles du panier') THEN
    CREATE POLICY "Les utilisateurs peuvent mettre à jour leurs propres articles du panier"
    ON public.cart_items FOR UPDATE
    USING (auth.uid() = user_id);
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs peuvent supprimer leurs propres articles du panier') THEN
    CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres articles du panier"
    ON public.cart_items FOR DELETE
    USING (auth.uid() = user_id);
  END IF;
END
$$;

-- Politiques pour orders
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs peuvent voir leurs propres commandes') THEN
    CREATE POLICY "Les utilisateurs peuvent voir leurs propres commandes"
    ON public.orders FOR SELECT
    USING (auth.uid() = user_id);
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs peuvent créer des commandes') THEN
    CREATE POLICY "Les utilisateurs peuvent créer des commandes"
    ON public.orders FOR INSERT
    WITH CHECK (auth.uid() = user_id);
  END IF;
END
$$;

-- Politiques pour order_items (via la relation avec orders)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les utilisateurs peuvent voir les articles de leurs propres commandes') THEN
    CREATE POLICY "Les utilisateurs peuvent voir les articles de leurs propres commandes"
    ON public.order_items FOR SELECT
    USING (
      EXISTS (
        SELECT 1 FROM public.orders
        WHERE orders.id = order_items.order_id
        AND orders.user_id = auth.uid()
      )
    );
  END IF;
END
$$;

-- Politiques pour phone_models (accessibles par tous)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Tout le monde peut voir les modèles de téléphone') THEN
    CREATE POLICY "Tout le monde peut voir les modèles de téléphone"
    ON public.phone_models FOR SELECT
    USING (true);
  END IF;
END
$$;

-- Politiques pour phone_cases (accessibles par tous)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Tout le monde peut voir les coques de téléphone') THEN
    CREATE POLICY "Tout le monde peut voir les coques de téléphone"
    ON public.phone_cases FOR SELECT
    USING (true);
  END IF;
END
$$;

-- Politiques pour admins
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les admins peuvent s''insérer eux-mêmes') THEN
    CREATE POLICY "Les admins peuvent s''insérer eux-mêmes"
    ON public.admins FOR INSERT
    WITH CHECK (auth.uid() = user_id);
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Les admins peuvent supprimer leur propre entrée') THEN
    CREATE POLICY "Les admins peuvent supprimer leur propre entrée"
    ON public.admins FOR DELETE
    USING (auth.uid() = user_id);
  END IF;
END
$$;

-- Données initiales pour les produits
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM public.products WHERE name = 'Tasse Personnalisable Classique') THEN
    INSERT INTO public.products (name, description, price, category, subcategory, image_url, is_new, is_bestseller, type, colors, collections)
    VALUES
      ('Tasse Personnalisable Classique', 'Tasse en céramique personnalisable avec votre design préféré', 7990, 'tasses', 'mugs', '/placeholder.svg?height=400&width=400', true, true, 'Céramique', ARRAY['Blanc', 'Noir'], ARRAY['Classique']),
      ('Tasse Magique Thermosensible', 'Tasse qui révèle votre design lorsqu''elle est chaude', 9990, 'tasses', 'mugs', '/placeholder.svg?height=400&width=400', true, false, 'Thermosensible', ARRAY['Noir', 'Bleu'], ARRAY['Premium']),
      ('Tasse Isotherme Inox', 'Gardez vos boissons chaudes ou froides pendant des heures', 12990, 'tasses', 'mugs', '/placeholder.svg?height=400&width=400', false, true, 'Isotherme', ARRAY['Argent', 'Noir', 'Blanc'], ARRAY['Premium']),

      ('T-shirt Personnalisable Coton', 'T-shirt 100% coton avec impression de haute qualité', 8990, 'tshirts', 'tshirts', '/placeholder.svg?height=400&width=400', false, true, 'Coton', ARRAY['Blanc', 'Noir', 'Gris', 'Bleu'], ARRAY['Basique']),
      ('T-shirt Premium Impression Totale', 'T-shirt avec impression sur toute la surface', 12990, 'tshirts', 'tshirts', '/placeholder.svg?height=400&width=400', true, false, 'Premium', ARRAY['Blanc'], ARRAY['Premium']),
      ('Polo Brodé Personnalisable', 'Polo élégant avec votre logo brodé', 14990, 'tshirts', 'tshirts', '/placeholder.svg?height=400&width=400', false, false, 'Polo', ARRAY['Blanc', 'Noir', 'Bleu marine'], ARRAY['Business']),

      ('Tapis de Souris Standard', 'Tapis de souris personnalisable avec surface lisse', 4990, 'tapis', 'mousepads', '/placeholder.svg?height=400&width=400', false, true, 'Standard', ARRAY['Noir'], ARRAY['Basique']),
      ('Tapis de Souris XXL Gamer', 'Grand tapis de souris pour les gamers', 9990, 'tapis', 'mousepads', '/placeholder.svg?height=400&width=400', true, true, 'XXL', ARRAY['Noir', 'Rouge'], ARRAY['Gaming']),
      ('Tapis de Souris avec Repose-Poignet', 'Tapis de souris ergonomique avec support pour le poignet', 7990, 'tapis', 'mousepads', '/placeholder.svg?height=400&width=400', false, false, 'Ergonomique', ARRAY['Noir', 'Bleu'], ARRAY['Confort']),

      ('Coussin Décoratif Personnalisable', 'Coussin décoratif pour votre intérieur', 11990, 'coussins', 'cushions', '/placeholder.svg?height=400&width=400', true, false, 'Décoratif', ARRAY['Blanc', 'Beige', 'Gris'], ARRAY['Maison']),
      ('Coussin Photo Recto-Verso', 'Coussin avec impression de vos photos des deux côtés', 14990, 'coussins', 'cushions', '/placeholder.svg?height=400&width=400', true, true, 'Photo', ARRAY['Blanc'], ARRAY['Premium']),
      ('Coussin de Sol XXL', 'Grand coussin de sol confortable', 19990, 'coussins', 'cushions', '/placeholder.svg?height=400&width=400', false, false, 'XXL', ARRAY['Gris', 'Bleu', 'Beige'], ARRAY['Confort']),

      ('Porte-clé Photo Personnalisable', 'Porte-clé avec votre photo préférée', 3990, 'portecles', 'keychains', '/placeholder.svg?height=400&width=400', false, true, 'Photo', ARRAY['Transparent'], ARRAY['Basique']),
      ('Porte-clé Métal Gravé', 'Porte-clé en métal avec gravure personnalisée', 5990, 'portecles', 'keychains', '/placeholder.svg?height=400&width=400', true, false, 'Métal', ARRAY['Argent', 'Or'], ARRAY['Premium']),
      ('Porte-clé Multifonction', 'Porte-clé avec outils intégrés', 6990, 'portecles', 'keychains', '/placeholder.svg?height=400&width=400', false, true, 'Multifonction', ARRAY['Noir', 'Rouge', 'Bleu'], ARRAY['Pratique']);
  END IF;
END
$$;

-- Données initiales pour les modèles de téléphone
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM public.phone_models WHERE brand = 'Apple' AND name = 'iPhone 15 Pro') THEN
    INSERT INTO public.phone_models (brand, name, width, height, image_url)
    VALUES
      ('Apple', 'iPhone 15 Pro', 71, 147, '/images/phone-models/iphone15pro.png'),
      ('Apple', 'iPhone 14', 72, 146, '/images/phone-models/iphone14.png'),
      ('Apple', 'iPhone 13', 72, 146, '/images/phone-models/iphone13.png'),
      ('Samsung', 'Galaxy S23', 70, 146, '/images/phone-models/galaxys23.png'),
      ('Samsung', 'Galaxy S22', 71, 146, '/images/phone-models/galaxys22.png'),
      ('Xiaomi', 'Redmi Note 12', 76, 165, '/images/phone-models/redminote12.png'),
      ('Google', 'Pixel 7', 73, 155, '/images/phone-models/pixel7.png');
  END IF;
END
$$;

-- Données initiales pour les coques de téléphone
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM public.phone_cases WHERE name = 'Coque Transparente' LIMIT 1) THEN
    INSERT INTO public.phone_cases (model_id, name, image_url, price)
    SELECT
      id,
      'Coque Transparente',
      '/images/phone-cases/transparent.png',
      4990
    FROM public.phone_models;
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM public.phone_cases WHERE name = 'Coque Premium' LIMIT 1) THEN
    INSERT INTO public.phone_cases (model_id, name, image_url, price)
    SELECT
      id,
      'Coque Premium',
      '/images/phone-cases/premium.png',
      7990
    FROM public.phone_models;
  END IF;
END
$$;
