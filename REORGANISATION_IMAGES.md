# Guide de Réorganisation des Dossiers d'Images

## Objectif
Réorganiser la structure des dossiers d'images pour qu'ils soient organisés par page, comme demandé : `C:\Users\<USER>\OneDrive\Documents\GitHub\v0-site-hcp-designci\public\images\accueil\banniere`

## Structure Actuelle (Problématique)
- Doublons : `banners/` général + dossiers spécifiques
- Incohérences : `Cadeaux-et-Gadgets/` + `cadeaux/` + `🎁 Cadeaux & Gadgets/`
- Noms mixtes : `Tous-les-produits/` + `products/` + `produits/`
- Dossiers génériques non organisés par page

## Nouvelle Structure Recommandée

### Pages Principales
```
public/images/
├── accueil/                    # Page d'accueil (app/page.tsx)
│   ├── banniere/
│   ├── designs-populaires/
│   └── carousel/
│
├── about/                      # Page À propos (app/about/)
│   ├── banniere/
│   └── equipe/
│
├── contact/                    # Page Contact (app/contact/)
│   ├── banniere/
│   └── formulaire/
│
├── gallery/                    # Page Galerie (app/gallery/)
│   ├── banniere/
│   ├── collections/
│   │   ├── animaux-polygonaux/
│   │   ├── fleurs-tropicales/
│   │   ├── galaxie-cosmique/
│   │   ├── gospel/
│   │   ├── mandala-zen/
│   │   ├── marbre-elegant/
│   │   ├── montagnes-minimalistes/
│   │   ├── motif-geometrique/
│   │   ├── neon-urbain/
│   │   ├── retro-synthwave/
│   │   ├── typographie-creative/
│   │   └── vagues-abstraites/
│   └── variants/
│
├── models/                     # Page Modèles (app/models/)
│   ├── banniere/
│   ├── google/
│   ├── iphone/
│   └── samsung/
│
├── shop/                       # Page Boutique (app/shop/)
│   ├── banniere/
│   ├── accessoires/
│   ├── coques/
│   └── nouveautes/
│
├── products/                   # Pages Produits (app/products/)
│   ├── banniere/
│   └── details/
│
├── gifts/                      # Page Cadeaux (app/gifts/)
│   ├── banniere/
│   ├── evenements/
│   ├── gadgets/
│   ├── packs/
│   │   ├── anniversary-pack/
│   │   ├── baby-pack/
│   │   ├── corporate-pack/
│   │   ├── dinner-pack/
│   │   ├── graduation-pack/
│   │   └── wedding-pack/
│   └── placeholders/
│
├── promos/                     # Page Promotions (app/promos/)
│   ├── banniere/
│   ├── nouveaux-clients/
│   ├── packs/
│   └── saisonniere/
│
├── customize/                  # Page Personnaliser (app/customize/)
│   ├── banniere/
│   ├── modeles/
│   └── templates/
│
├── account/                    # Pages Compte (app/account/)
│   ├── banniere/
│   ├── designs/
│   ├── favorites/
│   ├── orders/
│   └── profile/
│
├── cart/                       # Page Panier (app/cart/)
│   ├── banniere/
│   └── icons/
│
├── checkout/                   # Page Commande (app/checkout/)
│   ├── banniere/
│   └── confirmation/
│
└── shared/                     # Éléments partagés
    ├── brands/                 # Logos marques (Apple, Samsung, etc.)
    ├── icons/                  # Icônes générales
    ├── logos/                  # Logos du site
    ├── payments/               # Méthodes de paiement
    ├── phone-models/           # Modèles de téléphones
    ├── phone-cases/            # Types de coques
    └── placeholders/           # Images par défaut
```

## Actions à Effectuer

### 1. Renommage des Dossiers
- `A-propos/` → `about/`
- `Contact/` → `contact/`
- `Modeles/` → `models/`
- `Tous-les-produits/` → `shop/`
- `personnaliser/` → `customize/`
- `Cadeaux-et-Gadgets/` → `gifts/`
- `Promotions_old/` → `promos/`

### 2. Consolidation des Doublons
- Fusionner `cadeaux/`, `🎁 Cadeaux & Gadgets/` dans `gifts/`
- Fusionner `products/`, `produits/` dans `shop/` ou `products/`
- Déplacer contenu de `banners/` vers les pages correspondantes
- Regrouper `designs/` dans les pages qui les utilisent

### 3. Création du Dossier Shared
- Déplacer `brands/`, `icons/`, `logos/`, `payments/`, `phone-models/` vers `shared/`
- Créer `shared/placeholders/` pour les images par défaut

### 4. Mise à Jour du Code
Après réorganisation, mettre à jour les références dans :
- `app/page.tsx` (bannières accueil)
- `components/media-banner.tsx`
- Tous les composants utilisant des images
- Fichiers de configuration

## Avantages de Cette Structure

1. **Clarté** : Chaque page a ses propres ressources
2. **Maintenance** : Plus facile de trouver et gérer les images
3. **Évolutivité** : Ajout facile de nouvelles pages
4. **Cohérence** : Structure uniforme pour toutes les pages
5. **Performance** : Chargement optimisé par page

## Exemple Concret

Pour la page d'accueil :
```
public/images/accueil/
├── banniere/
│   ├── banner-accueil-principal.jpg
│   ├── banner-promo-noel.jpg
│   └── banner-nouveautes.jpg
├── designs-populaires/
│   ├── design-1.png
│   ├── design-2.png
│   └── design-3.png
└── carousel/
    ├── slide-1.jpg
    ├── slide-2.jpg
    └── slide-3.jpg
```

Utilisation dans le code :
```typescript
const bannerMediaItems = [
  {
    type: 'image' as const,
    src: '/images/accueil/banniere/banner-accueil-principal.jpg',
    alt: 'Bannière principale'
  },
  // ...
];
```

## Prochaines Étapes

1. Valider cette structure avec l'équipe
2. Effectuer la réorganisation par étapes
3. Tester les références d'images
4. Mettre à jour la documentation
5. Déployer les changements