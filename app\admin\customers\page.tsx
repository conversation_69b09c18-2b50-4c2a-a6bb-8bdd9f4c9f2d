import { <PERSON>ada<PERSON> } from "next";
import { AdminLayout } from "@/components/admin/admin-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Search, Plus, ShoppingBag, Edit, Trash2, Eye } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Gestion des clients | Admin HCP-DESIGN CI",
  description: "Gérez les informations des clients",
};

export default function CustomersPage() {
  // Données fictives pour la démo
  const customers = [
    { 
      id: 1, 
      name: "<PERSON>", 
      email: "<EMAIL>", 
      phone: "+225 07 12 34 56 78", 
      orders: 5, 
      lastOrder: "15/05/2023",
      notes: "C<PERSON> fid<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>pp"
    },
    { 
      id: 2, 
      name: "<PERSON>", 
      email: "<EMAIL>", 
      phone: "+225 05 98 76 54 32", 
      orders: 2, 
      lastOrder: "14/05/2023",
      notes: ""
    },
    { 
      id: 3, 
      name: "Paul Durand", 
      email: "<EMAIL>", 
      phone: "+225 01 23 45 67 89", 
      orders: 8, 
      lastOrder: "13/05/2023",
      notes: "Client VIP, livraison express"
    },
    { 
      id: 4, 
      name: "Sophie Dubois", 
      email: "<EMAIL>", 
      phone: "+225 07 89 67 45 23", 
      orders: 1, 
      lastOrder: "12/05/2023",
      notes: "Nouveau client, contactez-le"
    },
    { 
      id: 5, 
      name: "Lucas Moreau", 
      email: "<EMAIL>", 
      phone: "+225 05 43 21 67 89", 
      orders: 3, 
      lastOrder: "11/05/2023",
      notes: "Client régulier, préfère paiement par carte"
    },
  ];

  return (
    <AdminLayout>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Gestion des clients</h1>
        <Link href="/admin/customers/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Nouveau client
          </Button>
        </Link>
      </div>
      
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="grid gap-2 flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Rechercher un client..." 
                  className="w-full pl-8"
                />
              </div>
            </div>
            <div className="grid gap-2 w-full md:w-[180px]">
              <Select defaultValue="recent">
                <SelectTrigger>
                  <SelectValue placeholder="Trier par" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Ajout récent</SelectItem>
                  <SelectItem value="oldest">Ajout ancien</SelectItem>
                  <SelectItem value="most-orders">Plus de commandes</SelectItem>
                  <SelectItem value="least-orders">Moins de commandes</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Liste des clients</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nom</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Téléphone</TableHead>
                <TableHead>Commandes</TableHead>
                <TableHead>Dernière commande</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {customers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell className="font-medium">{customer.name}</TableCell>
                  <TableCell>{customer.email}</TableCell>
                  <TableCell>{customer.phone}</TableCell>
                  <TableCell>{customer.orders}</TableCell>
                  <TableCell>{customer.lastOrder}</TableCell>
                  <TableCell className="max-w-[200px] truncate">{customer.notes}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon" title="Voir les détails">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Link href={`/admin/customers/${customer.id}/edit`}>
                        <Button variant="ghost" size="icon" title="Modifier">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/admin/customers/${customer.id}/orders`}>
                        <Button variant="ghost" size="icon" title="Voir les commandes">
                          <ShoppingBag className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button variant="ghost" size="icon" title="Supprimer" className="text-destructive">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </AdminLayout>
  );
}