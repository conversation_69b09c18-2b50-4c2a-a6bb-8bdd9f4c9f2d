import { Metadata } from "next";
import { AdminLayout } from "@/components/admin/admin-layout";

export const metadata: Metadata = {
  title: "Paramètres | Admin HCP-DESIGN CI",
  description: "Configurez les paramètres de l'application",
};

export default function SettingsPage() {
  return (
    <AdminLayout>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Paramètres</h1>
      </div>
      <div className="space-y-4">
        <p className="text-muted-foreground">
          Cette page vous permet de configurer les paramètres généraux de l'application.
        </p>
        {/* Contenu à implémenter */}
        <div className="p-8 bg-white rounded-lg border">
          <p className="text-center text-muted-foreground">
            Fonctionnalité en cours de développement
          </p>
        </div>
      </div>
    </AdminLayout>
  );
}