"use client";

import { useState, useEffect } from "react";
import <PERSON>rip<PERSON> from "next/script";
import { Loader2 } from "lucide-react";

interface ReCaptchaV3Props {
  onVerify: (token: string) => void;
  action: string;
}

/**
 * Composant pour intégrer Google reCAPTCHA v3
 * Cette version est invisible pour l'utilisateur et attribue un score de confiance
 */
export function ReCaptchaV3({ onVerify, action = "login" }: ReCaptchaV3Props) {
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Clé de site reCAPTCHA v3
  const SITE_KEY = "6LcZ3TgrAAAAAOnap0r_cX5yFApJZwCux0rvz-T4";

  // Fonction pour exécuter reCAPTCHA
  const executeReCaptcha = async () => {
    if (!window.grecaptcha || !window.grecaptcha.execute) {
      setError("reCAPTCHA n'est pas chargé correctement");
      return;
    }

    try {
      setIsLoading(true);
      const token = await window.grecaptcha.execute(SITE_KEY, { action });
      console.log("reCAPTCHA exécuté avec succès, action:", action);
      onVerify(token);
    } catch (err) {
      console.error("Erreur lors de l'exécution de reCAPTCHA:", err);
      setError("Erreur lors de la vérification de sécurité");
    } finally {
      setIsLoading(false);
    }
  };

  // Gérer le chargement du script
  const handleScriptLoad = () => {
    console.log("Script reCAPTCHA v3 chargé");
    setScriptLoaded(true);
    setIsLoading(true);

    // Attendre que grecaptcha soit disponible et initialisé
    if (window.grecaptcha) {
      if (window.grecaptcha.ready) {
        window.grecaptcha.ready(() => {
          console.log("reCAPTCHA prêt à être utilisé");
          executeReCaptcha();
        });
      } else {
        // Fallback si ready n'est pas disponible
        setTimeout(executeReCaptcha, 1000);
      }
    } else {
      // Fallback si grecaptcha n'est pas disponible immédiatement
      setTimeout(() => {
        if (window.grecaptcha) {
          if (window.grecaptcha.ready) {
            window.grecaptcha.ready(executeReCaptcha);
          } else {
            executeReCaptcha();
          }
        } else {
          setError("Impossible de charger reCAPTCHA");
          setIsLoading(false);
        }
      }, 2000);
    }
  };

  // Gérer l'erreur de chargement du script
  const handleScriptError = () => {
    console.error("Erreur lors du chargement du script reCAPTCHA v3");
    setError("Impossible de charger la vérification de sécurité");
    setIsLoading(false);
  };

  // Exécuter reCAPTCHA périodiquement pour maintenir un token valide
  useEffect(() => {
    if (scriptLoaded) {
      // Rafraîchir le token toutes les 90 secondes (les tokens expirent après 2 minutes)
      const refreshInterval = setInterval(() => {
        executeReCaptcha();
      }, 90000);

      return () => clearInterval(refreshInterval);
    }
  }, [scriptLoaded]);

  return (
    <>
      <Script
        src={`https://www.google.com/recaptcha/api.js?render=${SITE_KEY}`}
        async
        defer
        onLoad={handleScriptLoad}
        onError={handleScriptError}
        strategy="afterInteractive"
      />

      <div className="flex items-center justify-center py-2">
        {isLoading && (
          <div className="flex items-center text-xs text-gray-500">
            <Loader2 className="h-3 w-3 mr-2 animate-spin" />
            Vérification de sécurité en cours...
          </div>
        )}
        {error && (
          <div className="text-xs text-red-500">
            {error}. Veuillez rafraîchir la page.
          </div>
        )}
        {!isLoading && !error && (
          <div className="text-xs text-gray-500">
            Protégé par reCAPTCHA
          </div>
        )}
      </div>
    </>
  );
}

// Déclarer les types pour window
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
    };
  }
}
