-- Table: clients
CREATE TABLE clients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    email VARCHAR(100),
    telephone VARCHAR(20),
    whatsapp VARCHAR(20),
    commentaire TEXT
);

-- Table: modeles_telephones
CREATE TABLE modeles_telephones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    marque VARCHAR(50),
    modele VARCHAR(100),
    image VARCHAR(255)
);

-- Table: produits
CREATE TABLE produits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom VARCHAR(100),
    description TEXT,
    prix FLOAT,
    categorie VARCHAR(50),
    type VARCHAR(50),
    image_principale VARCHAR(255),
    note FLOAT DEFAULT 0,
    nombre_avis INT DEFAULT 0,
    est_nouveau BOOLEAN DEFAULT FALSE,
    est_bestseller BOOLEAN DEFAULT FALSE,
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: images_produits
CREATE TABLE images_produits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    produit_id INT,
    url_image VARCHAR(255),
    ordre INT,
    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE
);

-- Table: couleurs_produits
CREATE TABLE couleurs_produits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    produit_id INT,
    nom_couleur VARCHAR(50),
    code_couleur VARCHAR(7),
    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE
);

-- Table: collections
CREATE TABLE collections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom VARCHAR(100),
    description TEXT,
    image VARCHAR(255)
);

-- Table: produits_collections
CREATE TABLE produits_collections (
    produit_id INT,
    collection_id INT,
    PRIMARY KEY (produit_id, collection_id),
    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE,
    FOREIGN KEY (collection_id) REFERENCES collections(id) ON DELETE CASCADE
);

-- Table: avis_produits
CREATE TABLE avis_produits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    produit_id INT,
    client_id INT,
    note INT,
    commentaire TEXT,
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Table: panier
CREATE TABLE panier (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(255),
    client_id INT,
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL
);

-- Table: panier_items
CREATE TABLE panier_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    panier_id INT,
    produit_id INT,
    quantite INT DEFAULT 1,
    couleur_id INT,
    prix_unitaire FLOAT,
    date_ajout DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (panier_id) REFERENCES panier(id) ON DELETE CASCADE,
    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE,
    FOREIGN KEY (couleur_id) REFERENCES couleurs_produits(id) ON DELETE SET NULL
);

-- Table: coques
CREATE TABLE coques (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom VARCHAR(100),
    categorie VARCHAR(50),
    prix_base FLOAT,
    stock INT
);

-- Table: designs
CREATE TABLE designs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    titre VARCHAR(100),
    theme VARCHAR(100),
    fichier VARCHAR(255),
    tags VARCHAR(255),
    utilisations INT DEFAULT 0
);

-- Table: commandes
CREATE TABLE commandes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT,
    date_commande DATETIME,
    statut VARCHAR(50),
    mode_paiement VARCHAR(50),
    lieu_livraison VARCHAR(100),
    total FLOAT,
    FOREIGN KEY (client_id) REFERENCES clients(id)
);

-- Table: commande_items
CREATE TABLE commande_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    commande_id INT,
    produit_id INT,
    quantite INT,
    prix_unitaire FLOAT,
    couleur VARCHAR(50),
    FOREIGN KEY (commande_id) REFERENCES commandes(id),
    FOREIGN KEY (produit_id) REFERENCES produits(id)
);

-- Table: parametres
CREATE TABLE parametres (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cle VARCHAR(100),
    valeur TEXT
);
