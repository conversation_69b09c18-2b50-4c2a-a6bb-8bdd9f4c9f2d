"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { ShoppingCart, Check, MessageCircle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import { phoneData } from "@/data/phone-models"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { createGalleryProduct, getGalleryProductPrice } from "@/lib/gallery-products"

interface PhoneModelSelectorProps {
  designId: number
  designTitle: string
  designImage: string
}

export default function PhoneModelSelector({ designId, designTitle, designImage }: PhoneModelSelectorProps) {
  const [selectedBrand, setSelectedBrand] = useState<string>("apple")
  const [selectedModel, setSelectedModel] = useState<string>("")
  const [isAdding, setIsAdding] = useState(false)
  const [isMagSafe, setIsMagSafe] = useState(false)
  const [customName, setCustomName] = useState("")
  const { toast } = useToast()
  const { addToCart } = useSimpleCart()

  // Réinitialiser le modèle sélectionné lorsque la marque change
  useEffect(() => {
    setSelectedModel("")
  }, [selectedBrand])

  // Obtenir toutes les marques disponibles
  const brands = Object.entries(phoneData).map(([key, brand]) => ({
    id: key,
    name: brand.name
  }))

  // Obtenir les modèles pour la marque sélectionnée
  const getModelsForBrand = () => {
    if (!selectedBrand) return []
    return phoneData[selectedBrand]?.models || []
  }

  // Ajouter au panier
  const handleAddToCart = () => {
    if (!selectedModel) {
      toast({
        title: "Veuillez sélectionner un modèle",
        description: "Vous devez sélectionner un modèle de téléphone pour continuer",
        variant: "destructive"
      })
      return
    }

    setIsAdding(true)

    // Trouver les détails du modèle sélectionné
    const selectedBrandData = phoneData[selectedBrand]
    const modelData = selectedBrandData.models.find(model => model.id === selectedModel)

    if (!modelData) {
      toast({
        title: "Erreur",
        description: "Modèle non trouvé",
        variant: "destructive"
      })
      setIsAdding(false)
      return
    }

    // Calculer le prix en fonction des options
    const price = getGalleryProductPrice(isMagSafe)

    // Créer l'objet produit en utilisant notre fonction helper
    const product = createGalleryProduct(
      designId,
      designTitle,
      modelData.name,
      modelData.id,
      selectedBrandData.name,
      price,
      designImage,
      isMagSafe,
      customName.trim() || null
    )

    // Ajouter au panier
    addToCart(product)

    // Afficher une notification
    toast({
      title: "Produit ajouté au panier",
      description: `${designTitle} pour ${modelData.name} a été ajouté à votre panier`,
      action: (
        <Button variant="outline" size="sm" onClick={() => window.location.href = "/cart"}>
          Voir le panier
        </Button>
      )
    })

    setIsAdding(false)
  }

  return (
    <div className="space-y-4 border rounded-lg p-4">
      <h3 className="font-medium mb-2">Commander ce design</h3>

      {/* Sélection de la marque */}
      <div className="space-y-2">
        <Label htmlFor="brand">Marque</Label>
        <Select value={selectedBrand} onValueChange={setSelectedBrand}>
          <SelectTrigger id="brand">
            <SelectValue placeholder="Sélectionner une marque" />
          </SelectTrigger>
          <SelectContent>
            {brands.map(brand => (
              <SelectItem key={brand.id} value={brand.id}>
                {brand.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Sélection du modèle */}
      <div className="space-y-2">
        <Label htmlFor="model">Modèle</Label>
        <Select value={selectedModel} onValueChange={setSelectedModel} disabled={!selectedBrand}>
          <SelectTrigger id="model">
            <SelectValue placeholder="Sélectionner un modèle" />
          </SelectTrigger>
          <SelectContent>
            {getModelsForBrand().map(model => (
              <SelectItem key={model.id} value={model.id}>
                {model.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Option MagSafe */}
      <div className="flex items-center space-x-2 py-2">
        <Checkbox
          id="magsafe"
          checked={isMagSafe}
          onCheckedChange={(checked) => setIsMagSafe(checked === true)}
        />
        <Label htmlFor="magsafe" className="cursor-pointer">Option MagSafe (+2.000 Fr)</Label>
      </div>

      {/* Champ pour le nom personnalisé */}
      <div className="space-y-2">
        <Label htmlFor="customName">Ajouter un nom (optionnel)</Label>
        <div className="flex items-center space-x-2">
          <Input
            id="customName"
            placeholder="Votre nom à ajouter"
            value={customName}
            onChange={(e) => setCustomName(e.target.value)}
          />
          <Button variant="outline" size="icon" title="Nous vous contacterons par WhatsApp pour confirmer">
            <MessageCircle className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-xs text-gray-500">Nous vous contacterons par WhatsApp pour confirmer les détails</p>
      </div>

      {/* Prix */}
      <div className="flex justify-between items-center py-2 border-t mt-2 pt-4">
        <span className="text-gray-600">Prix de base:</span>
        <span className="font-medium">8.000 Fr</span>
      </div>
      {isMagSafe && (
        <div className="flex justify-between items-center py-1">
          <span className="text-gray-600">Option MagSafe:</span>
          <span className="font-medium">+2.000 Fr</span>
        </div>
      )}
      <div className="flex justify-between items-center py-2 border-t border-b">
        <span className="text-gray-700 font-medium">Total:</span>
        <span className="font-bold text-lg">{isMagSafe ? '10.000' : '8.000'} Fr</span>
      </div>

      {/* Bouton d'ajout au panier */}
      <Button
        className="w-full mt-4 bg-purple-600 hover:bg-purple-700"
        onClick={handleAddToCart}
        disabled={!selectedModel || isAdding}
      >
        {isAdding ? (
          <>
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Ajout en cours...
            </span>
          </>
        ) : (
          <>
            <ShoppingCart className="mr-2 h-4 w-4" /> Ajouter au panier
          </>
        )}
      </Button>

      {/* Informations supplémentaires */}
      <div className="text-xs text-gray-500 space-y-1 mt-2">
        <div className="flex items-center">
          <Check className="h-3 w-3 mr-1 text-green-500" /> Coque premium de haute qualité
        </div>
        <div className="flex items-center">
          <Check className="h-3 w-3 mr-1 text-green-500" /> Livraison sous 3-5 jours ouvrés
        </div>
        <div className="flex items-center">
          <Check className="h-3 w-3 mr-1 text-green-500" /> Garantie 30 jours satisfait ou remboursé
        </div>
      </div>
    </div>
  )
}
