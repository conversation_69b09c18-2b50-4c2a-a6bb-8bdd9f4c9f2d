// Fonction pour charger les images de manière différée
export function lazyLoadImages() {
  if (typeof window !== 'undefined') {
    // Utiliser l'API Intersection Observer pour charger les images à la demande
    const images = document.querySelectorAll('img[data-src]')
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          img.src = img.dataset.src || ''
          img.removeAttribute('data-src')
          observer.unobserve(img)
        }
      })
    })
    
    images.forEach(img => {
      imageObserver.observe(img)
    })
  }
}

// Fonction pour précharger les ressources critiques
export function preloadCriticalResources() {
  if (typeof window !== 'undefined') {
    // Précharger les polices
    const fontUrls = [
      '/fonts/inter-var.woff2',
      // Ajoutez d'autres polices si nécessaire
    ]
    
    fontUrls.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = url
      link.as = 'font'
      link.type = 'font/woff2'
      link.crossOrigin = 'anonymous'
      document.head.appendChild(link)
    })
    
    // Précharger les images critiques
    const criticalImageUrls = [
      '/images/hero-background.jpg',
      // Ajoutez d'autres images critiques
    ]
    
    criticalImageUrls.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = url
      link.as = 'image'
      document.head.appendChild(link)
    })
  }
}