"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronDown, ChevronUp } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { PhoneBrand, PhoneModel } from "@/types/phone-models";

interface PhoneBrandDropdownProps {
  brand: PhoneBrand;
  brandKey: string;
}

export default function PhoneBrandDropdown({ brand, brandKey }: PhoneBrandDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedSeries, setSelectedSeries] = useState<string | null>(null);

  // Regrouper les modèles par série
  const modelsBySeries = brand.models.reduce((acc, model) => {
    // Extraire la série du nom du modèle
    let series = "";
    
    if (brandKey === "apple") {
      // Pour iPhone, extraire le numéro de série (iPhone 15, iPhone 14, etc.)
      const match = model.name.match(/iPhone (\d+)/);
      if (match) {
        series = `iPhone ${match[1]}`;
      } else if (model.name.includes("SE")) {
        series = "iPhone SE";
      } else {
        series = "Autres iPhone";
      }
    } else if (brandKey === "samsung") {
      // Pour Samsung, extraire la série (S25, S24, A54, etc.)
      if (model.name.includes("S25")) {
        series = "Galaxy S25";
      } else if (model.name.includes("S24")) {
        series = "Galaxy S24";
      } else if (model.name.includes("S23")) {
        series = "Galaxy S23";
      } else if (model.name.includes("S22")) {
        series = "Galaxy S22";
      } else if (model.name.includes("A")) {
        series = "Galaxy A";
      } else {
        series = "Autres Galaxy";
      }
    } else if (brandKey === "google") {
      // Pour Google, extraire la série (Pixel 8, Pixel 7, etc.)
      const match = model.name.match(/Pixel (\d+)/);
      if (match) {
        series = `Pixel ${match[1]}`;
      } else {
        series = "Autres Pixel";
      }
    } else if (brandKey === "xiaomi") {
      // Pour Xiaomi, extraire la série (Xiaomi 13, Redmi Note 12, etc.)
      if (model.name.includes("Xiaomi")) {
        const match = model.name.match(/Xiaomi (\d+)/);
        if (match) {
          series = `Xiaomi ${match[1]}`;
        } else {
          series = "Autres Xiaomi";
        }
      } else if (model.name.includes("Redmi")) {
        const match = model.name.match(/Redmi Note (\d+)/);
        if (match) {
          series = `Redmi Note ${match[1]}`;
        } else {
          series = "Autres Redmi";
        }
      } else {
        series = "Autres Xiaomi";
      }
    } else if (brandKey === "huawei") {
      // Pour Huawei, extraire la série (P60, etc.)
      const match = model.name.match(/P(\d+)/);
      if (match) {
        series = `P${match[1]}`;
      } else {
        series = "Autres Huawei";
      }
    } else {
      series = "Autres modèles";
    }
    
    if (!acc[series]) {
      acc[series] = [];
    }
    acc[series].push(model);
    return acc;
  }, {} as Record<string, PhoneModel[]>);

  // Trier les séries par ordre décroissant (les plus récentes d'abord)
  const sortedSeries = Object.keys(modelsBySeries).sort((a, b) => {
    // Extraire les numéros des séries pour les comparer
    const numA = parseInt(a.match(/\d+/)?.[0] || "0");
    const numB = parseInt(b.match(/\d+/)?.[0] || "0");
    return numB - numA;
  });

  return (
    <Card className="w-full mb-6">
      <CardHeader className="pb-2">
        <div 
          className="flex items-center justify-between cursor-pointer" 
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 relative">
              <Image
                src={brand.logo}
                alt={brand.name}
                fill
                className="object-contain"
              />
            </div>
            <CardTitle>{brand.name}</CardTitle>
          </div>
          <Button variant="ghost" size="icon">
            {isOpen ? <ChevronUp /> : <ChevronDown />}
          </Button>
        </div>
      </CardHeader>
      
      {isOpen && (
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
            {sortedSeries.map((series) => (
              <div key={series} className="border rounded-lg p-3">
                <div 
                  className="flex items-center justify-between cursor-pointer mb-2"
                  onClick={() => setSelectedSeries(selectedSeries === series ? null : series)}
                >
                  <h3 className="font-medium">{series}</h3>
                  <Button variant="ghost" size="sm">
                    {selectedSeries === series ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                </div>
                
                {selectedSeries === series && (
                  <div className="space-y-2 mt-2">
                    {modelsBySeries[series].map((model) => (
                      <Link 
                        href={`/models/${brandKey}/${model.id}`} 
                        key={model.id}
                        className="block"
                      >
                        <div className="flex items-center justify-between p-2 hover:bg-gray-100 rounded-md">
                          <span>{model.name}</span>
                          <div className="flex gap-1">
                            {model.availableQualities?.map((quality) => (
                              <Badge key={quality} variant="outline" className="text-xs">
                                {quality}
                              </Badge>
                            ))}
                            {model.popular && (
                              <Badge variant="secondary" className="text-xs">
                                Populaire
                              </Badge>
                            )}
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}
