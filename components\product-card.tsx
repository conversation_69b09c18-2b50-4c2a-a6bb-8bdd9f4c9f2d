"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Heart, ShoppingCart, Star } from "lucide-react"
import Link from "next/link"
import { useToast } from "@/components/ui/use-toast"
import { useSimpleCart } from "@/hooks/use-simple-cart"
import { useFavoritesContext } from "@/hooks/favorites-provider"

interface Product {
  id: number
  name: string
  price: number
  image: string
  rating: number
  reviews: number
  isNew: boolean
  isBestseller: boolean
  compatibleModels?: string[]
  category?: string
  type: string
  colors: string[]
  collections: string[]
}

interface ProductCardProps {
  product: Product
  viewMode: "grid" | "list"
}

// Composant ProductCard
export default function ProductCard({ product, viewMode = "grid" }: ProductCardProps) {
  const [isAdding, setIsAdding] = useState(false)
  const { toast } = useToast()
  const { addToCart } = useSimpleCart()
  const { favorites, toggleFavorite } = useFavoritesContext()

  const isLiked = favorites.some(fav => fav.id === product.id.toString())

  const toggleLike = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }

    // Utiliser la fonction toggleFavorite du hook useFavorites
    toggleFavorite({
      id: product.id.toString(),
      name: product.name,
      price: product.price,
      image_url: product.image
    })

    toast({
      title: isLiked ? "Retiré des favoris" : "Ajouté aux favoris",
      description: isLiked
        ? `${product.name} a été retiré de vos favoris.`
        : `${product.name} a été ajouté à vos favoris.`
    })
  }

  const handleAddToCart = async (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }

    // Si déjà en cours d'ajout, ne rien faire
    if (isAdding) return;

    setIsAdding(true)
    console.log("ProductCard - handleAddToCart - Début de l'ajout au panier pour:", product.name);

    try {
      // Convertir le produit au format attendu par useCart
      const cartProduct = {
        id: product.id.toString(),
        name: product.name,
        price: product.price,
        image_url: product.image,
        category: product.category || "Accessoires",
        subcategory: product.type,
        is_new: product.isNew,
        is_bestseller: product.isBestseller,
        created_at: new Date().toISOString(),
        type: product.type,
        colors: product.colors,
        collections: product.collections
      }

      console.log("ProductCard - handleAddToCart - Produit formaté:", cartProduct);

      // Utiliser await pour s'assurer que l'ajout est terminé avant de continuer
      await addToCart(cartProduct, 1)
      console.log("ProductCard - handleAddToCart - Produit ajouté avec succès");

      toast({
        title: "Produit ajouté",
        description: `${product.name} a été ajouté à votre panier.`,
        action: (
          <Button variant="outline" size="sm" onClick={() => window.location.href = "/cart"}>
            Voir le panier
          </Button>
        )
      })

      // Sauvegarder localement en cas de problème avec Supabase
      try {
        if (typeof window !== 'undefined') {
          const localCart = JSON.parse(localStorage.getItem('local-cart') || '[]');
          const existingItemIndex = localCart.findIndex((item: any) => item.id === cartProduct.id);

          if (existingItemIndex >= 0) {
            localCart[existingItemIndex].quantity += 1;
          } else {
            localCart.push({ ...cartProduct, quantity: 1 });
          }

          localStorage.setItem('local-cart', JSON.stringify(localCart));
          console.log("ProductCard - handleAddToCart - Produit sauvegardé localement");
        }
      } catch (localError) {
        console.error("ProductCard - handleAddToCart - Erreur lors de la sauvegarde locale:", localError);
      }
    } catch (error) {
      console.error("ProductCard - handleAddToCart - Erreur lors de l'ajout au panier:", error);

      toast({
        title: "Erreur",
        description: "Impossible d'ajouter ce produit au panier.",
        variant: "destructive"
      })

      // En cas d'erreur, essayer de sauvegarder localement
      try {
        if (typeof window !== 'undefined') {
          const localCart = JSON.parse(localStorage.getItem('local-cart') || '[]');
          const cartProduct = {
            id: product.id.toString(),
            name: product.name,
            price: product.price,
            image_url: product.image,
            category: product.category || "Accessoires",
            subcategory: product.type,
            is_new: product.isNew,
            is_bestseller: product.isBestseller,
            created_at: new Date().toISOString(),
            quantity: 1
          };

          const existingItemIndex = localCart.findIndex((item: any) => item.id === cartProduct.id);

          if (existingItemIndex >= 0) {
            localCart[existingItemIndex].quantity += 1;
          } else {
            localCart.push(cartProduct);
          }

          localStorage.setItem('local-cart', JSON.stringify(localCart));
          console.log("ProductCard - handleAddToCart - Produit sauvegardé localement (mode secours)");

          toast({
            title: "Produit ajouté",
            description: `${product.name} a été ajouté à votre panier local.`,
          });
        }
      } catch (localError) {
        console.error("ProductCard - handleAddToCart - Erreur lors de la sauvegarde locale (mode secours):", localError);
      }
    } finally {
      setIsAdding(false)
    }
  }

  // Formater le prix en FCFA
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR').format(price) + " FCFA"
  }

  if (viewMode === "list") {
    return (
      <Card className="overflow-hidden transition-all hover:shadow-md">
        <CardContent className="p-0">
          <div className="flex flex-col sm:flex-row">
            <div className="relative sm:w-48 h-48">
              <img
                src={product.image || "/placeholder.svg"}
                alt={product.name}
                className="w-full h-full object-cover"
              />
              {product.isNew && <Badge className="absolute top-2 left-2 bg-blue-500">Nouveau</Badge>}
              {product.isBestseller && <Badge className="absolute top-2 left-2 bg-orange-500">Bestseller</Badge>}
              <Button
                variant="ghost"
                size="icon"
                className={`absolute top-2 right-2 rounded-full bg-white/80 hover:bg-white ${isLiked ? "text-red-500" : "text-gray-500"}`}
                onClick={toggleLike}
              >
                <Heart className="h-5 w-5" fill={isLiked ? "currentColor" : "none"} />
              </Button>
            </div>
            <div className="p-4 flex-1 flex flex-col">
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-1">{product.name}</h3>
                <div className="flex items-center mb-2">
                  <div className="flex items-center text-amber-500 mr-2">
                    <Star className="h-4 w-4 fill-current" />
                    <span className="ml-1 text-sm">{product.rating ? product.rating.toFixed(1) : '0.0'}</span>
                  </div>
                  <span className="text-sm text-gray-500">({product.reviews || 0} avis)</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">Type: {product.type}</p>
                {product.category && (
                  <p className="text-sm text-gray-600 mb-2">
                    Catégorie: {product.category.charAt(0).toUpperCase() + product.category.slice(1)}
                  </p>
                )}
                {product.compatibleModels && (
                  <p className="text-sm text-gray-600 mb-2">
                    Compatible: {product.compatibleModels.slice(0, 3).join(", ")}
                    {product.compatibleModels.length > 3 && " et plus"}
                  </p>
                )}
                <div className="flex flex-wrap gap-1 mb-2">
                  {product.colors.map((color) => {
                    // Convertir le nom de la couleur en format compatible avec les classes CSS
                    const colorClass = `color-${color.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '')}`;
                    return (
                      <div
                        key={color}
                        className={`color-swatch ${colorClass}`}
                        title={color}
                      />
                    );
                  })}
                </div>
              </div>
              <div className="flex items-center justify-between mt-2">
                <span className="font-bold text-purple-600 text-lg">{formatPrice(product.price)}</span>
                <div className="flex space-x-2">
                  <Link href={`/products/${product.id}`}>
                    <Button variant="outline" size="sm">
                      Afficher le produit
                    </Button>
                  </Link>
                  <Button
                    size="sm"
                    className="bg-purple-600 hover:bg-purple-700"
                    onClick={handleAddToCart}
                    disabled={isAdding}
                  >
                    {isAdding ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    ) : (
                      <ShoppingCart className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="overflow-hidden transition-all hover:shadow-md flex flex-col h-full">
      <CardContent className="p-0 flex flex-col h-full">
        {/* Section Image - 80% de l'espace */}
        <div className="relative w-full flex-[4]">
          <div className="relative h-full overflow-hidden">
            <img src={product.image || "/placeholder.svg"} alt={product.name} className="w-full h-full object-cover" />
            
            {/* Seuls les badges et bouton favoris sont en overlay sur l'image */}
            {product.isNew && <Badge className="absolute top-2 left-2 bg-blue-500 shadow-sm">Nouveau</Badge>}
            {product.isBestseller && <Badge className="absolute top-2 left-2 bg-orange-500 shadow-sm">Bestseller</Badge>}
            <Button
              variant="ghost"
              size="icon"
              className={`absolute top-2 right-2 rounded-full bg-white/90 hover:bg-white shadow-sm ${isLiked ? "text-red-500" : "text-gray-500"}`}
              onClick={toggleLike}
            >
              <Heart className="h-5 w-5" fill={isLiked ? "currentColor" : "none"} />
            </Button>
          </div>
        </div>
        
        {/* Section Contenu - 20% de l'espace */}
        <div className="p-2 flex-1 flex flex-col justify-between min-h-0">
          <div className="space-y-1">
            <h3 className="font-semibold text-xs line-clamp-1 leading-tight">{product.name}</h3>
            <div className="flex items-center justify-between">
              <span className="font-bold text-purple-600 text-sm">{formatPrice(product.price)}</span>
              {product.rating && (
                <div className="flex items-center text-xs text-amber-500">
                  <Star className="h-3 w-3 fill-current" />
                  <span className="ml-1">{product.rating.toFixed(1)}</span>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex gap-1 mt-1">
            <Link href={`/products/${product.id}`} className="flex-1">
              <Button variant="outline" size="sm" className="w-full h-6 text-xs px-2">
                Voir
              </Button>
            </Link>
            <Button
              size="sm"
              className="bg-purple-600 hover:bg-purple-700 h-6 px-2"
              onClick={handleAddToCart}
              disabled={isAdding}
            >
              {isAdding ? (
                <div className="h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <ShoppingCart className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
