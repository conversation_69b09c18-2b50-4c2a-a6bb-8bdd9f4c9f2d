/**
 * Script pour vérifier que les images référencées dans le code existent réellement
 */

const fs = require('fs');
const path = require('path');

// Fonction pour lire un fichier
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier ${filePath}:`, error);
    return null;
  }
}

// Fonction pour vérifier si un fichier existe
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Fonction pour extraire les chemins d'images d'un fichier
function extractImagePaths(filePath) {
  console.log(`Extraction des chemins d'images de ${filePath}...`);
  
  const content = readFile(filePath);
  if (!content) return [];
  
  // Recherche des références aux images
  const imageRegex = /(?:src|image|imageUrl|thumbnail):\s*["']([^"']*\.(?:png|jpg|jpeg|svg|gif|webp))(?:\?[^"']*)?["']/g;
  let match;
  const imagePaths = [];
  
  while ((match = imageRegex.exec(content)) !== null) {
    const imagePath = match[1];
    
    // Ignorer les URLs externes
    if (imagePath.startsWith('http')) continue;
    
    // Ignorer les placeholders
    if (imagePath.includes('placeholder')) continue;
    
    // Ajouter le chemin à la liste
    imagePaths.push(imagePath);
  }
  
  return imagePaths;
}

// Fonction pour vérifier si une image existe
function checkImageExists(imagePath) {
  // Normaliser le chemin
  let normalizedPath = imagePath;
  if (normalizedPath.startsWith('/')) {
    normalizedPath = normalizedPath.substring(1);
  }
  
  // Construire le chemin absolu
  const absolutePath = path.join(process.cwd(), 'public', normalizedPath);
  
  // Vérifier si l'image existe
  return fileExists(absolutePath);
}

// Fonction pour parcourir récursivement un répertoire
function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    isDirectory ? walkDir(dirPath, callback) : callback(path.join(dir, f));
  });
}

// Fonction principale
function main() {
  console.log("Début de la vérification des images réelles...");
  
  const missingImages = [];
  const existingImages = [];
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire app
  walkDir('app', (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const imagePaths = extractImagePaths(filePath);
      
      imagePaths.forEach(imagePath => {
        const exists = checkImageExists(imagePath);
        if (exists) {
          existingImages.push({ file: filePath, path: imagePath });
        } else {
          missingImages.push({ file: filePath, path: imagePath });
        }
      });
    }
  });
  
  // Parcourir les fichiers .tsx et .jsx dans le répertoire components
  walkDir('components', (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const imagePaths = extractImagePaths(filePath);
      
      imagePaths.forEach(imagePath => {
        const exists = checkImageExists(imagePath);
        if (exists) {
          existingImages.push({ file: filePath, path: imagePath });
        } else {
          missingImages.push({ file: filePath, path: imagePath });
        }
      });
    }
  });
  
  // Afficher les résultats
  console.log(`\nRésultats de la vérification :`);
  console.log(`- Images existantes : ${existingImages.length}`);
  console.log(`- Images manquantes : ${missingImages.length}`);
  
  if (missingImages.length > 0) {
    console.log("\nListe des images manquantes :");
    missingImages.forEach(item => {
      console.log(`- ${item.path} (référencée dans ${item.file})`);
    });
  }
  
  console.log("\nVérification des images réelles terminée.");
}

// Exécuter la fonction principale
main();
