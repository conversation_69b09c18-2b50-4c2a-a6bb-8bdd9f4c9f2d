"use client";

import { useState } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';
import { formatOrderMessage, sendTelegramNotification } from '@/lib/telegram-service';

export default function OrderForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    items: [{ name: '', quantity: 1, price: 0 }],
    totalAmount: 0,
    advancePayment: 0,
    remainingAmount: 0,
    paymentMethod: 'Orange Money',
    deliveryCity: '',
    deliveryAddress: '',
    specialInstructions: ''
  });

  // Calculer le montant restant automatiquement
  const calculateRemainingAmount = () => {
    const total = parseInt(formData.totalAmount) || 0;
    const advance = parseInt(formData.advancePayment) || 0;
    return total - advance;
  };

  // Mettre à jour le champ de formulaire
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
      ...(name === 'totalAmount' || name === 'advancePayment'
        ? { remainingAmount: calculateRemainingAmount() }
        : {})
    }));
  };

  // Mettre à jour un article
  const handleItemChange = (index, field, value) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: field === 'quantity' || field === 'price' ? parseInt(value) || 0 : value
    };

    setFormData(prev => ({
      ...prev,
      items: updatedItems
    }));
  };

  // Ajouter un nouvel article
  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { name: '', quantity: 1, price: 0 }]
    }));
  };

  // Supprimer un article
  const removeItem = (index) => {
    if (formData.items.length > 1) {
      const updatedItems = formData.items.filter((_, i) => i !== index);
      setFormData(prev => ({
        ...prev,
        items: updatedItems
      }));
    }
  };

  // Soumettre le formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation basique
    if (!formData.name || !formData.phone || formData.items.some(item => !item.name)) {
      toast.error('Veuillez remplir tous les champs obligatoires');
      return;
    }

    setIsSubmitting(true);

    try {
      // Calculer le montant restant une dernière fois
      const remainingAmount = calculateRemainingAmount();

      // Préparer les données pour Supabase
      const orderData = {
        customer_name: formData.name,
        customer_phone: formData.phone,
        total: parseInt(formData.totalAmount) || 0,
        payment_method: formData.paymentMethod,
        shipping_city: formData.deliveryCity,
        shipping_address: formData.deliveryAddress || 'Non spécifié',
        shipping_notes: formData.specialInstructions || '',
        status: 'pending',
        payment_message: `Avance: ${formData.advancePayment} Fr, Reste: ${remainingAmount} Fr`
      };

      // 1. Créer la commande dans Supabase
      const { data: orderResult, error: orderError } = await supabase
        .from('orders')
        .insert(orderData)
        .select()
        .single();

      if (orderError) {
        console.error('Erreur lors de la création de la commande:', orderError);
        toast.error(`Erreur: ${orderError.message}`);
        return;
      }

      // 2. Ajouter les articles de la commande
      const orderItems = formData.items.map(item => ({
        order_id: orderResult.id,
        product_id: null, // Si vous avez l'ID du produit, utilisez-le ici
        quantity: item.quantity,
        price: item.price,
        customized: false,
        customization_data: { name: item.name } // Stocker le nom de l'article si pas de référence produit
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) {
        console.error('Erreur lors de l\'ajout des articles:', itemsError);
        toast.error(`Erreur: ${itemsError.message}`);
        return;
      }

      // 3. Envoyer une notification Telegram
      // Le déclencheur SQL devrait automatiquement envoyer une notification,
      // mais nous envoyons également une notification via l'API pour plus de fiabilité
      const telegramMessage = formatOrderMessage({
        ...formData,
        remainingAmount
      });

      const { success: telegramSuccess, error: telegramError } = await sendTelegramNotification(telegramMessage);

      if (telegramError) {
        console.warn('Avertissement: Notification Telegram non envoyée via API:', telegramError);
        // Ne pas bloquer le processus si la notification échoue
      }

      // Succès
      toast.success('Commande enregistrée avec succès!');

      // Réinitialiser le formulaire
      setFormData({
        name: '',
        phone: '',
        items: [{ name: '', quantity: 1, price: 0 }],
        totalAmount: 0,
        advancePayment: 0,
        remainingAmount: 0,
        paymentMethod: 'Orange Money',
        deliveryCity: '',
        deliveryAddress: '',
        specialInstructions: ''
      });
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la commande:', error);
      toast.error('Erreur lors du traitement de la commande');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">Formulaire de Commande</h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Informations client */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Nom du client *</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Téléphone *</label>
            <input
              type="text"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
              required
            />
          </div>
        </div>

        {/* Articles */}
        <div>
          <label className="block text-sm font-medium mb-2">Articles commandés *</label>
          {formData.items.map((item, index) => (
            <div key={index} className="flex flex-wrap items-center gap-2 mb-2">
              <input
                type="text"
                placeholder="Nom de l'article"
                value={item.name}
                onChange={(e) => handleItemChange(index, 'name', e.target.value)}
                className="flex-1 p-2 border rounded-md"
                required
              />
              <input
                type="number"
                placeholder="Qté"
                value={item.quantity}
                onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                className="w-16 p-2 border rounded-md"
                min="1"
              />
              <input
                type="number"
                placeholder="Prix"
                value={item.price}
                onChange={(e) => handleItemChange(index, 'price', e.target.value)}
                className="w-24 p-2 border rounded-md"
                min="0"
              />
              <button
                type="button"
                onClick={() => removeItem(index)}
                className="p-2 bg-red-500 text-white rounded-md"
                disabled={formData.items.length <= 1}
              >
                X
              </button>
            </div>
          ))}
          <button
            type="button"
            onClick={addItem}
            className="mt-2 p-2 bg-blue-500 text-white rounded-md"
          >
            + Ajouter un article
          </button>
        </div>

        {/* Paiement */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Montant total (Fr) *</label>
            <input
              type="number"
              name="totalAmount"
              value={formData.totalAmount}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
              min="0"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Avance payée (Fr)</label>
            <input
              type="number"
              name="advancePayment"
              value={formData.advancePayment}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
              min="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Reste à payer (Fr)</label>
            <input
              type="number"
              name="remainingAmount"
              value={calculateRemainingAmount()}
              className="w-full p-2 border rounded-md bg-gray-100"
              readOnly
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Méthode de paiement</label>
          <select
            name="paymentMethod"
            value={formData.paymentMethod}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
          >
            <option value="Orange Money">Orange Money</option>
            <option value="Wave">Wave</option>
            <option value="Espèces">Espèces</option>
            <option value="Autre">Autre</option>
          </select>
        </div>

        {/* Livraison */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Ville de livraison *</label>
            <input
              type="text"
              name="deliveryCity"
              value={formData.deliveryCity}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Adresse de livraison</label>
            <input
              type="text"
              name="deliveryAddress"
              value={formData.deliveryAddress}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
            />
          </div>
        </div>

        {/* Instructions spéciales */}
        <div>
          <label className="block text-sm font-medium mb-1">Instructions spéciales</label>
          <textarea
            name="specialInstructions"
            value={formData.specialInstructions}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            rows="3"
          ></textarea>
        </div>

        {/* Bouton de soumission */}
        <div className="text-center">
          <button
            type="submit"
            className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Envoi en cours...' : 'Envoyer la commande'}
          </button>
        </div>
      </form>
    </div>
  );
}
