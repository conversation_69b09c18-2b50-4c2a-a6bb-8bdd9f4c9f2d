import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Package, Eye, Download } from "lucide-react"

export default function OrdersPage() {
  // Données fictives pour les commandes
  const orders = [
    {
      id: "ORD-12345",
      date: "15/04/2025",
      status: "Livré",
      total: "49,90 €",
      items: 2,
      statusColor: "bg-green-500"
    },
    {
      id: "ORD-12344",
      date: "02/04/2025",
      status: "En cours de livraison",
      total: "29,95 €",
      items: 1,
      statusColor: "bg-blue-500"
    },
    {
      id: "ORD-12343",
      date: "20/03/2025",
      status: "Traité",
      total: "79,85 €",
      items: 3,
      statusColor: "bg-yellow-500"
    }
  ]

  return (
    <div className="container mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold mb-6">Mes commandes</h1>
      
      <div className="space-y-6">
        {orders.map((order) => (
          <Card key={order.id}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">Commande {order.id}</CardTitle>
                <Badge className={`${order.statusColor}`}>{order.status}</Badge>
              </div>
              <CardDescription>Passée le {order.date} • {order.items} article{order.items > 1 ? 's' : ''}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Total: {order.total}</p>
                </div>
                <div className="flex space-x-2">
                  <Link href={`/account/orders/${order.id}`}>
                    <Button variant="outline" size="sm" className="flex items-center">
                      <Eye className="h-4 w-4 mr-2" /> Détails
                    </Button>
                  </Link>
                  <Button variant="outline" size="sm" className="flex items-center">
                    <Download className="h-4 w-4 mr-2" /> Facture
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {orders.length === 0 && (
        <Card className="text-center py-10">
          <CardContent>
            <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium mb-2">Aucune commande</h3>
            <p className="text-gray-500 mb-6">Vous n'avez pas encore passé de commande.</p>
            <Link href="/models">
              <Button>Découvrir nos modèles</Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}