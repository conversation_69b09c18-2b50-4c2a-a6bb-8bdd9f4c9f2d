# Guide pour résoudre les problèmes de déploiement

Ce guide vous aidera à résoudre les problèmes courants liés au déploiement de votre site sur Vercel.

## Problèmes courants

### 1. Le site en ligne affiche une ancienne version

Si le site en ligne continue d'afficher une ancienne version malgré vos modifications, voici les causes possibles et leurs solutions :

#### Problème de cache du navigateur

**Symptôme** : Seul votre navigateur affiche l'ancienne version.

**Solutions** :
- Videz le cache de votre navigateur (Ctrl+F5 ou Cmd+Shift+R)
- Essayez d'ouvrir le site en navigation privée
- Essayez d'ouvrir le site sur un autre navigateur ou appareil

#### Problème de cache du CDN

**Symptôme** : Tout le monde voit l'ancienne version.

**Solutions** :
- Ajoutez des paramètres de version aux URLs des images (ex: `/images/logo.png?v=2`)
- Exécutez le script `invalidate-cache.js` pour ajouter automatiquement des paramètres de version
- Attendez que le cache expire naturellement (généralement 24-48 heures)

#### Problème de déploiement

**Symptôme** : Les modifications ne sont pas déployées.

**Solutions** :
- Vérifiez que vos modifications ont bien été poussées vers GitHub
- Vérifiez le tableau de bord Vercel pour voir si le déploiement a réussi
- Forcez un redéploiement en exécutant le script `force-redeploy.js`

### 2. Les images ne s'affichent pas sur le site en ligne

**Symptôme** : Les images s'affichent localement mais pas en ligne.

**Solutions** :
- Vérifiez que les images existent dans le dépôt Git
- Assurez-vous que les chemins d'accès sont corrects (commençant par `/images/`)
- Exécutez le script `fix-image-paths.js` pour corriger les références aux images
- Supprimez les fichiers `.gitkeep` des dossiers qui contiennent déjà des images

## Scripts utilitaires

Le projet inclut plusieurs scripts pour vous aider à résoudre les problèmes de déploiement :

### 1. Force redeploy

```bash
node scripts/force-redeploy.js
```

Ce script force un redéploiement en modifiant légèrement un fichier et en ajoutant un commentaire de version.

### 2. Invalidate cache

```bash
node scripts/invalidate-cache.js
```

Ce script invalide le cache en ajoutant des paramètres de version aux références d'images.

### 3. Fix image paths

```bash
node scripts/fix-image-paths.js
```

Ce script corrige les références aux images placeholder en les remplaçant par des références aux vraies images.

### 4. Remove gitkeep

```bash
node scripts/remove-gitkeep.js
```

Ce script supprime les fichiers `.gitkeep` des dossiers qui contiennent déjà des images.

## Vérification du déploiement

Pour vérifier l'état de votre déploiement :

1. Allez sur le tableau de bord Vercel : https://vercel.com/dashboard
2. Sélectionnez votre projet
3. Vérifiez l'onglet "Deployments" pour voir les déploiements récents
4. Cliquez sur un déploiement pour voir les détails et les logs

## Forcer un redéploiement manuel sur Vercel

Si les scripts ne fonctionnent pas, vous pouvez forcer un redéploiement manuellement :

1. Allez sur le tableau de bord Vercel : https://vercel.com/dashboard
2. Sélectionnez votre projet
3. Cliquez sur l'onglet "Deployments"
4. Trouvez le dernier déploiement réussi
5. Cliquez sur les trois points (...) à droite du déploiement
6. Sélectionnez "Redeploy"

## Vérification des images sur le site en ligne

Pour vérifier si les images sont correctement déployées :

1. Ouvrez le site en ligne
2. Ouvrez les outils de développement (F12 ou Ctrl+Shift+I)
3. Allez dans l'onglet "Network"
4. Rechargez la page (F5)
5. Filtrez par "img" pour voir uniquement les requêtes d'images
6. Vérifiez si les images sont chargées correctement (code 200) ou si elles génèrent des erreurs (code 404)

## Besoin d'aide supplémentaire ?

Si vous rencontrez toujours des problèmes avec le déploiement après avoir suivi ce guide, contactez l'équipe de développement pour obtenir de l'aide.
