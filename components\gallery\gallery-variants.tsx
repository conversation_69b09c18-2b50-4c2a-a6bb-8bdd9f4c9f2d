"use client";

import { useGalleryVariants } from "@/hooks/use-gallery-variants";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { Skeleton } from "@/components/ui/skeleton";
import { getVersionedImagePath } from "@/lib/image-version";

export default function GalleryVariants() {
  const { activeVariants, isLoading, error } = useGalleryVariants();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="sticky top-0 z-50 bg-white border-b border-gray-200 border-solid shadow-sm p-0">
              <Skeleton className="h-48 w-full" />
            </CardHeader>
            <CardContent className="p-4">
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-full" />
            </CardContent>
            <CardFooter className="p-4 pt-0">
              <Skeleton className="h-10 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 text-red-500 rounded-md">
        <p>Erreur lors du chargement des variantes de la galerie.</p>
        <p>{error.message}</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {activeVariants.map((variant) => (
        <Card key={variant.id} className="overflow-hidden">
          <CardHeader className="p-0">
            <div className="relative h-48 w-full">
              <Image
                src={getVersionedImagePath(variant.image_url)}
                alt={variant.name}
                fill
                className="object-cover"
              />
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <CardTitle>{variant.name}</CardTitle>
            <CardDescription>{variant.description}</CardDescription>
          </CardContent>
          <CardFooter className="p-4 pt-0">
            <Link href={`/customize`} className="w-full">
              <Button variant="default" className="w-full">
                Personnaliser ce design
              </Button>
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
