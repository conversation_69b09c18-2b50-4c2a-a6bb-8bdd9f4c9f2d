'use client';

import { useState, useEffect } from 'react';
import styles from './design-manager.module.css';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Search, Plus, Edit, Trash2, Image, Palette, Eye, Copy, Download } from 'lucide-react';
import { toast } from 'sonner';

interface Design {
  id: string;
  name: string;
  category: 'abstract' | 'nature' | 'geometric' | 'text' | 'photo' | 'pattern' | 'minimal';
  tags: string[];
  imageUrl: string;
  thumbnailUrl?: string;
  description?: string;
  isActive: boolean;
  isPremium: boolean;
  downloadCount: number;
  rating: number;
  phoneModels: string[]; // IDs des modèles compatibles
  colors: string[]; // Couleurs dominantes
  fileSize: number; // en KB
  resolution: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export function DesignManager() {
  const [designs, setDesigns] = useState<Design[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [selectedDesign, setSelectedDesign] = useState<Design | null>(null);
  const [newDesign, setNewDesign] = useState<Partial<Design>>({});

  // Données de démonstration
  useEffect(() => {
    const mockDesigns: Design[] = [
      {
        id: '1',
        name: 'Sunset Gradient',
        category: 'abstract',
        tags: ['gradient', 'sunset', 'orange', 'purple'],
        imageUrl: '/images/designs/sunset-gradient.jpg',
        thumbnailUrl: '/images/designs/thumbs/sunset-gradient.jpg',
        description: 'Un magnifique dégradé de coucher de soleil',
        isActive: true,
        isPremium: false,
        downloadCount: 1250,
        rating: 4.8,
        phoneModels: ['1', '2', '3'],
        colors: ['#FF6B35', '#F7931E', '#FFD23F', '#9B59B6'],
        fileSize: 2048,
        resolution: '1080x1920',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
        createdBy: 'Admin'
      },
      {
        id: '2',
        name: 'Tropical Leaves',
        category: 'nature',
        tags: ['tropical', 'leaves', 'green', 'nature'],
        imageUrl: '/images/designs/tropical-leaves.jpg',
        thumbnailUrl: '/images/designs/thumbs/tropical-leaves.jpg',
        description: 'Feuilles tropicales luxuriantes',
        isActive: true,
        isPremium: true,
        downloadCount: 890,
        rating: 4.6,
        phoneModels: ['1', '2'],
        colors: ['#27AE60', '#2ECC71', '#58D68D', '#85C1E9'],
        fileSize: 3200,
        resolution: '1080x1920',
        createdAt: '2024-01-20T10:00:00Z',
        updatedAt: '2024-01-20T10:00:00Z',
        createdBy: 'Designer1'
      },
      {
        id: '3',
        name: 'Geometric Patterns',
        category: 'geometric',
        tags: ['geometric', 'pattern', 'minimal', 'blue'],
        imageUrl: '/images/designs/geometric-patterns.jpg',
        thumbnailUrl: '/images/designs/thumbs/geometric-patterns.jpg',
        description: 'Motifs géométriques modernes',
        isActive: true,
        isPremium: false,
        downloadCount: 2100,
        rating: 4.9,
        phoneModels: ['1', '2', '3', '4'],
        colors: ['#3498DB', '#2980B9', '#5DADE2', '#AED6F1'],
        fileSize: 1800,
        resolution: '1080x1920',
        createdAt: '2024-02-01T10:00:00Z',
        updatedAt: '2024-02-01T10:00:00Z',
        createdBy: 'Designer2'
      },
      {
        id: '4',
        name: 'Vintage Typography',
        category: 'text',
        tags: ['vintage', 'typography', 'retro', 'brown'],
        imageUrl: '/images/designs/vintage-typography.jpg',
        thumbnailUrl: '/images/designs/thumbs/vintage-typography.jpg',
        description: 'Typographie vintage élégante',
        isActive: false,
        isPremium: true,
        downloadCount: 450,
        rating: 4.3,
        phoneModels: ['2', '3'],
        colors: ['#8B4513', '#D2691E', '#F4A460', '#DEB887'],
        fileSize: 2500,
        resolution: '1080x1920',
        createdAt: '2024-01-10T10:00:00Z',
        updatedAt: '2024-03-01T10:00:00Z',
        createdBy: 'Designer1'
      }
    ];
    setDesigns(mockDesigns);
  }, []);

  const categories = [
    { value: 'abstract', label: 'Abstrait' },
    { value: 'nature', label: 'Nature' },
    { value: 'geometric', label: 'Géométrique' },
    { value: 'text', label: 'Texte' },
    { value: 'photo', label: 'Photo' },
    { value: 'pattern', label: 'Motif' },
    { value: 'minimal', label: 'Minimal' }
  ];

  const filteredDesigns = designs.filter(design => {
    const matchesSearch = 
      design.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      design.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = categoryFilter === 'all' || design.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && design.isActive) ||
      (statusFilter === 'inactive' && !design.isActive) ||
      (statusFilter === 'premium' && design.isPremium);
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const addDesign = () => {
    if (!newDesign.name || !newDesign.category || !newDesign.imageUrl) {
      toast.error('Veuillez remplir tous les champs obligatoires');
      return;
    }

    const design: Design = {
      id: Date.now().toString(),
      name: newDesign.name || '',
      category: newDesign.category as Design['category'] || 'abstract',
      tags: newDesign.tags || [],
      imageUrl: newDesign.imageUrl || '',
      thumbnailUrl: newDesign.thumbnailUrl,
      description: newDesign.description,
      isActive: true,
      isPremium: newDesign.isPremium || false,
      downloadCount: 0,
      rating: 0,
      phoneModels: newDesign.phoneModels || [],
      colors: newDesign.colors || [],
      fileSize: newDesign.fileSize || 2048,
      resolution: newDesign.resolution || '1080x1920',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'Admin'
    };

    setDesigns([...designs, design]);
    setNewDesign({});
    setIsAddDialogOpen(false);
    toast.success('Design ajouté avec succès');
  };

  const updateDesign = () => {
    if (!selectedDesign || !selectedDesign.name || !selectedDesign.imageUrl) {
      toast.error('Veuillez remplir tous les champs obligatoires');
      return;
    }

    setDesigns(designs.map(design => 
      design.id === selectedDesign.id 
        ? { ...selectedDesign, updatedAt: new Date().toISOString() }
        : design
    ));
    setIsEditDialogOpen(false);
    setSelectedDesign(null);
    toast.success('Design mis à jour avec succès');
  };

  const toggleDesignStatus = (designId: string) => {
    setDesigns(designs.map(design => 
      design.id === designId 
        ? { ...design, isActive: !design.isActive, updatedAt: new Date().toISOString() }
        : design
    ));
    toast.success('Statut du design mis à jour');
  };

  const deleteDesign = (designId: string) => {
    setDesigns(designs.filter(design => design.id !== designId));
    toast.success('Design supprimé');
  };

  const duplicateDesign = (design: Design) => {
    const duplicated: Design = {
      ...design,
      id: Date.now().toString(),
      name: `${design.name} (Copie)`,
      downloadCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setDesigns([...designs, duplicated]);
    toast.success('Design dupliqué');
  };

  const editDesign = (design: Design) => {
    setSelectedDesign({ ...design });
    setIsEditDialogOpen(true);
  };

  const previewDesign = (design: Design) => {
    setSelectedDesign(design);
    setIsPreviewDialogOpen(true);
  };

  const getCategoryLabel = (category: string) => {
    return categories.find(c => c.value === category)?.label || category;
  };

  const DesignForm = ({ design, setDesign, isEdit = false }: { 
    design: Partial<Design>, 
    setDesign: (design: Partial<Design>) => void,
    isEdit?: boolean 
  }) => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="name">Nom du design *</Label>
        <Input
          id="name"
          value={design.name || ''}
          onChange={(e) => setDesign({...design, name: e.target.value})}
          placeholder="Sunset Gradient"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="category">Catégorie *</Label>
        <Select value={design.category || ''} onValueChange={(value: Design['category']) => setDesign({...design, category: value})}>
          <SelectTrigger>
            <SelectValue placeholder="Sélectionner une catégorie" />
          </SelectTrigger>
          <SelectContent>
            {categories.map(category => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label htmlFor="imageUrl">URL de l'image *</Label>
        <Input
          id="imageUrl"
          value={design.imageUrl || ''}
          onChange={(e) => setDesign({...design, imageUrl: e.target.value})}
          placeholder="/images/designs/design.jpg"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="thumbnailUrl">URL de la miniature</Label>
        <Input
          id="thumbnailUrl"
          value={design.thumbnailUrl || ''}
          onChange={(e) => setDesign({...design, thumbnailUrl: e.target.value})}
          placeholder="/images/designs/thumbs/design.jpg"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="tags">Tags (séparés par des virgules)</Label>
        <Input
          id="tags"
          value={design.tags?.join(', ') || ''}
          onChange={(e) => setDesign({...design, tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)})}
          placeholder="gradient, sunset, orange"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="resolution">Résolution</Label>
        <Input
          id="resolution"
          value={design.resolution || ''}
          onChange={(e) => setDesign({...design, resolution: e.target.value})}
          placeholder="1080x1920"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="fileSize">Taille du fichier (KB)</Label>
        <Input
          id="fileSize"
          type="number"
          min="100"
          max="10000"
          value={design.fileSize || ''}
          onChange={(e) => setDesign({...design, fileSize: parseInt(e.target.value) || 0})}
          placeholder="2048"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="colors">Couleurs (codes hex séparés par des virgules)</Label>
        <Input
          id="colors"
          value={design.colors?.join(', ') || ''}
          onChange={(e) => setDesign({...design, colors: e.target.value.split(',').map(color => color.trim()).filter(Boolean)})}
          placeholder="#FF6B35, #F7931E, #FFD23F"
        />
      </div>
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Switch
            id="isPremium"
            checked={design.isPremium || false}
            onCheckedChange={(checked) => setDesign({...design, isPremium: checked})}
          />
          <Label htmlFor="isPremium">Design premium</Label>
        </div>
      </div>
      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={design.description || ''}
          onChange={(e) => setDesign({...design, description: e.target.value})}
          placeholder="Description du design..."
        />
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Designs</CardTitle>
            <Image className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{designs.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Designs Actifs</CardTitle>
            <Palette className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {designs.filter(d => d.isActive).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Designs Premium</CardTitle>
            <Palette className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {designs.filter(d => d.isPremium).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Téléchargements</CardTitle>
            <Download className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {designs.reduce((sum, d) => sum + d.downloadCount, 0).toLocaleString()}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtres et actions */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Rechercher un design..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filtrer par catégorie" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les catégories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filtrer par statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="active">Actifs</SelectItem>
                  <SelectItem value="inactive">Inactifs</SelectItem>
                  <SelectItem value="premium">Premium</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter un design
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-3xl">
                <DialogHeader>
                  <DialogTitle>Ajouter un nouveau design</DialogTitle>
                </DialogHeader>
                <DesignForm design={newDesign} setDesign={setNewDesign} />
                <div className="flex justify-end gap-2 mt-4">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Annuler
                  </Button>
                  <Button onClick={addDesign}>
                    Ajouter le design
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Design</TableHead>
                <TableHead>Catégorie</TableHead>
                <TableHead>Tags</TableHead>
                <TableHead>Téléchargements</TableHead>
                <TableHead>Note</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDesigns.map((design) => (
                <TableRow key={design.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                        {design.thumbnailUrl ? (
                          <img 
                            src={design.thumbnailUrl} 
                            alt={design.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Image className="h-6 w-6 text-gray-600" />
                        )}
                      </div>
                      <div>
                        <div className="font-medium">{design.name}</div>
                        <div className="text-sm text-gray-500">{design.resolution}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getCategoryLabel(design.category)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {design.tags.slice(0, 3).map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {design.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{design.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{design.downloadCount.toLocaleString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <span className="text-yellow-500">★</span>
                      <span>{design.rating.toFixed(1)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Badge className={design.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {design.isActive ? 'Actif' : 'Inactif'}
                      </Badge>
                      {design.isPremium && (
                        <Badge className="bg-purple-100 text-purple-800">
                          Premium
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => previewDesign(design)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => editDesign(design)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => duplicateDesign(design)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleDesignStatus(design.id)}
                        className={design.isActive ? 'text-orange-600' : 'text-green-600'}
                      >
                        {design.isActive ? 'Désactiver' : 'Activer'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteDesign(design.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Dialog d'édition */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Modifier le design</DialogTitle>
          </DialogHeader>
          {selectedDesign && (
            <DesignForm 
              design={selectedDesign} 
              setDesign={(design: Partial<Design>) => {
                setSelectedDesign(prev => prev ? { ...prev, ...design } : null);
              }} 
              isEdit={true}
            />
          )}
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Annuler
            </Button>
            <Button onClick={updateDesign}>
              Mettre à jour
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog de prévisualisation */}
      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Aperçu du design</DialogTitle>
          </DialogHeader>
          {selectedDesign && (
            <div className="space-y-4">
              <div className="flex justify-center">
                <div className="w-64 h-96 bg-gray-100 rounded-lg overflow-hidden">
                  <img 
                    src={selectedDesign.imageUrl} 
                    alt={selectedDesign.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold">{selectedDesign.name}</h3>
                  <p className="text-sm text-gray-600">{selectedDesign.description}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Catégorie:</span>
                    <span className="text-sm">{getCategoryLabel(selectedDesign.category)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Résolution:</span>
                    <span className="text-sm">{selectedDesign.resolution}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Taille:</span>
                    <span className="text-sm">{selectedDesign.fileSize} KB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Téléchargements:</span>
                    <span className="text-sm">{selectedDesign.downloadCount}</span>
                  </div>
                </div>
              </div>
              {selectedDesign.colors.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Couleurs:</h4>
                  <div className="flex gap-2">
                    {selectedDesign.colors.map((color, index) => (
                      <div 
                        key={index}
                        className={`w-8 h-8 rounded-full border-2 border-gray-200 ${styles.colorSwatch}`}
                        style={{
                          '--color-bg': color
                        } as React.CSSProperties & { '--color-bg': string }}
                        title={color}
                      />
                    ))}
                  </div>
                </div>
              )}
              {selectedDesign.tags.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Tags:</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedDesign.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}