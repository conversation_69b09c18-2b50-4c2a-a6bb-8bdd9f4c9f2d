import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card"
import { FileText, Edit, Trash2, Plus } from "lucide-react"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"

export default function DesignsPage() {
  // Données fictives pour les designs sauvegardés
  const designs = [
    {
      id: "design-1",
      name: "Mon design floral",
      date: "10/04/2025",
      image: "/images/placeholder.svg?v=1747269269089",
      phoneModel: "iPhone 15 Pro"
    },
    {
      id: "design-2",
      name: "Photo de vacances",
      date: "05/04/2025",
      image: "/images/placeholder.svg?v=1747269269089",
      phoneModel: "Samsung Galaxy S23"
    },
    {
      id: "design-3",
      name: "Design abstrait",
      date: "28/03/2025",
      image: "/images/placeholder.svg?v=1747269269089",
      phoneModel: "iPhone 14"
    }
  ]

  return (
    <div className="container mx-auto py-10 px-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Mes designs sauvegardés</h1>
        <Link href="/customize">
          <Button className="bg-purple-600 hover:bg-purple-700">
            <Plus className="h-4 w-4 mr-2" /> Nouveau design
          </Button>
        </Link>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {designs.map((design) => (
          <Card key={design.id} className="overflow-hidden hover:shadow-md transition-all">
            <CardContent className="p-0">
              <div className="relative aspect-[1/2] bg-gray-100">
                <Image 
                  src={design.image} 
                  alt={design.name} 
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <div className="p-4">
                <h3 className="font-semibold mb-1">{design.name}</h3>
                <div className="flex flex-col space-y-1 text-sm text-gray-500">
                  <p>Créé le {design.date}</p>
                  <p>Modèle: {design.phoneModel}</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 p-4 flex justify-between">
              <Link href={`/customize?design=${design.id}`}>
                <Button variant="outline" size="sm" className="flex items-center">
                  <Edit className="h-4 w-4 mr-2" /> Modifier
                </Button>
              </Link>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center text-red-500 hover:text-red-600">
                    <Trash2 className="h-4 w-4 mr-2" /> Supprimer
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Êtes-vous sûr ?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Cette action ne peut pas être annulée. Cela supprimera définitivement votre design "{design.name}".
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Annuler</AlertDialogCancel>
                    <AlertDialogAction className="bg-red-500 hover:bg-red-600">Supprimer</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardFooter>
          </Card>
        ))}
        
        {designs.length === 0 && (
          <div className="col-span-full text-center py-12 bg-gray-50 rounded-lg">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <h3 className="text-lg font-medium mb-1">Aucun design sauvegardé</h3>
            <p className="text-gray-500 mb-4">Vous n'avez pas encore sauvegardé de designs personnalisés.</p>
            <Link href="/customize">
              <Button className="bg-purple-600 hover:bg-purple-700">
                Créer mon premier design
              </Button>
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
