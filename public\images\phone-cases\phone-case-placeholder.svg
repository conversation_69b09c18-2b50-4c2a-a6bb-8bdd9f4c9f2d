<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="600" fill="#f9fafb"/>
  
  <!-- Phone case outline -->
  <rect x="100" y="100" width="200" height="400" fill="#ffffff" stroke="#e5e7eb" stroke-width="4" rx="30" ry="30"/>
  
  <!-- Phone screen -->
  <rect x="120" y="140" width="160" height="320" fill="#f3f4f6" rx="10" ry="10"/>
  
  <!-- Camera cutout -->
  <circle cx="200" cy="120" r="10" fill="#d1d5db"/>
  
  <!-- Button cutouts -->
  <rect x="90" y="200" width="10" height="40" fill="#d1d5db" rx="5" ry="5"/>
  <rect x="90" y="260" width="10" height="60" fill="#d1d5db" rx="5" ry="5"/>
  <rect x="300" y="200" width="10" height="40" fill="#d1d5db" rx="5" ry="5"/>
  
  <!-- Bottom cutout -->
  <rect x="160" y="480" width="80" height="10" fill="#d1d5db" rx="5" ry="5"/>
  
  <!-- Design pattern on case -->
  <g opacity="0.1">
    <circle cx="200" cy="250" r="60" fill="#6d28d9"/>
    <circle cx="160" cy="300" r="40" fill="#8b5cf6"/>
    <circle cx="240" cy="300" r="40" fill="#a78bfa"/>
    <circle cx="200" cy="350" r="60" fill="#c4b5fd"/>
  </g>
  
  <text x="200" y="530" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#6d28d9">Coque de téléphone</text>
  <text x="200" y="550" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#6b7280">Dimensions recommandées: 400 x 600 pixels</text>
</svg>
