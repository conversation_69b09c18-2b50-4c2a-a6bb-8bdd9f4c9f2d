/* For components/optimized-image.tsx and components/ui/adaptive-image.tsx */
.image-dimensions {
  width: var(--img-width, auto);
  height: var(--img-height, auto);
}

.image-dimensions[data-width][data-height] {
  width: attr(data-width px);
  height: attr(data-height px);
}

/* Base64 image specific styles */
.base64-image {
  width: var(--img-width, auto);
  height: var(--img-height, auto);
}

.base64-image[data-width][data-height] {
  width: attr(data-width px);
  height: attr(data-height px);
}

.image-error-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(243, 244, 246); /* Corresponds to bg-gray-100 */
  color: rgb(107, 114, 128); /* Corresponds to text-gray-500 */
  font-size: 0.875rem; /* Corresponds to text-sm */
}

.image-container {
  position: relative;
  overflow: hidden;
}

.image-loading {
  transform: scale(1.1);
  filter: blur(4px);
}

.image-loaded {
  transform: scale(1);
  filter: blur(0);
}

.image-transition {
  transition: all 300ms;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
